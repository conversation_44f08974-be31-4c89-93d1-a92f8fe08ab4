const __vite__fileDeps=["./dagre-2BBEFEWP.3MZedGQZ.js","./graph.DeWI_lNN.js","./_baseUniq.DRRijMhv.js","./layout.D6g9irlD.js","./_basePickBy.Cj58b6zd.js","./clone.NLf96KYX.js","./c4Diagram-AAMF2YG6.DrmRm8LB.js","./chunk-OMD6QJNC.BSOp5wGC.js","./select.BigU4G0v.js","./flowDiagram-THRYKUMA.D5X5he_2.js","./chunk-GLLZNHP4.ChZN4qP2.js","./chunk-WVR4S24B.CRJFvfQA.js","./chunk-NRVI72HA.C8EqmXsV.js","./channel.Co3GgaTo.js","./erDiagram-HZWUO2LU.BWBQDz7-.js","./gitGraphDiagram-OJR772UL.BZy94xP4.js","./chunk-ANTBXLJU.DolPU_xH.js","./chunk-FHKO5MBM.sCha0NZK.js","./mermaid-parser.core.CmoLm5-z.js","./preload-helper.D6kgxu3v.js","./ganttDiagram-WV7ZQ7D5.C2yIMhNo.js","./2.Badqb4fR.js","./stores.7EmYvcaB.js","./client.CHduoe6c.js","../assets/2.DFUZa1hM.css","./time.Be8aEIgQ.js","./step.5RfL8DsB.js","./linear.BQxp-PJv.js","./init.Dmth1JHB.js","./defaultLocale.CNpUPyHh.js","./infoDiagram-DDUCL6P7.DGHj4NNp.js","./pieDiagram-DBDJKBY4.-Uqa3ON4.js","./arc.D5BN-xhm.js","./ordinal.BJp8kCrd.js","./quadrantDiagram-YPSRARAO.BLBqfq2h.js","./xychartDiagram-FDP5SA34.DvZIcumM.js","./range.OtVwhkKS.js","./requirementDiagram-EGVEC5DT.Dq51eLEV.js","./sequenceDiagram-4MX5Z3NR.CsIWAQna.js","./classDiagram-3BZAVTQC.f1uA9w0N.js","./chunk-JBRWN2VN.B5J0xtMu.js","./classDiagram-v2-QTMF73CY.f1uA9w0N.js","./stateDiagram-UUKSUZ4H.Ba--bB5h.js","./chunk-LXBSTHXV.DgHB2RPA.js","./stateDiagram-v2-EYPG3UTE.CrCkSxPR.js","./journeyDiagram-FFXJYRFH.B_vaNNIM.js","./timeline-definition-3HZDQTIS.CjCLpVxn.js","./mindmap-definition-LNHGMQRG.DKpqsxIq.js","./cytoscape.esm.BlEmlE64.js","./kanban-definition-KOZQBZVT.DhBeYgt7.js","./sankeyDiagram-HRAUVNP4.CpXlZRsE.js","./diagram-GUPCWM2R.BdHaD9a5.js","./diagram-RP2FKANI.DGSAwjmZ.js","./blockDiagram-ZYB65J3Q.CUyZilll.js","./architectureDiagram-KFL7JDKH.Cg89XiJ9.js","./diagram-4IRLE6MV.BtZ0ObP7.js","./index.Bi7T_FA4.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
var Yf=Object.defineProperty;var Uf=(e,t,r)=>t in e?Yf(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var mt=(e,t,r)=>(Uf(e,typeof t!="symbol"?t+"":t,r),r);import{_ as gt}from"./preload-helper.D6kgxu3v.js";import{H as Gf,h as Vf}from"./2.Badqb4fR.js";import{d as Xf}from"./dispatch.kxCwF96_.js";import{T as Zf,N as Kf,q as Qf,r as Do,u as Ro,v as Jf,L as tg,K as eg,M as rg,k as Zi,S as ig,U as ag,V as ng,Y as sg,X as og,W as Zl,_ as lg,$ as cg,Z as Kl,O as $n,a0 as hg,a2 as Ql,a1 as Jl,a3 as tc,a4 as ec,a5 as rc,a6 as ic,l as ug}from"./step.5RfL8DsB.js";import{n as ac,m as dg,a as pg,b as fg,c as Da,d as hi,s as ht}from"./select.BigU4G0v.js";var nc={exports:{}};(function(e,t){(function(r,i){e.exports=i()})(Gf,function(){var r=1e3,i=6e4,a=36e5,n="millisecond",o="second",s="minute",c="hour",l="day",h="week",u="month",p="quarter",d="year",g="date",m="Invalid Date",y=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,x=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,b={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(L){var A=["th","st","nd","rd"],B=L%100;return"["+L+(A[(B-20)%10]||A[B]||A[0])+"]"}},k=function(L,A,B){var F=String(L);return!F||F.length>=A?L:""+Array(A+1-F.length).join(B)+L},T={s:k,z:function(L){var A=-L.utcOffset(),B=Math.abs(A),F=Math.floor(B/60),M=B%60;return(A<=0?"+":"-")+k(F,2,"0")+":"+k(M,2,"0")},m:function L(A,B){if(A.date()<B.date())return-L(B,A);var F=12*(B.year()-A.year())+(B.month()-A.month()),M=A.clone().add(F,u),W=B-M<0,V=A.clone().add(F+(W?-1:1),u);return+(-(F+(B-M)/(W?M-V:V-M))||0)},a:function(L){return L<0?Math.ceil(L)||0:Math.floor(L)},p:function(L){return{M:u,y:d,w:h,d:l,D:g,h:c,m:s,s:o,ms:n,Q:p}[L]||String(L||"").toLowerCase().replace(/s$/,"")},u:function(L){return L===void 0}},v="en",C={};C[v]=b;var S="$isDayjsObject",O=function(L){return L instanceof N||!(!L||!L[S])},P=function L(A,B,F){var M;if(!A)return v;if(typeof A=="string"){var W=A.toLowerCase();C[W]&&(M=W),B&&(C[W]=B,M=W);var V=A.split("-");if(!M&&V.length>1)return L(V[0])}else{var U=A.name;C[U]=A,M=U}return!F&&M&&(v=M),M||!F&&v},R=function(L,A){if(O(L))return L.clone();var B=typeof A=="object"?A:{};return B.date=L,B.args=arguments,new N(B)},E=T;E.l=P,E.i=O,E.w=function(L,A){return R(L,{locale:A.$L,utc:A.$u,x:A.$x,$offset:A.$offset})};var N=function(){function L(B){this.$L=P(B.locale,null,!0),this.parse(B),this.$x=this.$x||B.x||{},this[S]=!0}var A=L.prototype;return A.parse=function(B){this.$d=function(F){var M=F.date,W=F.utc;if(M===null)return new Date(NaN);if(E.u(M))return new Date;if(M instanceof Date)return new Date(M);if(typeof M=="string"&&!/Z$/i.test(M)){var V=M.match(y);if(V){var U=V[2]-1||0,ft=(V[7]||"0").substring(0,3);return W?new Date(Date.UTC(V[1],U,V[3]||1,V[4]||0,V[5]||0,V[6]||0,ft)):new Date(V[1],U,V[3]||1,V[4]||0,V[5]||0,V[6]||0,ft)}}return new Date(M)}(B),this.init()},A.init=function(){var B=this.$d;this.$y=B.getFullYear(),this.$M=B.getMonth(),this.$D=B.getDate(),this.$W=B.getDay(),this.$H=B.getHours(),this.$m=B.getMinutes(),this.$s=B.getSeconds(),this.$ms=B.getMilliseconds()},A.$utils=function(){return E},A.isValid=function(){return this.$d.toString()!==m},A.isSame=function(B,F){var M=R(B);return this.startOf(F)<=M&&M<=this.endOf(F)},A.isAfter=function(B,F){return R(B)<this.startOf(F)},A.isBefore=function(B,F){return this.endOf(F)<R(B)},A.$g=function(B,F,M){return E.u(B)?this[F]:this.set(M,B)},A.unix=function(){return Math.floor(this.valueOf()/1e3)},A.valueOf=function(){return this.$d.getTime()},A.startOf=function(B,F){var M=this,W=!!E.u(F)||F,V=E.p(B),U=function(yt,xt){var Tt=E.w(M.$u?Date.UTC(M.$y,xt,yt):new Date(M.$y,xt,yt),M);return W?Tt:Tt.endOf(l)},ft=function(yt,xt){return E.w(M.toDate()[yt].apply(M.toDate("s"),(W?[0,0,0,0]:[23,59,59,999]).slice(xt)),M)},nt=this.$W,vt=this.$M,st=this.$D,at="set"+(this.$u?"UTC":"");switch(V){case d:return W?U(1,0):U(31,11);case u:return W?U(1,vt):U(0,vt+1);case h:var ct=this.$locale().weekStart||0,wt=(nt<ct?nt+7:nt)-ct;return U(W?st-wt:st+(6-wt),vt);case l:case g:return ft(at+"Hours",0);case c:return ft(at+"Minutes",1);case s:return ft(at+"Seconds",2);case o:return ft(at+"Milliseconds",3);default:return this.clone()}},A.endOf=function(B){return this.startOf(B,!1)},A.$set=function(B,F){var M,W=E.p(B),V="set"+(this.$u?"UTC":""),U=(M={},M[l]=V+"Date",M[g]=V+"Date",M[u]=V+"Month",M[d]=V+"FullYear",M[c]=V+"Hours",M[s]=V+"Minutes",M[o]=V+"Seconds",M[n]=V+"Milliseconds",M)[W],ft=W===l?this.$D+(F-this.$W):F;if(W===u||W===d){var nt=this.clone().set(g,1);nt.$d[U](ft),nt.init(),this.$d=nt.set(g,Math.min(this.$D,nt.daysInMonth())).$d}else U&&this.$d[U](ft);return this.init(),this},A.set=function(B,F){return this.clone().$set(B,F)},A.get=function(B){return this[E.p(B)]()},A.add=function(B,F){var M,W=this;B=Number(B);var V=E.p(F),U=function(vt){var st=R(W);return E.w(st.date(st.date()+Math.round(vt*B)),W)};if(V===u)return this.set(u,this.$M+B);if(V===d)return this.set(d,this.$y+B);if(V===l)return U(1);if(V===h)return U(7);var ft=(M={},M[s]=i,M[c]=a,M[o]=r,M)[V]||1,nt=this.$d.getTime()+B*ft;return E.w(nt,this)},A.subtract=function(B,F){return this.add(-1*B,F)},A.format=function(B){var F=this,M=this.$locale();if(!this.isValid())return M.invalidDate||m;var W=B||"YYYY-MM-DDTHH:mm:ssZ",V=E.z(this),U=this.$H,ft=this.$m,nt=this.$M,vt=M.weekdays,st=M.months,at=M.meridiem,ct=function(xt,Tt,qt,pe){return xt&&(xt[Tt]||xt(F,W))||qt[Tt].slice(0,pe)},wt=function(xt){return E.s(U%12||12,xt,"0")},yt=at||function(xt,Tt,qt){var pe=xt<12?"AM":"PM";return qt?pe.toLowerCase():pe};return W.replace(x,function(xt,Tt){return Tt||function(qt){switch(qt){case"YY":return String(F.$y).slice(-2);case"YYYY":return E.s(F.$y,4,"0");case"M":return nt+1;case"MM":return E.s(nt+1,2,"0");case"MMM":return ct(M.monthsShort,nt,st,3);case"MMMM":return ct(st,nt);case"D":return F.$D;case"DD":return E.s(F.$D,2,"0");case"d":return String(F.$W);case"dd":return ct(M.weekdaysMin,F.$W,vt,2);case"ddd":return ct(M.weekdaysShort,F.$W,vt,3);case"dddd":return vt[F.$W];case"H":return String(U);case"HH":return E.s(U,2,"0");case"h":return wt(1);case"hh":return wt(2);case"a":return yt(U,ft,!0);case"A":return yt(U,ft,!1);case"m":return String(ft);case"mm":return E.s(ft,2,"0");case"s":return String(F.$s);case"ss":return E.s(F.$s,2,"0");case"SSS":return E.s(F.$ms,3,"0");case"Z":return V}return null}(xt)||V.replace(":","")})},A.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},A.diff=function(B,F,M){var W,V=this,U=E.p(F),ft=R(B),nt=(ft.utcOffset()-this.utcOffset())*i,vt=this-ft,st=function(){return E.m(V,ft)};switch(U){case d:W=st()/12;break;case u:W=st();break;case p:W=st()/3;break;case h:W=(vt-nt)/6048e5;break;case l:W=(vt-nt)/864e5;break;case c:W=vt/a;break;case s:W=vt/i;break;case o:W=vt/r;break;default:W=vt}return M?W:E.a(W)},A.daysInMonth=function(){return this.endOf(u).$D},A.$locale=function(){return C[this.$L]},A.locale=function(B,F){if(!B)return this.$L;var M=this.clone(),W=P(B,F,!0);return W&&(M.$L=W),M},A.clone=function(){return E.w(this.$d,this)},A.toDate=function(){return new Date(this.valueOf())},A.toJSON=function(){return this.isValid()?this.toISOString():null},A.toISOString=function(){return this.$d.toISOString()},A.toString=function(){return this.$d.toUTCString()},L}(),D=N.prototype;return R.prototype=D,[["$ms",n],["$s",o],["$m",s],["$H",c],["$W",l],["$M",u],["$y",d],["$D",g]].forEach(function(L){D[L[1]]=function(A){return this.$g(A,L[0],L[1])}}),R.extend=function(L,A){return L.$i||(L(A,N,R),L.$i=!0),R},R.locale=P,R.isDayjs=O,R.unix=function(L){return R(1e3*L)},R.en=C[v],R.Ls=C,R.p={},R})})(nc);var gg=nc.exports;const mg=Vf(gg),Ki={min:{r:0,g:0,b:0,s:0,l:0,a:0},max:{r:255,g:255,b:255,h:360,s:100,l:100,a:1},clamp:{r:e=>e>=255?255:e<0?0:e,g:e=>e>=255?255:e<0?0:e,b:e=>e>=255?255:e<0?0:e,h:e=>e%360,s:e=>e>=100?100:e<0?0:e,l:e=>e>=100?100:e<0?0:e,a:e=>e>=1?1:e<0?0:e},toLinear:e=>{const t=e/255;return e>.03928?Math.pow((t+.055)/1.055,2.4):t/12.92},hue2rgb:(e,t,r)=>(r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(t-e)*6*r:r<1/2?t:r<2/3?e+(t-e)*(2/3-r)*6:e),hsl2rgb:({h:e,s:t,l:r},i)=>{if(!t)return r*2.55;e/=360,t/=100,r/=100;const a=r<.5?r*(1+t):r+t-r*t,n=2*r-a;switch(i){case"r":return Ki.hue2rgb(n,a,e+1/3)*255;case"g":return Ki.hue2rgb(n,a,e)*255;case"b":return Ki.hue2rgb(n,a,e-1/3)*255}},rgb2hsl:({r:e,g:t,b:r},i)=>{e/=255,t/=255,r/=255;const a=Math.max(e,t,r),n=Math.min(e,t,r),o=(a+n)/2;if(i==="l")return o*100;if(a===n)return 0;const s=a-n,c=o>.5?s/(2-a-n):s/(a+n);if(i==="s")return c*100;switch(a){case e:return((t-r)/s+(t<r?6:0))*60;case t:return((r-e)/s+2)*60;case r:return((e-t)/s+4)*60;default:return-1}}},yg={clamp:(e,t,r)=>t>r?Math.min(t,Math.max(r,e)):Math.min(r,Math.max(t,e)),round:e=>Math.round(e*1e10)/1e10},xg={dec2hex:e=>{const t=Math.round(e).toString(16);return t.length>1?t:`0${t}`}},it={channel:Ki,lang:yg,unit:xg},Pe={};for(let e=0;e<=255;e++)Pe[e]=it.unit.dec2hex(e);const It={ALL:0,RGB:1,HSL:2};class bg{constructor(){this.type=It.ALL}get(){return this.type}set(t){if(this.type&&this.type!==t)throw new Error("Cannot change both RGB and HSL channels at the same time");this.type=t}reset(){this.type=It.ALL}is(t){return this.type===t}}class Cg{constructor(t,r){this.color=r,this.changed=!1,this.data=t,this.type=new bg}set(t,r){return this.color=r,this.changed=!1,this.data=t,this.type.type=It.ALL,this}_ensureHSL(){const t=this.data,{h:r,s:i,l:a}=t;r===void 0&&(t.h=it.channel.rgb2hsl(t,"h")),i===void 0&&(t.s=it.channel.rgb2hsl(t,"s")),a===void 0&&(t.l=it.channel.rgb2hsl(t,"l"))}_ensureRGB(){const t=this.data,{r,g:i,b:a}=t;r===void 0&&(t.r=it.channel.hsl2rgb(t,"r")),i===void 0&&(t.g=it.channel.hsl2rgb(t,"g")),a===void 0&&(t.b=it.channel.hsl2rgb(t,"b"))}get r(){const t=this.data,r=t.r;return!this.type.is(It.HSL)&&r!==void 0?r:(this._ensureHSL(),it.channel.hsl2rgb(t,"r"))}get g(){const t=this.data,r=t.g;return!this.type.is(It.HSL)&&r!==void 0?r:(this._ensureHSL(),it.channel.hsl2rgb(t,"g"))}get b(){const t=this.data,r=t.b;return!this.type.is(It.HSL)&&r!==void 0?r:(this._ensureHSL(),it.channel.hsl2rgb(t,"b"))}get h(){const t=this.data,r=t.h;return!this.type.is(It.RGB)&&r!==void 0?r:(this._ensureRGB(),it.channel.rgb2hsl(t,"h"))}get s(){const t=this.data,r=t.s;return!this.type.is(It.RGB)&&r!==void 0?r:(this._ensureRGB(),it.channel.rgb2hsl(t,"s"))}get l(){const t=this.data,r=t.l;return!this.type.is(It.RGB)&&r!==void 0?r:(this._ensureRGB(),it.channel.rgb2hsl(t,"l"))}get a(){return this.data.a}set r(t){this.type.set(It.RGB),this.changed=!0,this.data.r=t}set g(t){this.type.set(It.RGB),this.changed=!0,this.data.g=t}set b(t){this.type.set(It.RGB),this.changed=!0,this.data.b=t}set h(t){this.type.set(It.HSL),this.changed=!0,this.data.h=t}set s(t){this.type.set(It.HSL),this.changed=!0,this.data.s=t}set l(t){this.type.set(It.HSL),this.changed=!0,this.data.l=t}set a(t){this.changed=!0,this.data.a=t}}const Ra=new Cg({r:0,g:0,b:0,a:0},"transparent"),kr={re:/^#((?:[a-f0-9]{2}){2,4}|[a-f0-9]{3})$/i,parse:e=>{if(e.charCodeAt(0)!==35)return;const t=e.match(kr.re);if(!t)return;const r=t[1],i=parseInt(r,16),a=r.length,n=a%4===0,o=a>4,s=o?1:17,c=o?8:4,l=n?0:-1,h=o?255:15;return Ra.set({r:(i>>c*(l+3)&h)*s,g:(i>>c*(l+2)&h)*s,b:(i>>c*(l+1)&h)*s,a:n?(i&h)*s/255:1},e)},stringify:e=>{const{r:t,g:r,b:i,a}=e;return a<1?`#${Pe[Math.round(t)]}${Pe[Math.round(r)]}${Pe[Math.round(i)]}${Pe[Math.round(a*255)]}`:`#${Pe[Math.round(t)]}${Pe[Math.round(r)]}${Pe[Math.round(i)]}`}},Ke={re:/^hsla?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(?:deg|grad|rad|turn)?)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(%)?))?\s*?\)$/i,hueRe:/^(.+?)(deg|grad|rad|turn)$/i,_hue2deg:e=>{const t=e.match(Ke.hueRe);if(t){const[,r,i]=t;switch(i){case"grad":return it.channel.clamp.h(parseFloat(r)*.9);case"rad":return it.channel.clamp.h(parseFloat(r)*180/Math.PI);case"turn":return it.channel.clamp.h(parseFloat(r)*360)}}return it.channel.clamp.h(parseFloat(e))},parse:e=>{const t=e.charCodeAt(0);if(t!==104&&t!==72)return;const r=e.match(Ke.re);if(!r)return;const[,i,a,n,o,s]=r;return Ra.set({h:Ke._hue2deg(i),s:it.channel.clamp.s(parseFloat(a)),l:it.channel.clamp.l(parseFloat(n)),a:o?it.channel.clamp.a(s?parseFloat(o)/100:parseFloat(o)):1},e)},stringify:e=>{const{h:t,s:r,l:i,a}=e;return a<1?`hsla(${it.lang.round(t)}, ${it.lang.round(r)}%, ${it.lang.round(i)}%, ${a})`:`hsl(${it.lang.round(t)}, ${it.lang.round(r)}%, ${it.lang.round(i)}%)`}},ai={colors:{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyanaqua:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",transparent:"#00000000",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},parse:e=>{e=e.toLowerCase();const t=ai.colors[e];if(t)return kr.parse(t)},stringify:e=>{const t=kr.stringify(e);for(const r in ai.colors)if(ai.colors[r]===t)return r}},Qr={re:/^rgba?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?)))?\s*?\)$/i,parse:e=>{const t=e.charCodeAt(0);if(t!==114&&t!==82)return;const r=e.match(Qr.re);if(!r)return;const[,i,a,n,o,s,c,l,h]=r;return Ra.set({r:it.channel.clamp.r(a?parseFloat(i)*2.55:parseFloat(i)),g:it.channel.clamp.g(o?parseFloat(n)*2.55:parseFloat(n)),b:it.channel.clamp.b(c?parseFloat(s)*2.55:parseFloat(s)),a:l?it.channel.clamp.a(h?parseFloat(l)/100:parseFloat(l)):1},e)},stringify:e=>{const{r:t,g:r,b:i,a}=e;return a<1?`rgba(${it.lang.round(t)}, ${it.lang.round(r)}, ${it.lang.round(i)}, ${it.lang.round(a)})`:`rgb(${it.lang.round(t)}, ${it.lang.round(r)}, ${it.lang.round(i)})`}},be={format:{keyword:ai,hex:kr,rgb:Qr,rgba:Qr,hsl:Ke,hsla:Ke},parse:e=>{if(typeof e!="string")return e;const t=kr.parse(e)||Qr.parse(e)||Ke.parse(e)||ai.parse(e);if(t)return t;throw new Error(`Unsupported color format: "${e}"`)},stringify:e=>!e.changed&&e.color?e.color:e.type.is(It.HSL)||e.data.r===void 0?Ke.stringify(e):e.a<1||!Number.isInteger(e.r)||!Number.isInteger(e.g)||!Number.isInteger(e.b)?Qr.stringify(e):kr.stringify(e)},sc=(e,t)=>{const r=be.parse(e);for(const i in t)r[i]=it.channel.clamp[i](t[i]);return be.stringify(r)},ni=(e,t,r=0,i=1)=>{if(typeof e!="number")return sc(e,{a:t});const a=Ra.set({r:it.channel.clamp.r(e),g:it.channel.clamp.g(t),b:it.channel.clamp.b(r),a:it.channel.clamp.a(i)});return be.stringify(a)},kg=e=>{const{r:t,g:r,b:i}=be.parse(e),a=.2126*it.channel.toLinear(t)+.7152*it.channel.toLinear(r)+.0722*it.channel.toLinear(i);return it.lang.round(a)},wg=e=>kg(e)>=.5,Li=e=>!wg(e),oc=(e,t,r)=>{const i=be.parse(e),a=i[t],n=it.channel.clamp[t](a+r);return a!==n&&(i[t]=n),be.stringify(i)},q=(e,t)=>oc(e,"l",t),J=(e,t)=>oc(e,"l",-t),_=(e,t)=>{const r=be.parse(e),i={};for(const a in t)t[a]&&(i[a]=r[a]+t[a]);return sc(e,i)},vg=(e,t,r=50)=>{const{r:i,g:a,b:n,a:o}=be.parse(e),{r:s,g:c,b:l,a:h}=be.parse(t),u=r/100,p=u*2-1,d=o-h,m=((p*d===-1?p:(p+d)/(1+p*d))+1)/2,y=1-m,x=i*m+s*y,b=a*m+c*y,k=n*m+l*y,T=o*u+h*(1-u);return ni(x,b,k,T)},z=(e,t=100)=>{const r=be.parse(e);return r.r=255-r.r,r.g=255-r.g,r.b=255-r.b,vg(r,e,t)};/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:lc,setPrototypeOf:Io,isFrozen:Sg,getPrototypeOf:Tg,getOwnPropertyDescriptor:_g}=Object;let{freeze:Gt,seal:ae,create:cc}=Object,{apply:On,construct:Dn}=typeof Reflect<"u"&&Reflect;Gt||(Gt=function(t){return t});ae||(ae=function(t){return t});On||(On=function(t,r,i){return t.apply(r,i)});Dn||(Dn=function(t,r){return new t(...r)});const qi=Vt(Array.prototype.forEach),Bg=Vt(Array.prototype.lastIndexOf),Po=Vt(Array.prototype.pop),Wr=Vt(Array.prototype.push),Lg=Vt(Array.prototype.splice),Qi=Vt(String.prototype.toLowerCase),gn=Vt(String.prototype.toString),No=Vt(String.prototype.match),qr=Vt(String.prototype.replace),Ag=Vt(String.prototype.indexOf),Mg=Vt(String.prototype.trim),le=Vt(Object.prototype.hasOwnProperty),Ht=Vt(RegExp.prototype.test),Hr=Eg(TypeError);function Vt(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];return On(e,t,i)}}function Eg(e){return function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];return Dn(e,r)}}function ot(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Qi;Io&&Io(e,null);let i=t.length;for(;i--;){let a=t[i];if(typeof a=="string"){const n=r(a);n!==a&&(Sg(t)||(t[i]=n),a=n)}e[a]=!0}return e}function Fg(e){for(let t=0;t<e.length;t++)le(e,t)||(e[t]=null);return e}function Le(e){const t=cc(null);for(const[r,i]of lc(e))le(e,r)&&(Array.isArray(i)?t[r]=Fg(i):i&&typeof i=="object"&&i.constructor===Object?t[r]=Le(i):t[r]=i);return t}function jr(e,t){for(;e!==null;){const i=_g(e,t);if(i){if(i.get)return Vt(i.get);if(typeof i.value=="function")return Vt(i.value)}e=Tg(e)}function r(){return null}return r}const zo=Gt(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),mn=Gt(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),yn=Gt(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),$g=Gt(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),xn=Gt(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Og=Gt(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Wo=Gt(["#text"]),qo=Gt(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),bn=Gt(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Ho=Gt(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Hi=Gt(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Dg=ae(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Rg=ae(/<%[\w\W]*|[\w\W]*%>/gm),Ig=ae(/\$\{[\w\W]*/gm),Pg=ae(/^data-[\-\w.\u00B7-\uFFFF]+$/),Ng=ae(/^aria-[\-\w]+$/),hc=ae(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),zg=ae(/^(?:\w+script|data):/i),Wg=ae(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),uc=ae(/^html$/i),qg=ae(/^[a-z][.\w]*(-[.\w]+)+$/i);var jo=Object.freeze({__proto__:null,ARIA_ATTR:Ng,ATTR_WHITESPACE:Wg,CUSTOM_ELEMENT:qg,DATA_ATTR:Pg,DOCTYPE_NAME:uc,ERB_EXPR:Rg,IS_ALLOWED_URI:hc,IS_SCRIPT_OR_DATA:zg,MUSTACHE_EXPR:Dg,TMPLIT_EXPR:Ig});const Yr={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},Hg=function(){return typeof window>"u"?null:window},jg=function(t,r){if(typeof t!="object"||typeof t.createPolicy!="function")return null;let i=null;const a="data-tt-policy-suffix";r&&r.hasAttribute(a)&&(i=r.getAttribute(a));const n="dompurify"+(i?"#"+i:"");try{return t.createPolicy(n,{createHTML(o){return o},createScriptURL(o){return o}})}catch{return console.warn("TrustedTypes policy "+n+" could not be created."),null}},Yo=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function dc(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Hg();const t=Q=>dc(Q);if(t.version="3.2.6",t.removed=[],!e||!e.document||e.document.nodeType!==Yr.document||!e.Element)return t.isSupported=!1,t;let{document:r}=e;const i=r,a=i.currentScript,{DocumentFragment:n,HTMLTemplateElement:o,Node:s,Element:c,NodeFilter:l,NamedNodeMap:h=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:u,DOMParser:p,trustedTypes:d}=e,g=c.prototype,m=jr(g,"cloneNode"),y=jr(g,"remove"),x=jr(g,"nextSibling"),b=jr(g,"childNodes"),k=jr(g,"parentNode");if(typeof o=="function"){const Q=r.createElement("template");Q.content&&Q.content.ownerDocument&&(r=Q.content.ownerDocument)}let T,v="";const{implementation:C,createNodeIterator:S,createDocumentFragment:O,getElementsByTagName:P}=r,{importNode:R}=i;let E=Yo();t.isSupported=typeof lc=="function"&&typeof k=="function"&&C&&C.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:N,ERB_EXPR:D,TMPLIT_EXPR:L,DATA_ATTR:A,ARIA_ATTR:B,IS_SCRIPT_OR_DATA:F,ATTR_WHITESPACE:M,CUSTOM_ELEMENT:W}=jo;let{IS_ALLOWED_URI:V}=jo,U=null;const ft=ot({},[...zo,...mn,...yn,...xn,...Wo]);let nt=null;const vt=ot({},[...qo,...bn,...Ho,...Hi]);let st=Object.seal(cc(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),at=null,ct=null,wt=!0,yt=!0,xt=!1,Tt=!0,qt=!1,pe=!0,oe=!1,sn=!1,on=!1,ur=!1,Ri=!1,Ii=!1,yo=!0,xo=!1;const If="user-content-";let ln=!0,Pr=!1,dr={},pr=null;const bo=ot({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Co=null;const ko=ot({},["audio","video","img","source","image","track"]);let cn=null;const wo=ot({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Pi="http://www.w3.org/1998/Math/MathML",Ni="http://www.w3.org/2000/svg",ve="http://www.w3.org/1999/xhtml";let fr=ve,hn=!1,un=null;const Pf=ot({},[Pi,Ni,ve],gn);let zi=ot({},["mi","mo","mn","ms","mtext"]),Wi=ot({},["annotation-xml"]);const Nf=ot({},["title","style","font","a","script"]);let Nr=null;const zf=["application/xhtml+xml","text/html"],Wf="text/html";let Mt=null,gr=null;const qf=r.createElement("form"),vo=function(w){return w instanceof RegExp||w instanceof Function},dn=function(){let w=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(gr&&gr===w)){if((!w||typeof w!="object")&&(w={}),w=Le(w),Nr=zf.indexOf(w.PARSER_MEDIA_TYPE)===-1?Wf:w.PARSER_MEDIA_TYPE,Mt=Nr==="application/xhtml+xml"?gn:Qi,U=le(w,"ALLOWED_TAGS")?ot({},w.ALLOWED_TAGS,Mt):ft,nt=le(w,"ALLOWED_ATTR")?ot({},w.ALLOWED_ATTR,Mt):vt,un=le(w,"ALLOWED_NAMESPACES")?ot({},w.ALLOWED_NAMESPACES,gn):Pf,cn=le(w,"ADD_URI_SAFE_ATTR")?ot(Le(wo),w.ADD_URI_SAFE_ATTR,Mt):wo,Co=le(w,"ADD_DATA_URI_TAGS")?ot(Le(ko),w.ADD_DATA_URI_TAGS,Mt):ko,pr=le(w,"FORBID_CONTENTS")?ot({},w.FORBID_CONTENTS,Mt):bo,at=le(w,"FORBID_TAGS")?ot({},w.FORBID_TAGS,Mt):Le({}),ct=le(w,"FORBID_ATTR")?ot({},w.FORBID_ATTR,Mt):Le({}),dr=le(w,"USE_PROFILES")?w.USE_PROFILES:!1,wt=w.ALLOW_ARIA_ATTR!==!1,yt=w.ALLOW_DATA_ATTR!==!1,xt=w.ALLOW_UNKNOWN_PROTOCOLS||!1,Tt=w.ALLOW_SELF_CLOSE_IN_ATTR!==!1,qt=w.SAFE_FOR_TEMPLATES||!1,pe=w.SAFE_FOR_XML!==!1,oe=w.WHOLE_DOCUMENT||!1,ur=w.RETURN_DOM||!1,Ri=w.RETURN_DOM_FRAGMENT||!1,Ii=w.RETURN_TRUSTED_TYPE||!1,on=w.FORCE_BODY||!1,yo=w.SANITIZE_DOM!==!1,xo=w.SANITIZE_NAMED_PROPS||!1,ln=w.KEEP_CONTENT!==!1,Pr=w.IN_PLACE||!1,V=w.ALLOWED_URI_REGEXP||hc,fr=w.NAMESPACE||ve,zi=w.MATHML_TEXT_INTEGRATION_POINTS||zi,Wi=w.HTML_INTEGRATION_POINTS||Wi,st=w.CUSTOM_ELEMENT_HANDLING||{},w.CUSTOM_ELEMENT_HANDLING&&vo(w.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(st.tagNameCheck=w.CUSTOM_ELEMENT_HANDLING.tagNameCheck),w.CUSTOM_ELEMENT_HANDLING&&vo(w.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(st.attributeNameCheck=w.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),w.CUSTOM_ELEMENT_HANDLING&&typeof w.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(st.allowCustomizedBuiltInElements=w.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),qt&&(yt=!1),Ri&&(ur=!0),dr&&(U=ot({},Wo),nt=[],dr.html===!0&&(ot(U,zo),ot(nt,qo)),dr.svg===!0&&(ot(U,mn),ot(nt,bn),ot(nt,Hi)),dr.svgFilters===!0&&(ot(U,yn),ot(nt,bn),ot(nt,Hi)),dr.mathMl===!0&&(ot(U,xn),ot(nt,Ho),ot(nt,Hi))),w.ADD_TAGS&&(U===ft&&(U=Le(U)),ot(U,w.ADD_TAGS,Mt)),w.ADD_ATTR&&(nt===vt&&(nt=Le(nt)),ot(nt,w.ADD_ATTR,Mt)),w.ADD_URI_SAFE_ATTR&&ot(cn,w.ADD_URI_SAFE_ATTR,Mt),w.FORBID_CONTENTS&&(pr===bo&&(pr=Le(pr)),ot(pr,w.FORBID_CONTENTS,Mt)),ln&&(U["#text"]=!0),oe&&ot(U,["html","head","body"]),U.table&&(ot(U,["tbody"]),delete at.tbody),w.TRUSTED_TYPES_POLICY){if(typeof w.TRUSTED_TYPES_POLICY.createHTML!="function")throw Hr('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof w.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Hr('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');T=w.TRUSTED_TYPES_POLICY,v=T.createHTML("")}else T===void 0&&(T=jg(d,a)),T!==null&&typeof v=="string"&&(v=T.createHTML(""));Gt&&Gt(w),gr=w}},So=ot({},[...mn,...yn,...$g]),To=ot({},[...xn,...Og]),Hf=function(w){let I=k(w);(!I||!I.tagName)&&(I={namespaceURI:fr,tagName:"template"});const G=Qi(w.tagName),Ct=Qi(I.tagName);return un[w.namespaceURI]?w.namespaceURI===Ni?I.namespaceURI===ve?G==="svg":I.namespaceURI===Pi?G==="svg"&&(Ct==="annotation-xml"||zi[Ct]):!!So[G]:w.namespaceURI===Pi?I.namespaceURI===ve?G==="math":I.namespaceURI===Ni?G==="math"&&Wi[Ct]:!!To[G]:w.namespaceURI===ve?I.namespaceURI===Ni&&!Wi[Ct]||I.namespaceURI===Pi&&!zi[Ct]?!1:!To[G]&&(Nf[G]||!So[G]):!!(Nr==="application/xhtml+xml"&&un[w.namespaceURI]):!1},fe=function(w){Wr(t.removed,{element:w});try{k(w).removeChild(w)}catch{y(w)}},mr=function(w,I){try{Wr(t.removed,{attribute:I.getAttributeNode(w),from:I})}catch{Wr(t.removed,{attribute:null,from:I})}if(I.removeAttribute(w),w==="is")if(ur||Ri)try{fe(I)}catch{}else try{I.setAttribute(w,"")}catch{}},_o=function(w){let I=null,G=null;if(on)w="<remove></remove>"+w;else{const Bt=No(w,/^[\r\n\t ]+/);G=Bt&&Bt[0]}Nr==="application/xhtml+xml"&&fr===ve&&(w='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+w+"</body></html>");const Ct=T?T.createHTML(w):w;if(fr===ve)try{I=new p().parseFromString(Ct,Nr)}catch{}if(!I||!I.documentElement){I=C.createDocument(fr,"template",null);try{I.documentElement.innerHTML=hn?v:Ct}catch{}}const Dt=I.body||I.documentElement;return w&&G&&Dt.insertBefore(r.createTextNode(G),Dt.childNodes[0]||null),fr===ve?P.call(I,oe?"html":"body")[0]:oe?I.documentElement:Dt},Bo=function(w){return S.call(w.ownerDocument||w,w,l.SHOW_ELEMENT|l.SHOW_COMMENT|l.SHOW_TEXT|l.SHOW_PROCESSING_INSTRUCTION|l.SHOW_CDATA_SECTION,null)},pn=function(w){return w instanceof u&&(typeof w.nodeName!="string"||typeof w.textContent!="string"||typeof w.removeChild!="function"||!(w.attributes instanceof h)||typeof w.removeAttribute!="function"||typeof w.setAttribute!="function"||typeof w.namespaceURI!="string"||typeof w.insertBefore!="function"||typeof w.hasChildNodes!="function")},Lo=function(w){return typeof s=="function"&&w instanceof s};function Se(Q,w,I){qi(Q,G=>{G.call(t,w,I,gr)})}const Ao=function(w){let I=null;if(Se(E.beforeSanitizeElements,w,null),pn(w))return fe(w),!0;const G=Mt(w.nodeName);if(Se(E.uponSanitizeElement,w,{tagName:G,allowedTags:U}),pe&&w.hasChildNodes()&&!Lo(w.firstElementChild)&&Ht(/<[/\w!]/g,w.innerHTML)&&Ht(/<[/\w!]/g,w.textContent)||w.nodeType===Yr.progressingInstruction||pe&&w.nodeType===Yr.comment&&Ht(/<[/\w]/g,w.data))return fe(w),!0;if(!U[G]||at[G]){if(!at[G]&&Eo(G)&&(st.tagNameCheck instanceof RegExp&&Ht(st.tagNameCheck,G)||st.tagNameCheck instanceof Function&&st.tagNameCheck(G)))return!1;if(ln&&!pr[G]){const Ct=k(w)||w.parentNode,Dt=b(w)||w.childNodes;if(Dt&&Ct){const Bt=Dt.length;for(let Xt=Bt-1;Xt>=0;--Xt){const Te=m(Dt[Xt],!0);Te.__removalCount=(w.__removalCount||0)+1,Ct.insertBefore(Te,x(w))}}}return fe(w),!0}return w instanceof c&&!Hf(w)||(G==="noscript"||G==="noembed"||G==="noframes")&&Ht(/<\/no(script|embed|frames)/i,w.innerHTML)?(fe(w),!0):(qt&&w.nodeType===Yr.text&&(I=w.textContent,qi([N,D,L],Ct=>{I=qr(I,Ct," ")}),w.textContent!==I&&(Wr(t.removed,{element:w.cloneNode()}),w.textContent=I)),Se(E.afterSanitizeElements,w,null),!1)},Mo=function(w,I,G){if(yo&&(I==="id"||I==="name")&&(G in r||G in qf))return!1;if(!(yt&&!ct[I]&&Ht(A,I))){if(!(wt&&Ht(B,I))){if(!nt[I]||ct[I]){if(!(Eo(w)&&(st.tagNameCheck instanceof RegExp&&Ht(st.tagNameCheck,w)||st.tagNameCheck instanceof Function&&st.tagNameCheck(w))&&(st.attributeNameCheck instanceof RegExp&&Ht(st.attributeNameCheck,I)||st.attributeNameCheck instanceof Function&&st.attributeNameCheck(I))||I==="is"&&st.allowCustomizedBuiltInElements&&(st.tagNameCheck instanceof RegExp&&Ht(st.tagNameCheck,G)||st.tagNameCheck instanceof Function&&st.tagNameCheck(G))))return!1}else if(!cn[I]){if(!Ht(V,qr(G,M,""))){if(!((I==="src"||I==="xlink:href"||I==="href")&&w!=="script"&&Ag(G,"data:")===0&&Co[w])){if(!(xt&&!Ht(F,qr(G,M,"")))){if(G)return!1}}}}}}return!0},Eo=function(w){return w!=="annotation-xml"&&No(w,W)},Fo=function(w){Se(E.beforeSanitizeAttributes,w,null);const{attributes:I}=w;if(!I||pn(w))return;const G={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:nt,forceKeepAttr:void 0};let Ct=I.length;for(;Ct--;){const Dt=I[Ct],{name:Bt,namespaceURI:Xt,value:Te}=Dt,zr=Mt(Bt),fn=Te;let Rt=Bt==="value"?fn:Mg(fn);if(G.attrName=zr,G.attrValue=Rt,G.keepAttr=!0,G.forceKeepAttr=void 0,Se(E.uponSanitizeAttribute,w,G),Rt=G.attrValue,xo&&(zr==="id"||zr==="name")&&(mr(Bt,w),Rt=If+Rt),pe&&Ht(/((--!?|])>)|<\/(style|title)/i,Rt)){mr(Bt,w);continue}if(G.forceKeepAttr)continue;if(!G.keepAttr){mr(Bt,w);continue}if(!Tt&&Ht(/\/>/i,Rt)){mr(Bt,w);continue}qt&&qi([N,D,L],Oo=>{Rt=qr(Rt,Oo," ")});const $o=Mt(w.nodeName);if(!Mo($o,zr,Rt)){mr(Bt,w);continue}if(T&&typeof d=="object"&&typeof d.getAttributeType=="function"&&!Xt)switch(d.getAttributeType($o,zr)){case"TrustedHTML":{Rt=T.createHTML(Rt);break}case"TrustedScriptURL":{Rt=T.createScriptURL(Rt);break}}if(Rt!==fn)try{Xt?w.setAttributeNS(Xt,Bt,Rt):w.setAttribute(Bt,Rt),pn(w)?fe(w):Po(t.removed)}catch{mr(Bt,w)}}Se(E.afterSanitizeAttributes,w,null)},jf=function Q(w){let I=null;const G=Bo(w);for(Se(E.beforeSanitizeShadowDOM,w,null);I=G.nextNode();)Se(E.uponSanitizeShadowNode,I,null),Ao(I),Fo(I),I.content instanceof n&&Q(I.content);Se(E.afterSanitizeShadowDOM,w,null)};return t.sanitize=function(Q){let w=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},I=null,G=null,Ct=null,Dt=null;if(hn=!Q,hn&&(Q="<!-->"),typeof Q!="string"&&!Lo(Q))if(typeof Q.toString=="function"){if(Q=Q.toString(),typeof Q!="string")throw Hr("dirty is not a string, aborting")}else throw Hr("toString is not a function");if(!t.isSupported)return Q;if(sn||dn(w),t.removed=[],typeof Q=="string"&&(Pr=!1),Pr){if(Q.nodeName){const Te=Mt(Q.nodeName);if(!U[Te]||at[Te])throw Hr("root node is forbidden and cannot be sanitized in-place")}}else if(Q instanceof s)I=_o("<!---->"),G=I.ownerDocument.importNode(Q,!0),G.nodeType===Yr.element&&G.nodeName==="BODY"||G.nodeName==="HTML"?I=G:I.appendChild(G);else{if(!ur&&!qt&&!oe&&Q.indexOf("<")===-1)return T&&Ii?T.createHTML(Q):Q;if(I=_o(Q),!I)return ur?null:Ii?v:""}I&&on&&fe(I.firstChild);const Bt=Bo(Pr?Q:I);for(;Ct=Bt.nextNode();)Ao(Ct),Fo(Ct),Ct.content instanceof n&&jf(Ct.content);if(Pr)return Q;if(ur){if(Ri)for(Dt=O.call(I.ownerDocument);I.firstChild;)Dt.appendChild(I.firstChild);else Dt=I;return(nt.shadowroot||nt.shadowrootmode)&&(Dt=R.call(i,Dt,!0)),Dt}let Xt=oe?I.outerHTML:I.innerHTML;return oe&&U["!doctype"]&&I.ownerDocument&&I.ownerDocument.doctype&&I.ownerDocument.doctype.name&&Ht(uc,I.ownerDocument.doctype.name)&&(Xt="<!DOCTYPE "+I.ownerDocument.doctype.name+`>
`+Xt),qt&&qi([N,D,L],Te=>{Xt=qr(Xt,Te," ")}),T&&Ii?T.createHTML(Xt):Xt},t.setConfig=function(){let Q=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};dn(Q),sn=!0},t.clearConfig=function(){gr=null,sn=!1},t.isValidAttribute=function(Q,w,I){gr||dn({});const G=Mt(Q),Ct=Mt(w);return Mo(G,Ct,I)},t.addHook=function(Q,w){typeof w=="function"&&Wr(E[Q],w)},t.removeHook=function(Q,w){if(w!==void 0){const I=Bg(E[Q],w);return I===-1?void 0:Lg(E[Q],I,1)[0]}return Po(E[Q])},t.removeHooks=function(Q){E[Q]=[]},t.removeAllHooks=function(){E=Yo()},t}var vr=dc(),pc=Object.defineProperty,f=(e,t)=>pc(e,"name",{value:t,configurable:!0}),Yg=(e,t)=>{for(var r in t)pc(e,r,{get:t[r],enumerable:!0})},_e={trace:0,debug:1,info:2,warn:3,error:4,fatal:5},$={trace:f((...e)=>{},"trace"),debug:f((...e)=>{},"debug"),info:f((...e)=>{},"info"),warn:f((...e)=>{},"warn"),error:f((...e)=>{},"error"),fatal:f((...e)=>{},"fatal")},Ss=f(function(e="fatal"){let t=_e.fatal;typeof e=="string"?e.toLowerCase()in _e&&(t=_e[e]):typeof e=="number"&&(t=e),$.trace=()=>{},$.debug=()=>{},$.info=()=>{},$.warn=()=>{},$.error=()=>{},$.fatal=()=>{},t<=_e.fatal&&($.fatal=console.error?console.error.bind(console,re("FATAL"),"color: orange"):console.log.bind(console,"\x1B[35m",re("FATAL"))),t<=_e.error&&($.error=console.error?console.error.bind(console,re("ERROR"),"color: orange"):console.log.bind(console,"\x1B[31m",re("ERROR"))),t<=_e.warn&&($.warn=console.warn?console.warn.bind(console,re("WARN"),"color: orange"):console.log.bind(console,"\x1B[33m",re("WARN"))),t<=_e.info&&($.info=console.info?console.info.bind(console,re("INFO"),"color: lightblue"):console.log.bind(console,"\x1B[34m",re("INFO"))),t<=_e.debug&&($.debug=console.debug?console.debug.bind(console,re("DEBUG"),"color: lightgreen"):console.log.bind(console,"\x1B[32m",re("DEBUG"))),t<=_e.trace&&($.trace=console.debug?console.debug.bind(console,re("TRACE"),"color: lightgreen"):console.log.bind(console,"\x1B[32m",re("TRACE")))},"setLogLevel"),re=f(e=>`%c${mg().format("ss.SSS")} : ${e} : `,"format"),fc=/^-{3}\s*[\n\r](.*?)[\n\r]-{3}\s*[\n\r]+/s,si=/%{2}{\s*(?:(\w+)\s*:|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,Ug=/\s*%%.*\n/gm,Ci,gc=(Ci=class extends Error{constructor(t){super(t),this.name="UnknownDiagramError"}},f(Ci,"UnknownDiagramError"),Ci),er={},Ts=f(function(e,t){e=e.replace(fc,"").replace(si,"").replace(Ug,`
`);for(const[r,{detector:i}]of Object.entries(er))if(i(e,t))return r;throw new gc(`No diagram type detected matching given configuration for text: ${e}`)},"detectType"),Rn=f((...e)=>{for(const{id:t,detector:r,loader:i}of e)mc(t,r,i)},"registerLazyLoadedDiagrams"),mc=f((e,t,r)=>{er[e]&&$.warn(`Detector with key ${e} already exists. Overwriting.`),er[e]={detector:t,loader:r},$.debug(`Detector with key ${e} added${r?" with loader":""}`)},"addDetector"),Gg=f(e=>er[e].loader,"getDiagramLoader"),In=f((e,t,{depth:r=2,clobber:i=!1}={})=>{const a={depth:r,clobber:i};return Array.isArray(t)&&!Array.isArray(e)?(t.forEach(n=>In(e,n,a)),e):Array.isArray(t)&&Array.isArray(e)?(t.forEach(n=>{e.includes(n)||e.push(n)}),e):e===void 0||r<=0?e!=null&&typeof e=="object"&&typeof t=="object"?Object.assign(e,t):t:(t!==void 0&&typeof e=="object"&&typeof t=="object"&&Object.keys(t).forEach(n=>{typeof t[n]=="object"&&(e[n]===void 0||typeof e[n]=="object")?(e[n]===void 0&&(e[n]=Array.isArray(t[n])?[]:{}),e[n]=In(e[n],t[n],{depth:r-1,clobber:i})):(i||typeof e[n]!="object"&&typeof t[n]!="object")&&(e[n]=t[n])}),e)},"assignWithDepth"),Ot=In,Ia="#ffffff",Pa="#f2f2f2",jt=f((e,t)=>t?_(e,{s:-40,l:10}):_(e,{s:-40,l:-10}),"mkBorder"),ki,Vg=(ki=class{constructor(){this.background="#f4f4f4",this.primaryColor="#fff4dd",this.noteBkgColor="#fff5ad",this.noteTextColor="#333",this.THEME_COLOR_LIMIT=12,this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px"}updateColors(){var r,i,a,n,o,s,c,l,h,u,p,d,g,m,y,x,b,k,T,v,C;if(this.primaryTextColor=this.primaryTextColor||(this.darkMode?"#eee":"#333"),this.secondaryColor=this.secondaryColor||_(this.primaryColor,{h:-120}),this.tertiaryColor=this.tertiaryColor||_(this.primaryColor,{h:180,l:5}),this.primaryBorderColor=this.primaryBorderColor||jt(this.primaryColor,this.darkMode),this.secondaryBorderColor=this.secondaryBorderColor||jt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=this.tertiaryBorderColor||jt(this.tertiaryColor,this.darkMode),this.noteBorderColor=this.noteBorderColor||jt(this.noteBkgColor,this.darkMode),this.noteBkgColor=this.noteBkgColor||"#fff5ad",this.noteTextColor=this.noteTextColor||"#333",this.secondaryTextColor=this.secondaryTextColor||z(this.secondaryColor),this.tertiaryTextColor=this.tertiaryTextColor||z(this.tertiaryColor),this.lineColor=this.lineColor||z(this.background),this.arrowheadColor=this.arrowheadColor||z(this.background),this.textColor=this.textColor||this.primaryTextColor,this.border2=this.border2||this.tertiaryBorderColor,this.nodeBkg=this.nodeBkg||this.primaryColor,this.mainBkg=this.mainBkg||this.primaryColor,this.nodeBorder=this.nodeBorder||this.primaryBorderColor,this.clusterBkg=this.clusterBkg||this.tertiaryColor,this.clusterBorder=this.clusterBorder||this.tertiaryBorderColor,this.defaultLinkColor=this.defaultLinkColor||this.lineColor,this.titleColor=this.titleColor||this.tertiaryTextColor,this.edgeLabelBackground=this.edgeLabelBackground||(this.darkMode?J(this.secondaryColor,30):this.secondaryColor),this.nodeTextColor=this.nodeTextColor||this.primaryTextColor,this.actorBorder=this.actorBorder||this.primaryBorderColor,this.actorBkg=this.actorBkg||this.mainBkg,this.actorTextColor=this.actorTextColor||this.primaryTextColor,this.actorLineColor=this.actorLineColor||this.actorBorder,this.labelBoxBkgColor=this.labelBoxBkgColor||this.actorBkg,this.signalColor=this.signalColor||this.textColor,this.signalTextColor=this.signalTextColor||this.textColor,this.labelBoxBorderColor=this.labelBoxBorderColor||this.actorBorder,this.labelTextColor=this.labelTextColor||this.actorTextColor,this.loopTextColor=this.loopTextColor||this.actorTextColor,this.activationBorderColor=this.activationBorderColor||J(this.secondaryColor,10),this.activationBkgColor=this.activationBkgColor||this.secondaryColor,this.sequenceNumberColor=this.sequenceNumberColor||z(this.lineColor),this.sectionBkgColor=this.sectionBkgColor||this.tertiaryColor,this.altSectionBkgColor=this.altSectionBkgColor||"white",this.sectionBkgColor=this.sectionBkgColor||this.secondaryColor,this.sectionBkgColor2=this.sectionBkgColor2||this.primaryColor,this.excludeBkgColor=this.excludeBkgColor||"#eeeeee",this.taskBorderColor=this.taskBorderColor||this.primaryBorderColor,this.taskBkgColor=this.taskBkgColor||this.primaryColor,this.activeTaskBorderColor=this.activeTaskBorderColor||this.primaryColor,this.activeTaskBkgColor=this.activeTaskBkgColor||q(this.primaryColor,23),this.gridColor=this.gridColor||"lightgrey",this.doneTaskBkgColor=this.doneTaskBkgColor||"lightgrey",this.doneTaskBorderColor=this.doneTaskBorderColor||"grey",this.critBorderColor=this.critBorderColor||"#ff8888",this.critBkgColor=this.critBkgColor||"red",this.todayLineColor=this.todayLineColor||"red",this.vertLineColor=this.vertLineColor||"navy",this.taskTextColor=this.taskTextColor||this.textColor,this.taskTextOutsideColor=this.taskTextOutsideColor||this.textColor,this.taskTextLightColor=this.taskTextLightColor||this.textColor,this.taskTextColor=this.taskTextColor||this.primaryTextColor,this.taskTextDarkColor=this.taskTextDarkColor||this.textColor,this.taskTextClickableColor=this.taskTextClickableColor||"#003163",this.personBorder=this.personBorder||this.primaryBorderColor,this.personBkg=this.personBkg||this.mainBkg,this.darkMode?(this.rowOdd=this.rowOdd||J(this.mainBkg,5)||"#ffffff",this.rowEven=this.rowEven||J(this.mainBkg,10)):(this.rowOdd=this.rowOdd||q(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||q(this.mainBkg,5)),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||this.tertiaryColor,this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.specialStateColor=this.lineColor,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||_(this.primaryColor,{h:30}),this.cScale4=this.cScale4||_(this.primaryColor,{h:60}),this.cScale5=this.cScale5||_(this.primaryColor,{h:90}),this.cScale6=this.cScale6||_(this.primaryColor,{h:120}),this.cScale7=this.cScale7||_(this.primaryColor,{h:150}),this.cScale8=this.cScale8||_(this.primaryColor,{h:210,l:150}),this.cScale9=this.cScale9||_(this.primaryColor,{h:270}),this.cScale10=this.cScale10||_(this.primaryColor,{h:300}),this.cScale11=this.cScale11||_(this.primaryColor,{h:330}),this.darkMode)for(let S=0;S<this.THEME_COLOR_LIMIT;S++)this["cScale"+S]=J(this["cScale"+S],75);else for(let S=0;S<this.THEME_COLOR_LIMIT;S++)this["cScale"+S]=J(this["cScale"+S],25);for(let S=0;S<this.THEME_COLOR_LIMIT;S++)this["cScaleInv"+S]=this["cScaleInv"+S]||z(this["cScale"+S]);for(let S=0;S<this.THEME_COLOR_LIMIT;S++)this.darkMode?this["cScalePeer"+S]=this["cScalePeer"+S]||q(this["cScale"+S],10):this["cScalePeer"+S]=this["cScalePeer"+S]||J(this["cScale"+S],10);this.scaleLabelColor=this.scaleLabelColor||this.labelTextColor;for(let S=0;S<this.THEME_COLOR_LIMIT;S++)this["cScaleLabel"+S]=this["cScaleLabel"+S]||this.scaleLabelColor;const t=this.darkMode?-4:-1;for(let S=0;S<5;S++)this["surface"+S]=this["surface"+S]||_(this.mainBkg,{h:180,s:-15,l:t*(5+S*3)}),this["surfacePeer"+S]=this["surfacePeer"+S]||_(this.mainBkg,{h:180,s:-15,l:t*(8+S*3)});this.classText=this.classText||this.textColor,this.fillType0=this.fillType0||this.primaryColor,this.fillType1=this.fillType1||this.secondaryColor,this.fillType2=this.fillType2||_(this.primaryColor,{h:64}),this.fillType3=this.fillType3||_(this.secondaryColor,{h:64}),this.fillType4=this.fillType4||_(this.primaryColor,{h:-64}),this.fillType5=this.fillType5||_(this.secondaryColor,{h:-64}),this.fillType6=this.fillType6||_(this.primaryColor,{h:128}),this.fillType7=this.fillType7||_(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||_(this.primaryColor,{l:-10}),this.pie5=this.pie5||_(this.secondaryColor,{l:-10}),this.pie6=this.pie6||_(this.tertiaryColor,{l:-10}),this.pie7=this.pie7||_(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||_(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||_(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||_(this.primaryColor,{h:60,l:-20}),this.pie11=this.pie11||_(this.primaryColor,{h:-60,l:-20}),this.pie12=this.pie12||_(this.primaryColor,{h:120,l:-10}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.radar={axisColor:((r=this.radar)==null?void 0:r.axisColor)||this.lineColor,axisStrokeWidth:((i=this.radar)==null?void 0:i.axisStrokeWidth)||2,axisLabelFontSize:((a=this.radar)==null?void 0:a.axisLabelFontSize)||12,curveOpacity:((n=this.radar)==null?void 0:n.curveOpacity)||.5,curveStrokeWidth:((o=this.radar)==null?void 0:o.curveStrokeWidth)||2,graticuleColor:((s=this.radar)==null?void 0:s.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((c=this.radar)==null?void 0:c.graticuleStrokeWidth)||1,graticuleOpacity:((l=this.radar)==null?void 0:l.graticuleOpacity)||.3,legendBoxSize:((h=this.radar)==null?void 0:h.legendBoxSize)||12,legendFontSize:((u=this.radar)==null?void 0:u.legendFontSize)||12},this.archEdgeColor=this.archEdgeColor||"#777",this.archEdgeArrowColor=this.archEdgeArrowColor||"#777",this.archEdgeWidth=this.archEdgeWidth||"3",this.archGroupBorderColor=this.archGroupBorderColor||"#000",this.archGroupBorderWidth=this.archGroupBorderWidth||"2px",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||_(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||_(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||_(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||_(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||_(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||_(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Li(this.quadrant1Fill)?q(this.quadrant1Fill):J(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((p=this.xyChart)==null?void 0:p.backgroundColor)||this.background,titleColor:((d=this.xyChart)==null?void 0:d.titleColor)||this.primaryTextColor,xAxisTitleColor:((g=this.xyChart)==null?void 0:g.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((m=this.xyChart)==null?void 0:m.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((y=this.xyChart)==null?void 0:y.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((x=this.xyChart)==null?void 0:x.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((b=this.xyChart)==null?void 0:b.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((k=this.xyChart)==null?void 0:k.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((T=this.xyChart)==null?void 0:T.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((v=this.xyChart)==null?void 0:v.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((C=this.xyChart)==null?void 0:C.plotColorPalette)||"#FFF4DD,#FFD8B1,#FFA07A,#ECEFF1,#D6DBDF,#C3E0A8,#FFB6A4,#FFD74D,#738FA7,#FFFFF0"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?J(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||_(this.primaryColor,{h:-30}),this.git4=this.git4||_(this.primaryColor,{h:-60}),this.git5=this.git5||_(this.primaryColor,{h:-90}),this.git6=this.git6||_(this.primaryColor,{h:60}),this.git7=this.git7||_(this.primaryColor,{h:120}),this.darkMode?(this.git0=q(this.git0,25),this.git1=q(this.git1,25),this.git2=q(this.git2,25),this.git3=q(this.git3,25),this.git4=q(this.git4,25),this.git5=q(this.git5,25),this.git6=q(this.git6,25),this.git7=q(this.git7,25)):(this.git0=J(this.git0,25),this.git1=J(this.git1,25),this.git2=J(this.git2,25),this.git3=J(this.git3,25),this.git4=J(this.git4,25),this.git5=J(this.git5,25),this.git6=J(this.git6,25),this.git7=J(this.git7,25)),this.gitInv0=this.gitInv0||z(this.git0),this.gitInv1=this.gitInv1||z(this.git1),this.gitInv2=this.gitInv2||z(this.git2),this.gitInv3=this.gitInv3||z(this.git3),this.gitInv4=this.gitInv4||z(this.git4),this.gitInv5=this.gitInv5||z(this.git5),this.gitInv6=this.gitInv6||z(this.git6),this.gitInv7=this.gitInv7||z(this.git7),this.branchLabelColor=this.branchLabelColor||(this.darkMode?"black":this.labelTextColor),this.gitBranchLabel0=this.gitBranchLabel0||this.branchLabelColor,this.gitBranchLabel1=this.gitBranchLabel1||this.branchLabelColor,this.gitBranchLabel2=this.gitBranchLabel2||this.branchLabelColor,this.gitBranchLabel3=this.gitBranchLabel3||this.branchLabelColor,this.gitBranchLabel4=this.gitBranchLabel4||this.branchLabelColor,this.gitBranchLabel5=this.gitBranchLabel5||this.branchLabelColor,this.gitBranchLabel6=this.gitBranchLabel6||this.branchLabelColor,this.gitBranchLabel7=this.gitBranchLabel7||this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Ia,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Pa}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},f(ki,"Theme"),ki),Xg=f(e=>{const t=new Vg;return t.calculate(e),t},"getThemeVariables"),wi,Zg=(wi=class{constructor(){this.background="#333",this.primaryColor="#1f2020",this.secondaryColor=q(this.primaryColor,16),this.tertiaryColor=_(this.primaryColor,{h:-160}),this.primaryBorderColor=z(this.background),this.secondaryBorderColor=jt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=jt(this.tertiaryColor,this.darkMode),this.primaryTextColor=z(this.primaryColor),this.secondaryTextColor=z(this.secondaryColor),this.tertiaryTextColor=z(this.tertiaryColor),this.lineColor=z(this.background),this.textColor=z(this.background),this.mainBkg="#1f2020",this.secondBkg="calculated",this.mainContrastColor="lightgrey",this.darkTextColor=q(z("#323D47"),10),this.lineColor="calculated",this.border1="#ccc",this.border2=ni(255,255,255,.25),this.arrowheadColor="calculated",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="#181818",this.textColor="#ccc",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#F9FFFE",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="calculated",this.activationBkgColor="calculated",this.sequenceNumberColor="black",this.sectionBkgColor=J("#EAE8D9",30),this.altSectionBkgColor="calculated",this.sectionBkgColor2="#EAE8D9",this.excludeBkgColor=J(this.sectionBkgColor,10),this.taskBorderColor=ni(255,255,255,70),this.taskBkgColor="calculated",this.taskTextColor="calculated",this.taskTextLightColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor=ni(255,255,255,50),this.activeTaskBkgColor="#81B1DB",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="grey",this.critBorderColor="#E83737",this.critBkgColor="#E83737",this.taskTextDarkColor="calculated",this.todayLineColor="#DB5757",this.vertLineColor="#00BFFF",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd=this.rowOdd||q(this.mainBkg,5)||"#ffffff",this.rowEven=this.rowEven||J(this.mainBkg,10),this.labelColor="calculated",this.errorBkgColor="#a44141",this.errorTextColor="#ddd"}updateColors(){var t,r,i,a,n,o,s,c,l,h,u,p,d,g,m,y,x,b,k,T,v;this.secondBkg=q(this.mainBkg,16),this.lineColor=this.mainContrastColor,this.arrowheadColor=this.mainContrastColor,this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.edgeLabelBackground=q(this.labelBackground,25),this.actorBorder=this.border1,this.actorBkg=this.mainBkg,this.actorTextColor=this.mainContrastColor,this.actorLineColor=this.actorBorder,this.signalColor=this.mainContrastColor,this.signalTextColor=this.mainContrastColor,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.mainContrastColor,this.loopTextColor=this.mainContrastColor,this.noteBorderColor=this.secondaryBorderColor,this.noteBkgColor=this.secondBkg,this.noteTextColor=this.secondaryTextColor,this.activationBorderColor=this.border1,this.activationBkgColor=this.secondBkg,this.altSectionBkgColor=this.background,this.taskBkgColor=q(this.mainBkg,23),this.taskTextColor=this.darkTextColor,this.taskTextLightColor=this.mainContrastColor,this.taskTextOutsideColor=this.taskTextLightColor,this.gridColor=this.mainContrastColor,this.doneTaskBkgColor=this.mainContrastColor,this.taskTextDarkColor=this.darkTextColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#555",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#f4f4f4",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=_(this.primaryColor,{h:64}),this.fillType3=_(this.secondaryColor,{h:64}),this.fillType4=_(this.primaryColor,{h:-64}),this.fillType5=_(this.secondaryColor,{h:-64}),this.fillType6=_(this.primaryColor,{h:128}),this.fillType7=_(this.secondaryColor,{h:128}),this.cScale1=this.cScale1||"#0b0000",this.cScale2=this.cScale2||"#4d1037",this.cScale3=this.cScale3||"#3f5258",this.cScale4=this.cScale4||"#4f2f1b",this.cScale5=this.cScale5||"#6e0a0a",this.cScale6=this.cScale6||"#3b0048",this.cScale7=this.cScale7||"#995a01",this.cScale8=this.cScale8||"#154706",this.cScale9=this.cScale9||"#161722",this.cScale10=this.cScale10||"#00296f",this.cScale11=this.cScale11||"#01629c",this.cScale12=this.cScale12||"#010029",this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||_(this.primaryColor,{h:30}),this.cScale4=this.cScale4||_(this.primaryColor,{h:60}),this.cScale5=this.cScale5||_(this.primaryColor,{h:90}),this.cScale6=this.cScale6||_(this.primaryColor,{h:120}),this.cScale7=this.cScale7||_(this.primaryColor,{h:150}),this.cScale8=this.cScale8||_(this.primaryColor,{h:210}),this.cScale9=this.cScale9||_(this.primaryColor,{h:270}),this.cScale10=this.cScale10||_(this.primaryColor,{h:300}),this.cScale11=this.cScale11||_(this.primaryColor,{h:330});for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleInv"+C]=this["cScaleInv"+C]||z(this["cScale"+C]);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScalePeer"+C]=this["cScalePeer"+C]||q(this["cScale"+C],10);for(let C=0;C<5;C++)this["surface"+C]=this["surface"+C]||_(this.mainBkg,{h:30,s:-30,l:-(-10+C*4)}),this["surfacePeer"+C]=this["surfacePeer"+C]||_(this.mainBkg,{h:30,s:-30,l:-(-7+C*4)});this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleLabel"+C]=this["cScaleLabel"+C]||this.scaleLabelColor;for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["pie"+C]=this["cScale"+C];this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||_(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||_(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||_(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||_(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||_(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||_(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Li(this.quadrant1Fill)?q(this.quadrant1Fill):J(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((t=this.xyChart)==null?void 0:t.backgroundColor)||this.background,titleColor:((r=this.xyChart)==null?void 0:r.titleColor)||this.primaryTextColor,xAxisTitleColor:((i=this.xyChart)==null?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((a=this.xyChart)==null?void 0:a.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((n=this.xyChart)==null?void 0:n.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((o=this.xyChart)==null?void 0:o.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((s=this.xyChart)==null?void 0:s.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((c=this.xyChart)==null?void 0:c.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((l=this.xyChart)==null?void 0:l.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((h=this.xyChart)==null?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((u=this.xyChart)==null?void 0:u.plotColorPalette)||"#3498db,#2ecc71,#e74c3c,#f1c40f,#bdc3c7,#ffffff,#34495e,#9b59b6,#1abc9c,#e67e22"},this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.background},this.radar={axisColor:((p=this.radar)==null?void 0:p.axisColor)||this.lineColor,axisStrokeWidth:((d=this.radar)==null?void 0:d.axisStrokeWidth)||2,axisLabelFontSize:((g=this.radar)==null?void 0:g.axisLabelFontSize)||12,curveOpacity:((m=this.radar)==null?void 0:m.curveOpacity)||.5,curveStrokeWidth:((y=this.radar)==null?void 0:y.curveStrokeWidth)||2,graticuleColor:((x=this.radar)==null?void 0:x.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((b=this.radar)==null?void 0:b.graticuleStrokeWidth)||1,graticuleOpacity:((k=this.radar)==null?void 0:k.graticuleOpacity)||.3,legendBoxSize:((T=this.radar)==null?void 0:T.legendBoxSize)||12,legendFontSize:((v=this.radar)==null?void 0:v.legendFontSize)||12},this.classText=this.primaryTextColor,this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?J(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=q(this.secondaryColor,20),this.git1=q(this.pie2||this.secondaryColor,20),this.git2=q(this.pie3||this.tertiaryColor,20),this.git3=q(this.pie4||_(this.primaryColor,{h:-30}),20),this.git4=q(this.pie5||_(this.primaryColor,{h:-60}),20),this.git5=q(this.pie6||_(this.primaryColor,{h:-90}),10),this.git6=q(this.pie7||_(this.primaryColor,{h:60}),10),this.git7=q(this.pie8||_(this.primaryColor,{h:120}),20),this.gitInv0=this.gitInv0||z(this.git0),this.gitInv1=this.gitInv1||z(this.git1),this.gitInv2=this.gitInv2||z(this.git2),this.gitInv3=this.gitInv3||z(this.git3),this.gitInv4=this.gitInv4||z(this.git4),this.gitInv5=this.gitInv5||z(this.git5),this.gitInv6=this.gitInv6||z(this.git6),this.gitInv7=this.gitInv7||z(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||z(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||z(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||q(this.background,12),this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||q(this.background,2),this.nodeBorder=this.nodeBorder||"#999"}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},f(wi,"Theme"),wi),Kg=f(e=>{const t=new Zg;return t.calculate(e),t},"getThemeVariables"),vi,Qg=(vi=class{constructor(){this.background="#f4f4f4",this.primaryColor="#ECECFF",this.secondaryColor=_(this.primaryColor,{h:120}),this.secondaryColor="#ffffde",this.tertiaryColor=_(this.primaryColor,{h:-160}),this.primaryBorderColor=jt(this.primaryColor,this.darkMode),this.secondaryBorderColor=jt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=jt(this.tertiaryColor,this.darkMode),this.primaryTextColor=z(this.primaryColor),this.secondaryTextColor=z(this.secondaryColor),this.tertiaryTextColor=z(this.tertiaryColor),this.lineColor=z(this.background),this.textColor=z(this.background),this.background="white",this.mainBkg="#ECECFF",this.secondBkg="#ffffde",this.lineColor="#333333",this.border1="#9370DB",this.border2="#aaaa33",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="rgba(232,232,232, 0.8)",this.textColor="#333",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="calculated",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="calculated",this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor="calculated",this.taskTextOutsideColor=this.taskTextDarkColor,this.taskTextClickableColor="calculated",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBorderColor="calculated",this.critBkgColor="calculated",this.todayLineColor="calculated",this.vertLineColor="calculated",this.sectionBkgColor=ni(102,102,255,.49),this.altSectionBkgColor="white",this.sectionBkgColor2="#fff400",this.taskBorderColor="#534fbc",this.taskBkgColor="#8a90dd",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="#534fbc",this.activeTaskBkgColor="#bfc7ff",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.vertLineColor="navy",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd="calculated",this.rowEven="calculated",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222",this.updateColors()}updateColors(){var t,r,i,a,n,o,s,c,l,h,u,p,d,g,m,y,x,b,k,T,v;this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||_(this.primaryColor,{h:30}),this.cScale4=this.cScale4||_(this.primaryColor,{h:60}),this.cScale5=this.cScale5||_(this.primaryColor,{h:90}),this.cScale6=this.cScale6||_(this.primaryColor,{h:120}),this.cScale7=this.cScale7||_(this.primaryColor,{h:150}),this.cScale8=this.cScale8||_(this.primaryColor,{h:210}),this.cScale9=this.cScale9||_(this.primaryColor,{h:270}),this.cScale10=this.cScale10||_(this.primaryColor,{h:300}),this.cScale11=this.cScale11||_(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||J(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||J(this.tertiaryColor,40);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScale"+C]=J(this["cScale"+C],10),this["cScalePeer"+C]=this["cScalePeer"+C]||J(this["cScale"+C],25);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleInv"+C]=this["cScaleInv"+C]||_(this["cScale"+C],{h:180});for(let C=0;C<5;C++)this["surface"+C]=this["surface"+C]||_(this.mainBkg,{h:30,l:-(5+C*5)}),this["surfacePeer"+C]=this["surfacePeer"+C]||_(this.mainBkg,{h:30,l:-(7+C*5)});if(this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor,this.labelTextColor!=="calculated"){this.cScaleLabel0=this.cScaleLabel0||z(this.labelTextColor),this.cScaleLabel3=this.cScaleLabel3||z(this.labelTextColor);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleLabel"+C]=this["cScaleLabel"+C]||this.labelTextColor}this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.textColor,this.edgeLabelBackground=this.labelBackground,this.actorBorder=q(this.border1,23),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.signalColor=this.textColor,this.signalTextColor=this.textColor,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.actorLineColor=this.actorBorder,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.rowOdd=this.rowOdd||q(this.primaryColor,75)||"#ffffff",this.rowEven=this.rowEven||q(this.primaryColor,1),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=_(this.primaryColor,{h:64}),this.fillType3=_(this.secondaryColor,{h:64}),this.fillType4=_(this.primaryColor,{h:-64}),this.fillType5=_(this.secondaryColor,{h:-64}),this.fillType6=_(this.primaryColor,{h:128}),this.fillType7=_(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||_(this.tertiaryColor,{l:-40}),this.pie4=this.pie4||_(this.primaryColor,{l:-10}),this.pie5=this.pie5||_(this.secondaryColor,{l:-30}),this.pie6=this.pie6||_(this.tertiaryColor,{l:-20}),this.pie7=this.pie7||_(this.primaryColor,{h:60,l:-20}),this.pie8=this.pie8||_(this.primaryColor,{h:-60,l:-40}),this.pie9=this.pie9||_(this.primaryColor,{h:120,l:-40}),this.pie10=this.pie10||_(this.primaryColor,{h:60,l:-40}),this.pie11=this.pie11||_(this.primaryColor,{h:-90,l:-40}),this.pie12=this.pie12||_(this.primaryColor,{h:120,l:-30}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||_(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||_(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||_(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||_(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||_(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||_(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Li(this.quadrant1Fill)?q(this.quadrant1Fill):J(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.radar={axisColor:((t=this.radar)==null?void 0:t.axisColor)||this.lineColor,axisStrokeWidth:((r=this.radar)==null?void 0:r.axisStrokeWidth)||2,axisLabelFontSize:((i=this.radar)==null?void 0:i.axisLabelFontSize)||12,curveOpacity:((a=this.radar)==null?void 0:a.curveOpacity)||.5,curveStrokeWidth:((n=this.radar)==null?void 0:n.curveStrokeWidth)||2,graticuleColor:((o=this.radar)==null?void 0:o.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((s=this.radar)==null?void 0:s.graticuleStrokeWidth)||1,graticuleOpacity:((c=this.radar)==null?void 0:c.graticuleOpacity)||.3,legendBoxSize:((l=this.radar)==null?void 0:l.legendBoxSize)||12,legendFontSize:((h=this.radar)==null?void 0:h.legendFontSize)||12},this.xyChart={backgroundColor:((u=this.xyChart)==null?void 0:u.backgroundColor)||this.background,titleColor:((p=this.xyChart)==null?void 0:p.titleColor)||this.primaryTextColor,xAxisTitleColor:((d=this.xyChart)==null?void 0:d.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((g=this.xyChart)==null?void 0:g.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((m=this.xyChart)==null?void 0:m.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((y=this.xyChart)==null?void 0:y.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((x=this.xyChart)==null?void 0:x.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((b=this.xyChart)==null?void 0:b.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((k=this.xyChart)==null?void 0:k.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((T=this.xyChart)==null?void 0:T.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((v=this.xyChart)==null?void 0:v.plotColorPalette)||"#ECECFF,#8493A6,#FFC3A0,#DCDDE1,#B8E994,#D1A36F,#C3CDE6,#FFB6C1,#496078,#F8F3E3"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.labelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||_(this.primaryColor,{h:-30}),this.git4=this.git4||_(this.primaryColor,{h:-60}),this.git5=this.git5||_(this.primaryColor,{h:-90}),this.git6=this.git6||_(this.primaryColor,{h:60}),this.git7=this.git7||_(this.primaryColor,{h:120}),this.darkMode?(this.git0=q(this.git0,25),this.git1=q(this.git1,25),this.git2=q(this.git2,25),this.git3=q(this.git3,25),this.git4=q(this.git4,25),this.git5=q(this.git5,25),this.git6=q(this.git6,25),this.git7=q(this.git7,25)):(this.git0=J(this.git0,25),this.git1=J(this.git1,25),this.git2=J(this.git2,25),this.git3=J(this.git3,25),this.git4=J(this.git4,25),this.git5=J(this.git5,25),this.git6=J(this.git6,25),this.git7=J(this.git7,25)),this.gitInv0=this.gitInv0||J(z(this.git0),25),this.gitInv1=this.gitInv1||z(this.git1),this.gitInv2=this.gitInv2||z(this.git2),this.gitInv3=this.gitInv3||z(this.git3),this.gitInv4=this.gitInv4||z(this.git4),this.gitInv5=this.gitInv5||z(this.git5),this.gitInv6=this.gitInv6||z(this.git6),this.gitInv7=this.gitInv7||z(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||z(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||z(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Ia,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Pa}calculate(t){if(Object.keys(this).forEach(i=>{this[i]==="calculated"&&(this[i]=void 0)}),typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},f(vi,"Theme"),vi),Jg=f(e=>{const t=new Qg;return t.calculate(e),t},"getThemeVariables"),Si,tm=(Si=class{constructor(){this.background="#f4f4f4",this.primaryColor="#cde498",this.secondaryColor="#cdffb2",this.background="white",this.mainBkg="#cde498",this.secondBkg="#cdffb2",this.lineColor="green",this.border1="#13540c",this.border2="#6eaa49",this.arrowheadColor="green",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.tertiaryColor=q("#cde498",10),this.primaryBorderColor=jt(this.primaryColor,this.darkMode),this.secondaryBorderColor=jt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=jt(this.tertiaryColor,this.darkMode),this.primaryTextColor=z(this.primaryColor),this.secondaryTextColor=z(this.secondaryColor),this.tertiaryTextColor=z(this.primaryColor),this.lineColor=z(this.background),this.textColor=z(this.background),this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#333",this.edgeLabelBackground="#e8e8e8",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="calculated",this.signalColor="#333",this.signalTextColor="#333",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="#326932",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="#6eaa49",this.altSectionBkgColor="white",this.sectionBkgColor2="#6eaa49",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="#487e3a",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.vertLineColor="#00BFFF",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){var t,r,i,a,n,o,s,c,l,h,u,p,d,g,m,y,x,b,k,T,v;this.actorBorder=J(this.mainBkg,20),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.actorLineColor=this.actorBorder,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||_(this.primaryColor,{h:30}),this.cScale4=this.cScale4||_(this.primaryColor,{h:60}),this.cScale5=this.cScale5||_(this.primaryColor,{h:90}),this.cScale6=this.cScale6||_(this.primaryColor,{h:120}),this.cScale7=this.cScale7||_(this.primaryColor,{h:150}),this.cScale8=this.cScale8||_(this.primaryColor,{h:210}),this.cScale9=this.cScale9||_(this.primaryColor,{h:270}),this.cScale10=this.cScale10||_(this.primaryColor,{h:300}),this.cScale11=this.cScale11||_(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||J(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||J(this.tertiaryColor,40);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScale"+C]=J(this["cScale"+C],10),this["cScalePeer"+C]=this["cScalePeer"+C]||J(this["cScale"+C],25);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleInv"+C]=this["cScaleInv"+C]||_(this["cScale"+C],{h:180});this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor;for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleLabel"+C]=this["cScaleLabel"+C]||this.scaleLabelColor;for(let C=0;C<5;C++)this["surface"+C]=this["surface"+C]||_(this.mainBkg,{h:30,s:-30,l:-(5+C*5)}),this["surfacePeer"+C]=this["surfacePeer"+C]||_(this.mainBkg,{h:30,s:-30,l:-(8+C*5)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.taskBorderColor=this.border1,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.rowOdd=this.rowOdd||q(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||q(this.mainBkg,20),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=_(this.primaryColor,{h:64}),this.fillType3=_(this.secondaryColor,{h:64}),this.fillType4=_(this.primaryColor,{h:-64}),this.fillType5=_(this.secondaryColor,{h:-64}),this.fillType6=_(this.primaryColor,{h:128}),this.fillType7=_(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||_(this.primaryColor,{l:-30}),this.pie5=this.pie5||_(this.secondaryColor,{l:-30}),this.pie6=this.pie6||_(this.tertiaryColor,{h:40,l:-40}),this.pie7=this.pie7||_(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||_(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||_(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||_(this.primaryColor,{h:60,l:-50}),this.pie11=this.pie11||_(this.primaryColor,{h:-60,l:-50}),this.pie12=this.pie12||_(this.primaryColor,{h:120,l:-50}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||_(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||_(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||_(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||_(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||_(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||_(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Li(this.quadrant1Fill)?q(this.quadrant1Fill):J(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.mainBkg},this.radar={axisColor:((t=this.radar)==null?void 0:t.axisColor)||this.lineColor,axisStrokeWidth:((r=this.radar)==null?void 0:r.axisStrokeWidth)||2,axisLabelFontSize:((i=this.radar)==null?void 0:i.axisLabelFontSize)||12,curveOpacity:((a=this.radar)==null?void 0:a.curveOpacity)||.5,curveStrokeWidth:((n=this.radar)==null?void 0:n.curveStrokeWidth)||2,graticuleColor:((o=this.radar)==null?void 0:o.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((s=this.radar)==null?void 0:s.graticuleStrokeWidth)||1,graticuleOpacity:((c=this.radar)==null?void 0:c.graticuleOpacity)||.3,legendBoxSize:((l=this.radar)==null?void 0:l.legendBoxSize)||12,legendFontSize:((h=this.radar)==null?void 0:h.legendFontSize)||12},this.xyChart={backgroundColor:((u=this.xyChart)==null?void 0:u.backgroundColor)||this.background,titleColor:((p=this.xyChart)==null?void 0:p.titleColor)||this.primaryTextColor,xAxisTitleColor:((d=this.xyChart)==null?void 0:d.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((g=this.xyChart)==null?void 0:g.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((m=this.xyChart)==null?void 0:m.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((y=this.xyChart)==null?void 0:y.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((x=this.xyChart)==null?void 0:x.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((b=this.xyChart)==null?void 0:b.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((k=this.xyChart)==null?void 0:k.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((T=this.xyChart)==null?void 0:T.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((v=this.xyChart)==null?void 0:v.plotColorPalette)||"#CDE498,#FF6B6B,#A0D2DB,#D7BDE2,#F0F0F0,#FFC3A0,#7FD8BE,#FF9A8B,#FAF3E0,#FFF176"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||_(this.primaryColor,{h:-30}),this.git4=this.git4||_(this.primaryColor,{h:-60}),this.git5=this.git5||_(this.primaryColor,{h:-90}),this.git6=this.git6||_(this.primaryColor,{h:60}),this.git7=this.git7||_(this.primaryColor,{h:120}),this.darkMode?(this.git0=q(this.git0,25),this.git1=q(this.git1,25),this.git2=q(this.git2,25),this.git3=q(this.git3,25),this.git4=q(this.git4,25),this.git5=q(this.git5,25),this.git6=q(this.git6,25),this.git7=q(this.git7,25)):(this.git0=J(this.git0,25),this.git1=J(this.git1,25),this.git2=J(this.git2,25),this.git3=J(this.git3,25),this.git4=J(this.git4,25),this.git5=J(this.git5,25),this.git6=J(this.git6,25),this.git7=J(this.git7,25)),this.gitInv0=this.gitInv0||z(this.git0),this.gitInv1=this.gitInv1||z(this.git1),this.gitInv2=this.gitInv2||z(this.git2),this.gitInv3=this.gitInv3||z(this.git3),this.gitInv4=this.gitInv4||z(this.git4),this.gitInv5=this.gitInv5||z(this.git5),this.gitInv6=this.gitInv6||z(this.git6),this.gitInv7=this.gitInv7||z(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||z(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||z(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Ia,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Pa}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},f(Si,"Theme"),Si),em=f(e=>{const t=new tm;return t.calculate(e),t},"getThemeVariables"),Ti,rm=(Ti=class{constructor(){this.primaryColor="#eee",this.contrast="#707070",this.secondaryColor=q(this.contrast,55),this.background="#ffffff",this.tertiaryColor=_(this.primaryColor,{h:-160}),this.primaryBorderColor=jt(this.primaryColor,this.darkMode),this.secondaryBorderColor=jt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=jt(this.tertiaryColor,this.darkMode),this.primaryTextColor=z(this.primaryColor),this.secondaryTextColor=z(this.secondaryColor),this.tertiaryTextColor=z(this.tertiaryColor),this.lineColor=z(this.background),this.textColor=z(this.background),this.mainBkg="#eee",this.secondBkg="calculated",this.lineColor="#666",this.border1="#999",this.border2="calculated",this.note="#ffa",this.text="#333",this.critical="#d42",this.done="#bbb",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="white",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor=this.actorBorder,this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="calculated",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="white",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBkgColor="calculated",this.critBorderColor="calculated",this.todayLineColor="calculated",this.vertLineColor="calculated",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd=this.rowOdd||q(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||"#f4f4f4",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){var t,r,i,a,n,o,s,c,l,h,u,p,d,g,m,y,x,b,k,T,v;this.secondBkg=q(this.contrast,55),this.border2=this.contrast,this.actorBorder=q(this.border1,23),this.actorBkg=this.mainBkg,this.actorTextColor=this.text,this.actorLineColor=this.actorBorder,this.signalColor=this.text,this.signalTextColor=this.text,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.text,this.loopTextColor=this.text,this.noteBorderColor="#999",this.noteBkgColor="#666",this.noteTextColor="#fff",this.cScale0=this.cScale0||"#555",this.cScale1=this.cScale1||"#F4F4F4",this.cScale2=this.cScale2||"#555",this.cScale3=this.cScale3||"#BBB",this.cScale4=this.cScale4||"#777",this.cScale5=this.cScale5||"#999",this.cScale6=this.cScale6||"#DDD",this.cScale7=this.cScale7||"#FFF",this.cScale8=this.cScale8||"#DDD",this.cScale9=this.cScale9||"#BBB",this.cScale10=this.cScale10||"#999",this.cScale11=this.cScale11||"#777";for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleInv"+C]=this["cScaleInv"+C]||z(this["cScale"+C]);for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this.darkMode?this["cScalePeer"+C]=this["cScalePeer"+C]||q(this["cScale"+C],10):this["cScalePeer"+C]=this["cScalePeer"+C]||J(this["cScale"+C],10);this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor),this.cScaleLabel0=this.cScaleLabel0||this.cScale1,this.cScaleLabel2=this.cScaleLabel2||this.cScale1;for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["cScaleLabel"+C]=this["cScaleLabel"+C]||this.scaleLabelColor;for(let C=0;C<5;C++)this["surface"+C]=this["surface"+C]||_(this.mainBkg,{l:-(5+C*5)}),this["surfacePeer"+C]=this["surfacePeer"+C]||_(this.mainBkg,{l:-(8+C*5)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.text,this.sectionBkgColor=q(this.contrast,30),this.sectionBkgColor2=q(this.contrast,30),this.taskBorderColor=J(this.contrast,10),this.taskBkgColor=this.contrast,this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor=this.text,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.gridColor=q(this.border1,30),this.doneTaskBkgColor=this.done,this.doneTaskBorderColor=this.lineColor,this.critBkgColor=this.critical,this.critBorderColor=J(this.critBkgColor,10),this.todayLineColor=this.critBkgColor,this.vertLineColor=this.critBkgColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.transitionColor=this.transitionColor||"#000",this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f4f4f4",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.stateBorder=this.stateBorder||"#000",this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#222",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=_(this.primaryColor,{h:64}),this.fillType3=_(this.secondaryColor,{h:64}),this.fillType4=_(this.primaryColor,{h:-64}),this.fillType5=_(this.secondaryColor,{h:-64}),this.fillType6=_(this.primaryColor,{h:128}),this.fillType7=_(this.secondaryColor,{h:128});for(let C=0;C<this.THEME_COLOR_LIMIT;C++)this["pie"+C]=this["cScale"+C];this.pie12=this.pie0,this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||_(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||_(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||_(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||_(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||_(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||_(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Li(this.quadrant1Fill)?q(this.quadrant1Fill):J(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((t=this.xyChart)==null?void 0:t.backgroundColor)||this.background,titleColor:((r=this.xyChart)==null?void 0:r.titleColor)||this.primaryTextColor,xAxisTitleColor:((i=this.xyChart)==null?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((a=this.xyChart)==null?void 0:a.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((n=this.xyChart)==null?void 0:n.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((o=this.xyChart)==null?void 0:o.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((s=this.xyChart)==null?void 0:s.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((c=this.xyChart)==null?void 0:c.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((l=this.xyChart)==null?void 0:l.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((h=this.xyChart)==null?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((u=this.xyChart)==null?void 0:u.plotColorPalette)||"#EEE,#6BB8E4,#8ACB88,#C7ACD6,#E8DCC2,#FFB2A8,#FFF380,#7E8D91,#FFD8B1,#FAF3E0"},this.radar={axisColor:((p=this.radar)==null?void 0:p.axisColor)||this.lineColor,axisStrokeWidth:((d=this.radar)==null?void 0:d.axisStrokeWidth)||2,axisLabelFontSize:((g=this.radar)==null?void 0:g.axisLabelFontSize)||12,curveOpacity:((m=this.radar)==null?void 0:m.curveOpacity)||.5,curveStrokeWidth:((y=this.radar)==null?void 0:y.curveStrokeWidth)||2,graticuleColor:((x=this.radar)==null?void 0:x.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((b=this.radar)==null?void 0:b.graticuleStrokeWidth)||1,graticuleOpacity:((k=this.radar)==null?void 0:k.graticuleOpacity)||.3,legendBoxSize:((T=this.radar)==null?void 0:T.legendBoxSize)||12,legendFontSize:((v=this.radar)==null?void 0:v.legendFontSize)||12},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=J(this.pie1,25)||this.primaryColor,this.git1=this.pie2||this.secondaryColor,this.git2=this.pie3||this.tertiaryColor,this.git3=this.pie4||_(this.primaryColor,{h:-30}),this.git4=this.pie5||_(this.primaryColor,{h:-60}),this.git5=this.pie6||_(this.primaryColor,{h:-90}),this.git6=this.pie7||_(this.primaryColor,{h:60}),this.git7=this.pie8||_(this.primaryColor,{h:120}),this.gitInv0=this.gitInv0||z(this.git0),this.gitInv1=this.gitInv1||z(this.git1),this.gitInv2=this.gitInv2||z(this.git2),this.gitInv3=this.gitInv3||z(this.git3),this.gitInv4=this.gitInv4||z(this.git4),this.gitInv5=this.gitInv5||z(this.git5),this.gitInv6=this.gitInv6||z(this.git6),this.gitInv7=this.gitInv7||z(this.git7),this.branchLabelColor=this.branchLabelColor||this.labelTextColor,this.gitBranchLabel0=this.branchLabelColor,this.gitBranchLabel1="white",this.gitBranchLabel2=this.branchLabelColor,this.gitBranchLabel3="white",this.gitBranchLabel4=this.branchLabelColor,this.gitBranchLabel5=this.branchLabelColor,this.gitBranchLabel6=this.branchLabelColor,this.gitBranchLabel7=this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Ia,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Pa}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},f(Ti,"Theme"),Ti),im=f(e=>{const t=new rm;return t.calculate(e),t},"getThemeVariables"),$e={base:{getThemeVariables:Xg},dark:{getThemeVariables:Kg},default:{getThemeVariables:Jg},forest:{getThemeVariables:em},neutral:{getThemeVariables:im}},ge={flowchart:{useMaxWidth:!0,titleTopMargin:25,subGraphTitleMargin:{top:0,bottom:0},diagramPadding:8,htmlLabels:!0,nodeSpacing:50,rankSpacing:50,curve:"basis",padding:15,defaultRenderer:"dagre-wrapper",wrappingWidth:200,inheritDir:!1},sequence:{useMaxWidth:!0,hideUnusedParticipants:!1,activationWidth:10,diagramMarginX:50,diagramMarginY:10,actorMargin:50,width:150,height:65,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",mirrorActors:!0,forceMenus:!1,bottomMarginAdj:1,rightAngles:!1,showSequenceNumbers:!1,actorFontSize:14,actorFontFamily:'"Open Sans", sans-serif',actorFontWeight:400,noteFontSize:14,noteFontFamily:'"trebuchet ms", verdana, arial, sans-serif',noteFontWeight:400,noteAlign:"center",messageFontSize:16,messageFontFamily:'"trebuchet ms", verdana, arial, sans-serif',messageFontWeight:400,wrap:!1,wrapPadding:10,labelBoxWidth:50,labelBoxHeight:20},gantt:{useMaxWidth:!0,titleTopMargin:25,barHeight:20,barGap:4,topPadding:50,rightPadding:75,leftPadding:75,gridLineStartPadding:35,fontSize:11,sectionFontSize:11,numberSectionStyles:4,axisFormat:"%Y-%m-%d",topAxis:!1,displayMode:"",weekday:"sunday"},journey:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,maxLabelWidth:360,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"],titleColor:"",titleFontFamily:'"trebuchet ms", verdana, arial, sans-serif',titleFontSize:"4ex"},class:{useMaxWidth:!0,titleTopMargin:25,arrowMarkerAbsolute:!1,dividerMargin:10,padding:5,textHeight:10,defaultRenderer:"dagre-wrapper",htmlLabels:!1,hideEmptyMembersBox:!1},state:{useMaxWidth:!0,titleTopMargin:25,dividerMargin:10,sizeUnit:5,padding:8,textHeight:10,titleShift:-15,noteMargin:10,forkWidth:70,forkHeight:7,miniPadding:2,fontSizeFactor:5.02,fontSize:24,labelHeight:16,edgeLengthFactor:"20",compositTitleSize:35,radius:5,defaultRenderer:"dagre-wrapper"},er:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:20,layoutDirection:"TB",minEntityWidth:100,minEntityHeight:75,entityPadding:15,nodeSpacing:140,rankSpacing:80,stroke:"gray",fill:"honeydew",fontSize:12},pie:{useMaxWidth:!0,textPosition:.75},quadrantChart:{useMaxWidth:!0,chartWidth:500,chartHeight:500,titleFontSize:20,titlePadding:10,quadrantPadding:5,xAxisLabelPadding:5,yAxisLabelPadding:5,xAxisLabelFontSize:16,yAxisLabelFontSize:16,quadrantLabelFontSize:16,quadrantTextTopPadding:5,pointTextPadding:5,pointLabelFontSize:12,pointRadius:5,xAxisPosition:"top",yAxisPosition:"left",quadrantInternalBorderStrokeWidth:1,quadrantExternalBorderStrokeWidth:2},xyChart:{useMaxWidth:!0,width:700,height:500,titleFontSize:20,titlePadding:10,showDataLabel:!1,showTitle:!0,xAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},yAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},chartOrientation:"vertical",plotReservedSpacePercent:50},requirement:{useMaxWidth:!0,rect_fill:"#f9f9f9",text_color:"#333",rect_border_size:"0.5px",rect_border_color:"#bbb",rect_min_width:200,rect_min_height:200,fontSize:14,rect_padding:10,line_height:20},mindmap:{useMaxWidth:!0,padding:10,maxNodeWidth:200},kanban:{useMaxWidth:!0,padding:8,sectionWidth:200,ticketBaseUrl:""},timeline:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"],disableMulticolor:!1},gitGraph:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:8,nodeLabel:{width:75,height:100,x:-25,y:0},mainBranchName:"main",mainBranchOrder:0,showCommitLabel:!0,showBranches:!0,rotateCommitLabel:!0,parallelCommits:!1,arrowMarkerAbsolute:!1},c4:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,c4ShapeMargin:50,c4ShapePadding:20,width:216,height:60,boxMargin:10,c4ShapeInRow:4,nextLinePaddingX:0,c4BoundaryInRow:2,personFontSize:14,personFontFamily:'"Open Sans", sans-serif',personFontWeight:"normal",external_personFontSize:14,external_personFontFamily:'"Open Sans", sans-serif',external_personFontWeight:"normal",systemFontSize:14,systemFontFamily:'"Open Sans", sans-serif',systemFontWeight:"normal",external_systemFontSize:14,external_systemFontFamily:'"Open Sans", sans-serif',external_systemFontWeight:"normal",system_dbFontSize:14,system_dbFontFamily:'"Open Sans", sans-serif',system_dbFontWeight:"normal",external_system_dbFontSize:14,external_system_dbFontFamily:'"Open Sans", sans-serif',external_system_dbFontWeight:"normal",system_queueFontSize:14,system_queueFontFamily:'"Open Sans", sans-serif',system_queueFontWeight:"normal",external_system_queueFontSize:14,external_system_queueFontFamily:'"Open Sans", sans-serif',external_system_queueFontWeight:"normal",boundaryFontSize:14,boundaryFontFamily:'"Open Sans", sans-serif',boundaryFontWeight:"normal",messageFontSize:12,messageFontFamily:'"Open Sans", sans-serif',messageFontWeight:"normal",containerFontSize:14,containerFontFamily:'"Open Sans", sans-serif',containerFontWeight:"normal",external_containerFontSize:14,external_containerFontFamily:'"Open Sans", sans-serif',external_containerFontWeight:"normal",container_dbFontSize:14,container_dbFontFamily:'"Open Sans", sans-serif',container_dbFontWeight:"normal",external_container_dbFontSize:14,external_container_dbFontFamily:'"Open Sans", sans-serif',external_container_dbFontWeight:"normal",container_queueFontSize:14,container_queueFontFamily:'"Open Sans", sans-serif',container_queueFontWeight:"normal",external_container_queueFontSize:14,external_container_queueFontFamily:'"Open Sans", sans-serif',external_container_queueFontWeight:"normal",componentFontSize:14,componentFontFamily:'"Open Sans", sans-serif',componentFontWeight:"normal",external_componentFontSize:14,external_componentFontFamily:'"Open Sans", sans-serif',external_componentFontWeight:"normal",component_dbFontSize:14,component_dbFontFamily:'"Open Sans", sans-serif',component_dbFontWeight:"normal",external_component_dbFontSize:14,external_component_dbFontFamily:'"Open Sans", sans-serif',external_component_dbFontWeight:"normal",component_queueFontSize:14,component_queueFontFamily:'"Open Sans", sans-serif',component_queueFontWeight:"normal",external_component_queueFontSize:14,external_component_queueFontFamily:'"Open Sans", sans-serif',external_component_queueFontWeight:"normal",wrap:!0,wrapPadding:10,person_bg_color:"#08427B",person_border_color:"#073B6F",external_person_bg_color:"#686868",external_person_border_color:"#8A8A8A",system_bg_color:"#1168BD",system_border_color:"#3C7FC0",system_db_bg_color:"#1168BD",system_db_border_color:"#3C7FC0",system_queue_bg_color:"#1168BD",system_queue_border_color:"#3C7FC0",external_system_bg_color:"#999999",external_system_border_color:"#8A8A8A",external_system_db_bg_color:"#999999",external_system_db_border_color:"#8A8A8A",external_system_queue_bg_color:"#999999",external_system_queue_border_color:"#8A8A8A",container_bg_color:"#438DD5",container_border_color:"#3C7FC0",container_db_bg_color:"#438DD5",container_db_border_color:"#3C7FC0",container_queue_bg_color:"#438DD5",container_queue_border_color:"#3C7FC0",external_container_bg_color:"#B3B3B3",external_container_border_color:"#A6A6A6",external_container_db_bg_color:"#B3B3B3",external_container_db_border_color:"#A6A6A6",external_container_queue_bg_color:"#B3B3B3",external_container_queue_border_color:"#A6A6A6",component_bg_color:"#85BBF0",component_border_color:"#78A8D8",component_db_bg_color:"#85BBF0",component_db_border_color:"#78A8D8",component_queue_bg_color:"#85BBF0",component_queue_border_color:"#78A8D8",external_component_bg_color:"#CCCCCC",external_component_border_color:"#BFBFBF",external_component_db_bg_color:"#CCCCCC",external_component_db_border_color:"#BFBFBF",external_component_queue_bg_color:"#CCCCCC",external_component_queue_border_color:"#BFBFBF"},sankey:{useMaxWidth:!0,width:600,height:400,linkColor:"gradient",nodeAlignment:"justify",showValues:!0,prefix:"",suffix:""},block:{useMaxWidth:!0,padding:8},packet:{useMaxWidth:!0,rowHeight:32,bitWidth:32,bitsPerRow:32,showBits:!0,paddingX:5,paddingY:5},architecture:{useMaxWidth:!0,padding:40,iconSize:80,fontSize:16},radar:{useMaxWidth:!0,width:600,height:600,marginTop:50,marginRight:50,marginBottom:50,marginLeft:50,axisScaleFactor:1,axisLabelFactor:1.05,curveTension:.17},theme:"default",look:"classic",handDrawnSeed:0,layout:"dagre",maxTextSize:5e4,maxEdges:500,darkMode:!1,fontFamily:'"trebuchet ms", verdana, arial, sans-serif;',logLevel:5,securityLevel:"strict",startOnLoad:!0,arrowMarkerAbsolute:!1,secure:["secure","securityLevel","startOnLoad","maxTextSize","suppressErrorRendering","maxEdges"],legacyMathML:!1,forceLegacyMathML:!1,deterministicIds:!1,fontSize:16,markdownAutoWrap:!0,suppressErrorRendering:!1},yc={...ge,deterministicIDSeed:void 0,elk:{mergeEdges:!1,nodePlacementStrategy:"BRANDES_KOEPF",forceNodeModelOrder:!1,considerModelOrder:"NODES_AND_EDGES"},themeCSS:void 0,themeVariables:$e.default.getThemeVariables(),sequence:{...ge.sequence,messageFont:f(function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},"messageFont"),noteFont:f(function(){return{fontFamily:this.noteFontFamily,fontSize:this.noteFontSize,fontWeight:this.noteFontWeight}},"noteFont"),actorFont:f(function(){return{fontFamily:this.actorFontFamily,fontSize:this.actorFontSize,fontWeight:this.actorFontWeight}},"actorFont")},class:{hideEmptyMembersBox:!1},gantt:{...ge.gantt,tickInterval:void 0,useWidth:void 0},c4:{...ge.c4,useWidth:void 0,personFont:f(function(){return{fontFamily:this.personFontFamily,fontSize:this.personFontSize,fontWeight:this.personFontWeight}},"personFont"),flowchart:{...ge.flowchart,inheritDir:!1},external_personFont:f(function(){return{fontFamily:this.external_personFontFamily,fontSize:this.external_personFontSize,fontWeight:this.external_personFontWeight}},"external_personFont"),systemFont:f(function(){return{fontFamily:this.systemFontFamily,fontSize:this.systemFontSize,fontWeight:this.systemFontWeight}},"systemFont"),external_systemFont:f(function(){return{fontFamily:this.external_systemFontFamily,fontSize:this.external_systemFontSize,fontWeight:this.external_systemFontWeight}},"external_systemFont"),system_dbFont:f(function(){return{fontFamily:this.system_dbFontFamily,fontSize:this.system_dbFontSize,fontWeight:this.system_dbFontWeight}},"system_dbFont"),external_system_dbFont:f(function(){return{fontFamily:this.external_system_dbFontFamily,fontSize:this.external_system_dbFontSize,fontWeight:this.external_system_dbFontWeight}},"external_system_dbFont"),system_queueFont:f(function(){return{fontFamily:this.system_queueFontFamily,fontSize:this.system_queueFontSize,fontWeight:this.system_queueFontWeight}},"system_queueFont"),external_system_queueFont:f(function(){return{fontFamily:this.external_system_queueFontFamily,fontSize:this.external_system_queueFontSize,fontWeight:this.external_system_queueFontWeight}},"external_system_queueFont"),containerFont:f(function(){return{fontFamily:this.containerFontFamily,fontSize:this.containerFontSize,fontWeight:this.containerFontWeight}},"containerFont"),external_containerFont:f(function(){return{fontFamily:this.external_containerFontFamily,fontSize:this.external_containerFontSize,fontWeight:this.external_containerFontWeight}},"external_containerFont"),container_dbFont:f(function(){return{fontFamily:this.container_dbFontFamily,fontSize:this.container_dbFontSize,fontWeight:this.container_dbFontWeight}},"container_dbFont"),external_container_dbFont:f(function(){return{fontFamily:this.external_container_dbFontFamily,fontSize:this.external_container_dbFontSize,fontWeight:this.external_container_dbFontWeight}},"external_container_dbFont"),container_queueFont:f(function(){return{fontFamily:this.container_queueFontFamily,fontSize:this.container_queueFontSize,fontWeight:this.container_queueFontWeight}},"container_queueFont"),external_container_queueFont:f(function(){return{fontFamily:this.external_container_queueFontFamily,fontSize:this.external_container_queueFontSize,fontWeight:this.external_container_queueFontWeight}},"external_container_queueFont"),componentFont:f(function(){return{fontFamily:this.componentFontFamily,fontSize:this.componentFontSize,fontWeight:this.componentFontWeight}},"componentFont"),external_componentFont:f(function(){return{fontFamily:this.external_componentFontFamily,fontSize:this.external_componentFontSize,fontWeight:this.external_componentFontWeight}},"external_componentFont"),component_dbFont:f(function(){return{fontFamily:this.component_dbFontFamily,fontSize:this.component_dbFontSize,fontWeight:this.component_dbFontWeight}},"component_dbFont"),external_component_dbFont:f(function(){return{fontFamily:this.external_component_dbFontFamily,fontSize:this.external_component_dbFontSize,fontWeight:this.external_component_dbFontWeight}},"external_component_dbFont"),component_queueFont:f(function(){return{fontFamily:this.component_queueFontFamily,fontSize:this.component_queueFontSize,fontWeight:this.component_queueFontWeight}},"component_queueFont"),external_component_queueFont:f(function(){return{fontFamily:this.external_component_queueFontFamily,fontSize:this.external_component_queueFontSize,fontWeight:this.external_component_queueFontWeight}},"external_component_queueFont"),boundaryFont:f(function(){return{fontFamily:this.boundaryFontFamily,fontSize:this.boundaryFontSize,fontWeight:this.boundaryFontWeight}},"boundaryFont"),messageFont:f(function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},"messageFont")},pie:{...ge.pie,useWidth:984},xyChart:{...ge.xyChart,useWidth:void 0},requirement:{...ge.requirement,useWidth:void 0},packet:{...ge.packet},radar:{...ge.radar},treemap:{useMaxWidth:!0,padding:10,diagramPadding:8,showValues:!0,nodeWidth:100,nodeHeight:40,borderWidth:1,valueFontSize:12,labelFontSize:14,valueFormat:","}},xc=f((e,t="")=>Object.keys(e).reduce((r,i)=>Array.isArray(e[i])?r:typeof e[i]=="object"&&e[i]!==null?[...r,t+i,...xc(e[i],"")]:[...r,t+i],[]),"keyify"),am=new Set(xc(yc,"")),bc=yc,ha=f(e=>{if($.debug("sanitizeDirective called with",e),!(typeof e!="object"||e==null)){if(Array.isArray(e)){e.forEach(t=>ha(t));return}for(const t of Object.keys(e)){if($.debug("Checking key",t),t.startsWith("__")||t.includes("proto")||t.includes("constr")||!am.has(t)||e[t]==null){$.debug("sanitize deleting key: ",t),delete e[t];continue}if(typeof e[t]=="object"){$.debug("sanitizing object",t),ha(e[t]);continue}const r=["themeCSS","fontFamily","altFontFamily"];for(const i of r)t.includes(i)&&($.debug("sanitizing css option",t),e[t]=nm(e[t]))}if(e.themeVariables)for(const t of Object.keys(e.themeVariables)){const r=e.themeVariables[t];r!=null&&r.match&&!r.match(/^[\d "#%(),.;A-Za-z]+$/)&&(e.themeVariables[t]="")}$.debug("After sanitization",e)}},"sanitizeDirective"),nm=f(e=>{let t=0,r=0;for(const i of e){if(t<r)return"{ /* ERROR: Unbalanced CSS */ }";i==="{"?t++:i==="}"&&r++}return t!==r?"{ /* ERROR: Unbalanced CSS */ }":e},"sanitizeCss"),Sr=Object.freeze(bc),Kt=Ot({},Sr),Cc,Tr=[],oi=Ot({},Sr),Na=f((e,t)=>{let r=Ot({},e),i={};for(const a of t)vc(a),i=Ot(i,a);if(r=Ot(r,i),i.theme&&i.theme in $e){const a=Ot({},Cc),n=Ot(a.themeVariables||{},i.themeVariables);r.theme&&r.theme in $e&&(r.themeVariables=$e[r.theme].getThemeVariables(n))}return oi=r,Sc(oi),oi},"updateCurrentConfig"),sm=f(e=>(Kt=Ot({},Sr),Kt=Ot(Kt,e),e.theme&&$e[e.theme]&&(Kt.themeVariables=$e[e.theme].getThemeVariables(e.themeVariables)),Na(Kt,Tr),Kt),"setSiteConfig"),om=f(e=>{Cc=Ot({},e)},"saveConfigFromInitialize"),lm=f(e=>(Kt=Ot(Kt,e),Na(Kt,Tr),Kt),"updateSiteConfig"),kc=f(()=>Ot({},Kt),"getSiteConfig"),wc=f(e=>(Sc(e),Ot(oi,e),Nt()),"setConfig"),Nt=f(()=>Ot({},oi),"getConfig"),vc=f(e=>{e&&(["secure",...Kt.secure??[]].forEach(t=>{Object.hasOwn(e,t)&&($.debug(`Denied attempt to modify a secure key ${t}`,e[t]),delete e[t])}),Object.keys(e).forEach(t=>{t.startsWith("__")&&delete e[t]}),Object.keys(e).forEach(t=>{typeof e[t]=="string"&&(e[t].includes("<")||e[t].includes(">")||e[t].includes("url(data:"))&&delete e[t],typeof e[t]=="object"&&vc(e[t])}))},"sanitize"),cm=f(e=>{var t;ha(e),e.fontFamily&&!((t=e.themeVariables)!=null&&t.fontFamily)&&(e.themeVariables={...e.themeVariables,fontFamily:e.fontFamily}),Tr.push(e),Na(Kt,Tr)},"addDirective"),ua=f((e=Kt)=>{Tr=[],Na(e,Tr)},"reset"),hm={LAZY_LOAD_DEPRECATED:"The configuration options lazyLoadedDiagrams and loadExternalDiagramsAtStartup are deprecated. Please use registerExternalDiagrams instead."},Uo={},um=f(e=>{Uo[e]||($.warn(hm[e]),Uo[e]=!0)},"issueWarning"),Sc=f(e=>{e&&(e.lazyLoadedDiagrams||e.loadExternalDiagramsAtStartup)&&um("LAZY_LOAD_DEPRECATED")},"checkConfig"),Ai=/<br\s*\/?>/gi,dm=f(e=>e?Bc(e).replace(/\\n/g,"#br#").split("#br#"):[""],"getRows"),pm=(()=>{let e=!1;return()=>{e||(Tc(),e=!0)}})();function Tc(){const e="data-temp-href-target";vr.addHook("beforeSanitizeAttributes",t=>{t.tagName==="A"&&t.hasAttribute("target")&&t.setAttribute(e,t.getAttribute("target")??"")}),vr.addHook("afterSanitizeAttributes",t=>{t.tagName==="A"&&t.hasAttribute(e)&&(t.setAttribute("target",t.getAttribute(e)??""),t.removeAttribute(e),t.getAttribute("target")==="_blank"&&t.setAttribute("rel","noopener"))})}f(Tc,"setupDompurifyHooks");var _c=f(e=>(pm(),vr.sanitize(e)),"removeScript"),Go=f((e,t)=>{var r;if(((r=t.flowchart)==null?void 0:r.htmlLabels)!==!1){const i=t.securityLevel;i==="antiscript"||i==="strict"?e=_c(e):i!=="loose"&&(e=Bc(e),e=e.replace(/</g,"&lt;").replace(/>/g,"&gt;"),e=e.replace(/=/g,"&equals;"),e=ym(e))}return e},"sanitizeMore"),ne=f((e,t)=>e&&(t.dompurifyConfig?e=vr.sanitize(Go(e,t),t.dompurifyConfig).toString():e=vr.sanitize(Go(e,t),{FORBID_TAGS:["style"]}).toString(),e),"sanitizeText"),fm=f((e,t)=>typeof e=="string"?ne(e,t):e.flat().map(r=>ne(r,t)),"sanitizeTextOrArray"),gm=f(e=>Ai.test(e),"hasBreaks"),mm=f(e=>e.split(Ai),"splitBreaks"),ym=f(e=>e.replace(/#br#/g,"<br/>"),"placeholderToBreak"),Bc=f(e=>e.replace(Ai,"#br#"),"breakToPlaceholder"),Lc=f(e=>{let t="";return e&&(t=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,t=CSS.escape(t)),t},"getUrl"),At=f(e=>!(e===!1||["false","null","0"].includes(String(e).trim().toLowerCase())),"evaluate"),xm=f(function(...e){const t=e.filter(r=>!isNaN(r));return Math.max(...t)},"getMax"),bm=f(function(...e){const t=e.filter(r=>!isNaN(r));return Math.min(...t)},"getMin"),Vo=f(function(e){const t=e.split(/(,)/),r=[];for(let i=0;i<t.length;i++){let a=t[i];if(a===","&&i>0&&i+1<t.length){const n=t[i-1],o=t[i+1];Cm(n,o)&&(a=n+","+o,i++,r.pop())}r.push(km(a))}return r.join("")},"parseGenericTypes"),Pn=f((e,t)=>Math.max(0,e.split(t).length-1),"countOccurrence"),Cm=f((e,t)=>{const r=Pn(e,"~"),i=Pn(t,"~");return r===1&&i===1},"shouldCombineSets"),km=f(e=>{const t=Pn(e,"~");let r=!1;if(t<=1)return e;t%2!==0&&e.startsWith("~")&&(e=e.substring(1),r=!0);const i=[...e];let a=i.indexOf("~"),n=i.lastIndexOf("~");for(;a!==-1&&n!==-1&&a!==n;)i[a]="<",i[n]=">",a=i.indexOf("~"),n=i.lastIndexOf("~");return r&&i.unshift("~"),i.join("")},"processSet"),Xo=f(()=>window.MathMLElement!==void 0,"isMathMLSupported"),Nn=/\$\$(.*)\$\$/g,_r=f(e=>{var t;return(((t=e.match(Nn))==null?void 0:t.length)??0)>0},"hasKatex"),HS=f(async(e,t)=>{const r=document.createElement("div");r.innerHTML=await _s(e,t),r.id="katex-temp",r.style.visibility="hidden",r.style.position="absolute",r.style.top="0";const i=document.querySelector("body");i==null||i.insertAdjacentElement("beforeend",r);const a={width:r.clientWidth,height:r.clientHeight};return r.remove(),a},"calculateMathMLDimensions"),wm=f(async(e,t)=>{if(!_r(e))return e;if(!(Xo()||t.legacyMathML||t.forceLegacyMathML))return e.replace(Nn,"MathML is unsupported in this environment.");{const{default:r}=await gt(()=>import("./katex.ChWnQ-fc.js"),[],import.meta.url),i=t.forceLegacyMathML||!Xo()&&t.legacyMathML?"htmlAndMathml":"mathml";return e.split(Ai).map(a=>_r(a)?`<div style="display: flex; align-items: center; justify-content: center; white-space: nowrap;">${a}</div>`:`<div>${a}</div>`).join("").replace(Nn,(a,n)=>r.renderToString(n,{throwOnError:!0,displayMode:!0,output:i}).replace(/\n/g," ").replace(/<annotation.*<\/annotation>/g,""))}},"renderKatexUnsanitized"),_s=f(async(e,t)=>ne(await wm(e,t),t),"renderKatexSanitized"),Fr={getRows:dm,sanitizeText:ne,sanitizeTextOrArray:fm,hasBreaks:gm,splitBreaks:mm,lineBreakRegex:Ai,removeScript:_c,getUrl:Lc,evaluate:At,getMax:xm,getMin:bm},vm=f(function(e,t){for(let r of t)e.attr(r[0],r[1])},"d3Attrs"),Sm=f(function(e,t,r){let i=new Map;return r?(i.set("width","100%"),i.set("style",`max-width: ${t}px;`)):(i.set("height",e),i.set("width",t)),i},"calculateSvgSizeAttrs"),Ac=f(function(e,t,r,i){const a=Sm(t,r,i);vm(e,a)},"configureSvgSize"),Tm=f(function(e,t,r,i){const a=t.node().getBBox(),n=a.width,o=a.height;$.info(`SVG bounds: ${n}x${o}`,a);let s=0,c=0;$.info(`Graph bounds: ${s}x${c}`,e),s=n+r*2,c=o+r*2,$.info(`Calculated bounds: ${s}x${c}`),Ac(t,c,s,i);const l=`${a.x-r} ${a.y-r} ${a.width+2*r} ${a.height+2*r}`;t.attr("viewBox",l)},"setupGraphViewbox"),Ji={},_m=f((e,t,r)=>{let i="";return e in Ji&&Ji[e]?i=Ji[e](r):$.warn(`No theme found for ${e}`),` & {
    font-family: ${r.fontFamily};
    font-size: ${r.fontSize};
    fill: ${r.textColor}
  }
  @keyframes edge-animation-frame {
    from {
      stroke-dashoffset: 0;
    }
  }
  @keyframes dash {
    to {
      stroke-dashoffset: 0;
    }
  }
  & .edge-animation-slow {
    stroke-dasharray: 9,5 !important;
    stroke-dashoffset: 900;
    animation: dash 50s linear infinite;
    stroke-linecap: round;
  }
  & .edge-animation-fast {
    stroke-dasharray: 9,5 !important;
    stroke-dashoffset: 900;
    animation: dash 20s linear infinite;
    stroke-linecap: round;
  }
  /* Classes common for multiple diagrams */

  & .error-icon {
    fill: ${r.errorBkgColor};
  }
  & .error-text {
    fill: ${r.errorTextColor};
    stroke: ${r.errorTextColor};
  }

  & .edge-thickness-normal {
    stroke-width: 1px;
  }
  & .edge-thickness-thick {
    stroke-width: 3.5px
  }
  & .edge-pattern-solid {
    stroke-dasharray: 0;
  }
  & .edge-thickness-invisible {
    stroke-width: 0;
    fill: none;
  }
  & .edge-pattern-dashed{
    stroke-dasharray: 3;
  }
  .edge-pattern-dotted {
    stroke-dasharray: 2;
  }

  & .marker {
    fill: ${r.lineColor};
    stroke: ${r.lineColor};
  }
  & .marker.cross {
    stroke: ${r.lineColor};
  }

  & svg {
    font-family: ${r.fontFamily};
    font-size: ${r.fontSize};
  }
   & p {
    margin: 0
   }

  ${i}

  ${t}
`},"getStyles"),Bm=f((e,t)=>{t!==void 0&&(Ji[e]=t)},"addStylesForDiagram"),Lm=_m,Mc={};Yg(Mc,{clear:()=>Am,getAccDescription:()=>$m,getAccTitle:()=>Em,getDiagramTitle:()=>Dm,setAccDescription:()=>Fm,setAccTitle:()=>Mm,setDiagramTitle:()=>Om});var Bs="",Ls="",As="",Ms=f(e=>ne(e,Nt()),"sanitizeText"),Am=f(()=>{Bs="",As="",Ls=""},"clear"),Mm=f(e=>{Bs=Ms(e).replace(/^\s+/g,"")},"setAccTitle"),Em=f(()=>Bs,"getAccTitle"),Fm=f(e=>{As=Ms(e).replace(/\n\s+/g,`
`)},"setAccDescription"),$m=f(()=>As,"getAccDescription"),Om=f(e=>{Ls=Ms(e)},"setDiagramTitle"),Dm=f(()=>Ls,"getDiagramTitle"),Zo=$,Rm=Ss,ut=Nt,jS=wc,YS=Sr,Es=f(e=>ne(e,ut()),"sanitizeText"),Im=Tm,Pm=f(()=>Mc,"getCommonDb"),da={},pa=f((e,t,r)=>{var i;da[e]&&Zo.warn(`Diagram with id ${e} already registered. Overwriting.`),da[e]=t,r&&mc(e,r),Bm(e,t.styles),(i=t.injectUtils)==null||i.call(t,Zo,Rm,ut,Es,Im,Pm(),()=>{})},"registerDiagram"),zn=f(e=>{if(e in da)return da[e];throw new Nm(e)},"getDiagram"),_i,Nm=(_i=class extends Error{constructor(t){super(`Diagram ${t} not found.`)}},f(_i,"DiagramNotFoundError"),_i);function Fs(e){return typeof e>"u"||e===null}f(Fs,"isNothing");function Ec(e){return typeof e=="object"&&e!==null}f(Ec,"isObject");function Fc(e){return Array.isArray(e)?e:Fs(e)?[]:[e]}f(Fc,"toArray");function $c(e,t){var r,i,a,n;if(t)for(n=Object.keys(t),r=0,i=n.length;r<i;r+=1)a=n[r],e[a]=t[a];return e}f($c,"extend");function Oc(e,t){var r="",i;for(i=0;i<t;i+=1)r+=e;return r}f(Oc,"repeat");function Dc(e){return e===0&&Number.NEGATIVE_INFINITY===1/e}f(Dc,"isNegativeZero");var zm=Fs,Wm=Ec,qm=Fc,Hm=Oc,jm=Dc,Ym=$c,Lt={isNothing:zm,isObject:Wm,toArray:qm,repeat:Hm,isNegativeZero:jm,extend:Ym};function $s(e,t){var r="",i=e.reason||"(unknown reason)";return e.mark?(e.mark.name&&(r+='in "'+e.mark.name+'" '),r+="("+(e.mark.line+1)+":"+(e.mark.column+1)+")",!t&&e.mark.snippet&&(r+=`

`+e.mark.snippet),i+" "+r):i}f($s,"formatError");function Br(e,t){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=t,this.message=$s(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack||""}f(Br,"YAMLException$1");Br.prototype=Object.create(Error.prototype);Br.prototype.constructor=Br;Br.prototype.toString=f(function(t){return this.name+": "+$s(this,t)},"toString");var Qt=Br;function ta(e,t,r,i,a){var n="",o="",s=Math.floor(a/2)-1;return i-t>s&&(n=" ... ",t=i-s+n.length),r-i>s&&(o=" ...",r=i+s-o.length),{str:n+e.slice(t,r).replace(/\t/g,"→")+o,pos:i-t+n.length}}f(ta,"getLine");function ea(e,t){return Lt.repeat(" ",t-e.length)+e}f(ea,"padStart");function Rc(e,t){if(t=Object.create(t||null),!e.buffer)return null;t.maxLength||(t.maxLength=79),typeof t.indent!="number"&&(t.indent=1),typeof t.linesBefore!="number"&&(t.linesBefore=3),typeof t.linesAfter!="number"&&(t.linesAfter=2);for(var r=/\r?\n|\r|\0/g,i=[0],a=[],n,o=-1;n=r.exec(e.buffer);)a.push(n.index),i.push(n.index+n[0].length),e.position<=n.index&&o<0&&(o=i.length-2);o<0&&(o=i.length-1);var s="",c,l,h=Math.min(e.line+t.linesAfter,a.length).toString().length,u=t.maxLength-(t.indent+h+3);for(c=1;c<=t.linesBefore&&!(o-c<0);c++)l=ta(e.buffer,i[o-c],a[o-c],e.position-(i[o]-i[o-c]),u),s=Lt.repeat(" ",t.indent)+ea((e.line-c+1).toString(),h)+" | "+l.str+`
`+s;for(l=ta(e.buffer,i[o],a[o],e.position,u),s+=Lt.repeat(" ",t.indent)+ea((e.line+1).toString(),h)+" | "+l.str+`
`,s+=Lt.repeat("-",t.indent+h+3+l.pos)+`^
`,c=1;c<=t.linesAfter&&!(o+c>=a.length);c++)l=ta(e.buffer,i[o+c],a[o+c],e.position-(i[o]-i[o+c]),u),s+=Lt.repeat(" ",t.indent)+ea((e.line+c+1).toString(),h)+" | "+l.str+`
`;return s.replace(/\n$/,"")}f(Rc,"makeSnippet");var Um=Rc,Gm=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],Vm=["scalar","sequence","mapping"];function Ic(e){var t={};return e!==null&&Object.keys(e).forEach(function(r){e[r].forEach(function(i){t[String(i)]=r})}),t}f(Ic,"compileStyleAliases");function Pc(e,t){if(t=t||{},Object.keys(t).forEach(function(r){if(Gm.indexOf(r)===-1)throw new Qt('Unknown option "'+r+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(r){return r},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.representName=t.representName||null,this.defaultStyle=t.defaultStyle||null,this.multi=t.multi||!1,this.styleAliases=Ic(t.styleAliases||null),Vm.indexOf(this.kind)===-1)throw new Qt('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}f(Pc,"Type$1");var zt=Pc;function Wn(e,t){var r=[];return e[t].forEach(function(i){var a=r.length;r.forEach(function(n,o){n.tag===i.tag&&n.kind===i.kind&&n.multi===i.multi&&(a=o)}),r[a]=i}),r}f(Wn,"compileList");function Nc(){var e={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}},t,r;function i(a){a.multi?(e.multi[a.kind].push(a),e.multi.fallback.push(a)):e[a.kind][a.tag]=e.fallback[a.tag]=a}for(f(i,"collectType"),t=0,r=arguments.length;t<r;t+=1)arguments[t].forEach(i);return e}f(Nc,"compileMap");function fa(e){return this.extend(e)}f(fa,"Schema$1");fa.prototype.extend=f(function(t){var r=[],i=[];if(t instanceof zt)i.push(t);else if(Array.isArray(t))i=i.concat(t);else if(t&&(Array.isArray(t.implicit)||Array.isArray(t.explicit)))t.implicit&&(r=r.concat(t.implicit)),t.explicit&&(i=i.concat(t.explicit));else throw new Qt("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");r.forEach(function(n){if(!(n instanceof zt))throw new Qt("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(n.loadKind&&n.loadKind!=="scalar")throw new Qt("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(n.multi)throw new Qt("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}),i.forEach(function(n){if(!(n instanceof zt))throw new Qt("Specified list of YAML types (or a single Type object) contains a non-Type object.")});var a=Object.create(fa.prototype);return a.implicit=(this.implicit||[]).concat(r),a.explicit=(this.explicit||[]).concat(i),a.compiledImplicit=Wn(a,"implicit"),a.compiledExplicit=Wn(a,"explicit"),a.compiledTypeMap=Nc(a.compiledImplicit,a.compiledExplicit),a},"extend");var Xm=fa,Zm=new zt("tag:yaml.org,2002:str",{kind:"scalar",construct:f(function(e){return e!==null?e:""},"construct")}),Km=new zt("tag:yaml.org,2002:seq",{kind:"sequence",construct:f(function(e){return e!==null?e:[]},"construct")}),Qm=new zt("tag:yaml.org,2002:map",{kind:"mapping",construct:f(function(e){return e!==null?e:{}},"construct")}),Jm=new Xm({explicit:[Zm,Km,Qm]});function zc(e){if(e===null)return!0;var t=e.length;return t===1&&e==="~"||t===4&&(e==="null"||e==="Null"||e==="NULL")}f(zc,"resolveYamlNull");function Wc(){return null}f(Wc,"constructYamlNull");function qc(e){return e===null}f(qc,"isNull");var ty=new zt("tag:yaml.org,2002:null",{kind:"scalar",resolve:zc,construct:Wc,predicate:qc,represent:{canonical:f(function(){return"~"},"canonical"),lowercase:f(function(){return"null"},"lowercase"),uppercase:f(function(){return"NULL"},"uppercase"),camelcase:f(function(){return"Null"},"camelcase"),empty:f(function(){return""},"empty")},defaultStyle:"lowercase"});function Hc(e){if(e===null)return!1;var t=e.length;return t===4&&(e==="true"||e==="True"||e==="TRUE")||t===5&&(e==="false"||e==="False"||e==="FALSE")}f(Hc,"resolveYamlBoolean");function jc(e){return e==="true"||e==="True"||e==="TRUE"}f(jc,"constructYamlBoolean");function Yc(e){return Object.prototype.toString.call(e)==="[object Boolean]"}f(Yc,"isBoolean");var ey=new zt("tag:yaml.org,2002:bool",{kind:"scalar",resolve:Hc,construct:jc,predicate:Yc,represent:{lowercase:f(function(e){return e?"true":"false"},"lowercase"),uppercase:f(function(e){return e?"TRUE":"FALSE"},"uppercase"),camelcase:f(function(e){return e?"True":"False"},"camelcase")},defaultStyle:"lowercase"});function Uc(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}f(Uc,"isHexCode");function Gc(e){return 48<=e&&e<=55}f(Gc,"isOctCode");function Vc(e){return 48<=e&&e<=57}f(Vc,"isDecCode");function Xc(e){if(e===null)return!1;var t=e.length,r=0,i=!1,a;if(!t)return!1;if(a=e[r],(a==="-"||a==="+")&&(a=e[++r]),a==="0"){if(r+1===t)return!0;if(a=e[++r],a==="b"){for(r++;r<t;r++)if(a=e[r],a!=="_"){if(a!=="0"&&a!=="1")return!1;i=!0}return i&&a!=="_"}if(a==="x"){for(r++;r<t;r++)if(a=e[r],a!=="_"){if(!Uc(e.charCodeAt(r)))return!1;i=!0}return i&&a!=="_"}if(a==="o"){for(r++;r<t;r++)if(a=e[r],a!=="_"){if(!Gc(e.charCodeAt(r)))return!1;i=!0}return i&&a!=="_"}}if(a==="_")return!1;for(;r<t;r++)if(a=e[r],a!=="_"){if(!Vc(e.charCodeAt(r)))return!1;i=!0}return!(!i||a==="_")}f(Xc,"resolveYamlInteger");function Zc(e){var t=e,r=1,i;if(t.indexOf("_")!==-1&&(t=t.replace(/_/g,"")),i=t[0],(i==="-"||i==="+")&&(i==="-"&&(r=-1),t=t.slice(1),i=t[0]),t==="0")return 0;if(i==="0"){if(t[1]==="b")return r*parseInt(t.slice(2),2);if(t[1]==="x")return r*parseInt(t.slice(2),16);if(t[1]==="o")return r*parseInt(t.slice(2),8)}return r*parseInt(t,10)}f(Zc,"constructYamlInteger");function Kc(e){return Object.prototype.toString.call(e)==="[object Number]"&&e%1===0&&!Lt.isNegativeZero(e)}f(Kc,"isInteger");var ry=new zt("tag:yaml.org,2002:int",{kind:"scalar",resolve:Xc,construct:Zc,predicate:Kc,represent:{binary:f(function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},"binary"),octal:f(function(e){return e>=0?"0o"+e.toString(8):"-0o"+e.toString(8).slice(1)},"octal"),decimal:f(function(e){return e.toString(10)},"decimal"),hexadecimal:f(function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)},"hexadecimal")},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),iy=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function Qc(e){return!(e===null||!iy.test(e)||e[e.length-1]==="_")}f(Qc,"resolveYamlFloat");function Jc(e){var t,r;return t=e.replace(/_/g,"").toLowerCase(),r=t[0]==="-"?-1:1,"+-".indexOf(t[0])>=0&&(t=t.slice(1)),t===".inf"?r===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:t===".nan"?NaN:r*parseFloat(t,10)}f(Jc,"constructYamlFloat");var ay=/^[-+]?[0-9]+e/;function th(e,t){var r;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(Lt.isNegativeZero(e))return"-0.0";return r=e.toString(10),ay.test(r)?r.replace("e",".e"):r}f(th,"representYamlFloat");function eh(e){return Object.prototype.toString.call(e)==="[object Number]"&&(e%1!==0||Lt.isNegativeZero(e))}f(eh,"isFloat");var ny=new zt("tag:yaml.org,2002:float",{kind:"scalar",resolve:Qc,construct:Jc,predicate:eh,represent:th,defaultStyle:"lowercase"}),rh=Jm.extend({implicit:[ty,ey,ry,ny]}),sy=rh,ih=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),ah=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function nh(e){return e===null?!1:ih.exec(e)!==null||ah.exec(e)!==null}f(nh,"resolveYamlTimestamp");function sh(e){var t,r,i,a,n,o,s,c=0,l=null,h,u,p;if(t=ih.exec(e),t===null&&(t=ah.exec(e)),t===null)throw new Error("Date resolve error");if(r=+t[1],i=+t[2]-1,a=+t[3],!t[4])return new Date(Date.UTC(r,i,a));if(n=+t[4],o=+t[5],s=+t[6],t[7]){for(c=t[7].slice(0,3);c.length<3;)c+="0";c=+c}return t[9]&&(h=+t[10],u=+(t[11]||0),l=(h*60+u)*6e4,t[9]==="-"&&(l=-l)),p=new Date(Date.UTC(r,i,a,n,o,s,c)),l&&p.setTime(p.getTime()-l),p}f(sh,"constructYamlTimestamp");function oh(e){return e.toISOString()}f(oh,"representYamlTimestamp");var oy=new zt("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:nh,construct:sh,instanceOf:Date,represent:oh});function lh(e){return e==="<<"||e===null}f(lh,"resolveYamlMerge");var ly=new zt("tag:yaml.org,2002:merge",{kind:"scalar",resolve:lh}),Os=`ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=
\r`;function ch(e){if(e===null)return!1;var t,r,i=0,a=e.length,n=Os;for(r=0;r<a;r++)if(t=n.indexOf(e.charAt(r)),!(t>64)){if(t<0)return!1;i+=6}return i%8===0}f(ch,"resolveYamlBinary");function hh(e){var t,r,i=e.replace(/[\r\n=]/g,""),a=i.length,n=Os,o=0,s=[];for(t=0;t<a;t++)t%4===0&&t&&(s.push(o>>16&255),s.push(o>>8&255),s.push(o&255)),o=o<<6|n.indexOf(i.charAt(t));return r=a%4*6,r===0?(s.push(o>>16&255),s.push(o>>8&255),s.push(o&255)):r===18?(s.push(o>>10&255),s.push(o>>2&255)):r===12&&s.push(o>>4&255),new Uint8Array(s)}f(hh,"constructYamlBinary");function uh(e){var t="",r=0,i,a,n=e.length,o=Os;for(i=0;i<n;i++)i%3===0&&i&&(t+=o[r>>18&63],t+=o[r>>12&63],t+=o[r>>6&63],t+=o[r&63]),r=(r<<8)+e[i];return a=n%3,a===0?(t+=o[r>>18&63],t+=o[r>>12&63],t+=o[r>>6&63],t+=o[r&63]):a===2?(t+=o[r>>10&63],t+=o[r>>4&63],t+=o[r<<2&63],t+=o[64]):a===1&&(t+=o[r>>2&63],t+=o[r<<4&63],t+=o[64],t+=o[64]),t}f(uh,"representYamlBinary");function dh(e){return Object.prototype.toString.call(e)==="[object Uint8Array]"}f(dh,"isBinary");var cy=new zt("tag:yaml.org,2002:binary",{kind:"scalar",resolve:ch,construct:hh,predicate:dh,represent:uh}),hy=Object.prototype.hasOwnProperty,uy=Object.prototype.toString;function ph(e){if(e===null)return!0;var t=[],r,i,a,n,o,s=e;for(r=0,i=s.length;r<i;r+=1){if(a=s[r],o=!1,uy.call(a)!=="[object Object]")return!1;for(n in a)if(hy.call(a,n))if(!o)o=!0;else return!1;if(!o)return!1;if(t.indexOf(n)===-1)t.push(n);else return!1}return!0}f(ph,"resolveYamlOmap");function fh(e){return e!==null?e:[]}f(fh,"constructYamlOmap");var dy=new zt("tag:yaml.org,2002:omap",{kind:"sequence",resolve:ph,construct:fh}),py=Object.prototype.toString;function gh(e){if(e===null)return!0;var t,r,i,a,n,o=e;for(n=new Array(o.length),t=0,r=o.length;t<r;t+=1){if(i=o[t],py.call(i)!=="[object Object]"||(a=Object.keys(i),a.length!==1))return!1;n[t]=[a[0],i[a[0]]]}return!0}f(gh,"resolveYamlPairs");function mh(e){if(e===null)return[];var t,r,i,a,n,o=e;for(n=new Array(o.length),t=0,r=o.length;t<r;t+=1)i=o[t],a=Object.keys(i),n[t]=[a[0],i[a[0]]];return n}f(mh,"constructYamlPairs");var fy=new zt("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:gh,construct:mh}),gy=Object.prototype.hasOwnProperty;function yh(e){if(e===null)return!0;var t,r=e;for(t in r)if(gy.call(r,t)&&r[t]!==null)return!1;return!0}f(yh,"resolveYamlSet");function xh(e){return e!==null?e:{}}f(xh,"constructYamlSet");var my=new zt("tag:yaml.org,2002:set",{kind:"mapping",resolve:yh,construct:xh}),bh=sy.extend({implicit:[oy,ly],explicit:[cy,dy,fy,my]}),We=Object.prototype.hasOwnProperty,ga=1,Ch=2,kh=3,ma=4,Cn=1,yy=2,Ko=3,xy=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,by=/[\x85\u2028\u2029]/,Cy=/[,\[\]\{\}]/,wh=/^(?:!|!!|![a-z\-]+!)$/i,vh=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function qn(e){return Object.prototype.toString.call(e)}f(qn,"_class");function he(e){return e===10||e===13}f(he,"is_EOL");function ze(e){return e===9||e===32}f(ze,"is_WHITE_SPACE");function Yt(e){return e===9||e===32||e===10||e===13}f(Yt,"is_WS_OR_EOL");function Qe(e){return e===44||e===91||e===93||e===123||e===125}f(Qe,"is_FLOW_INDICATOR");function Sh(e){var t;return 48<=e&&e<=57?e-48:(t=e|32,97<=t&&t<=102?t-97+10:-1)}f(Sh,"fromHexCode");function Th(e){return e===120?2:e===117?4:e===85?8:0}f(Th,"escapedHexLen");function _h(e){return 48<=e&&e<=57?e-48:-1}f(_h,"fromDecimalCode");function Hn(e){return e===48?"\0":e===97?"\x07":e===98?"\b":e===116||e===9?"	":e===110?`
`:e===118?"\v":e===102?"\f":e===114?"\r":e===101?"\x1B":e===32?" ":e===34?'"':e===47?"/":e===92?"\\":e===78?"":e===95?" ":e===76?"\u2028":e===80?"\u2029":""}f(Hn,"simpleEscapeSequence");function Bh(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}f(Bh,"charFromCodepoint");var Lh=new Array(256),Ah=new Array(256);for(Ve=0;Ve<256;Ve++)Lh[Ve]=Hn(Ve)?1:0,Ah[Ve]=Hn(Ve);var Ve;function Mh(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||bh,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}f(Mh,"State$1");function Ds(e,t){var r={name:e.filename,buffer:e.input.slice(0,-1),position:e.position,line:e.line,column:e.position-e.lineStart};return r.snippet=Um(r),new Qt(t,r)}f(Ds,"generateError");function K(e,t){throw Ds(e,t)}f(K,"throwError");function ui(e,t){e.onWarning&&e.onWarning.call(null,Ds(e,t))}f(ui,"throwWarning");var Qo={YAML:f(function(t,r,i){var a,n,o;t.version!==null&&K(t,"duplication of %YAML directive"),i.length!==1&&K(t,"YAML directive accepts exactly one argument"),a=/^([0-9]+)\.([0-9]+)$/.exec(i[0]),a===null&&K(t,"ill-formed argument of the YAML directive"),n=parseInt(a[1],10),o=parseInt(a[2],10),n!==1&&K(t,"unacceptable YAML version of the document"),t.version=i[0],t.checkLineBreaks=o<2,o!==1&&o!==2&&ui(t,"unsupported YAML version of the document")},"handleYamlDirective"),TAG:f(function(t,r,i){var a,n;i.length!==2&&K(t,"TAG directive accepts exactly two arguments"),a=i[0],n=i[1],wh.test(a)||K(t,"ill-formed tag handle (first argument) of the TAG directive"),We.call(t.tagMap,a)&&K(t,'there is a previously declared suffix for "'+a+'" tag handle'),vh.test(n)||K(t,"ill-formed tag prefix (second argument) of the TAG directive");try{n=decodeURIComponent(n)}catch{K(t,"tag prefix is malformed: "+n)}t.tagMap[a]=n},"handleTagDirective")};function Oe(e,t,r,i){var a,n,o,s;if(t<r){if(s=e.input.slice(t,r),i)for(a=0,n=s.length;a<n;a+=1)o=s.charCodeAt(a),o===9||32<=o&&o<=1114111||K(e,"expected valid JSON character");else xy.test(s)&&K(e,"the stream contains non-printable characters");e.result+=s}}f(Oe,"captureSegment");function jn(e,t,r,i){var a,n,o,s;for(Lt.isObject(r)||K(e,"cannot merge mappings; the provided source object is unacceptable"),a=Object.keys(r),o=0,s=a.length;o<s;o+=1)n=a[o],We.call(t,n)||(t[n]=r[n],i[n]=!0)}f(jn,"mergeMappings");function Je(e,t,r,i,a,n,o,s,c){var l,h;if(Array.isArray(a))for(a=Array.prototype.slice.call(a),l=0,h=a.length;l<h;l+=1)Array.isArray(a[l])&&K(e,"nested arrays are not supported inside keys"),typeof a=="object"&&qn(a[l])==="[object Object]"&&(a[l]="[object Object]");if(typeof a=="object"&&qn(a)==="[object Object]"&&(a="[object Object]"),a=String(a),t===null&&(t={}),i==="tag:yaml.org,2002:merge")if(Array.isArray(n))for(l=0,h=n.length;l<h;l+=1)jn(e,t,n[l],r);else jn(e,t,n,r);else!e.json&&!We.call(r,a)&&We.call(t,a)&&(e.line=o||e.line,e.lineStart=s||e.lineStart,e.position=c||e.position,K(e,"duplicated mapping key")),a==="__proto__"?Object.defineProperty(t,a,{configurable:!0,enumerable:!0,writable:!0,value:n}):t[a]=n,delete r[a];return t}f(Je,"storeMappingPair");function za(e){var t;t=e.input.charCodeAt(e.position),t===10?e.position++:t===13?(e.position++,e.input.charCodeAt(e.position)===10&&e.position++):K(e,"a line break is expected"),e.line+=1,e.lineStart=e.position,e.firstTabInLine=-1}f(za,"readLineBreak");function St(e,t,r){for(var i=0,a=e.input.charCodeAt(e.position);a!==0;){for(;ze(a);)a===9&&e.firstTabInLine===-1&&(e.firstTabInLine=e.position),a=e.input.charCodeAt(++e.position);if(t&&a===35)do a=e.input.charCodeAt(++e.position);while(a!==10&&a!==13&&a!==0);if(he(a))for(za(e),a=e.input.charCodeAt(e.position),i++,e.lineIndent=0;a===32;)e.lineIndent++,a=e.input.charCodeAt(++e.position);else break}return r!==-1&&i!==0&&e.lineIndent<r&&ui(e,"deficient indentation"),i}f(St,"skipSeparationSpace");function Mi(e){var t=e.position,r;return r=e.input.charCodeAt(t),!!((r===45||r===46)&&r===e.input.charCodeAt(t+1)&&r===e.input.charCodeAt(t+2)&&(t+=3,r=e.input.charCodeAt(t),r===0||Yt(r)))}f(Mi,"testDocumentSeparator");function Wa(e,t){t===1?e.result+=" ":t>1&&(e.result+=Lt.repeat(`
`,t-1))}f(Wa,"writeFoldedLines");function Eh(e,t,r){var i,a,n,o,s,c,l,h,u=e.kind,p=e.result,d;if(d=e.input.charCodeAt(e.position),Yt(d)||Qe(d)||d===35||d===38||d===42||d===33||d===124||d===62||d===39||d===34||d===37||d===64||d===96||(d===63||d===45)&&(a=e.input.charCodeAt(e.position+1),Yt(a)||r&&Qe(a)))return!1;for(e.kind="scalar",e.result="",n=o=e.position,s=!1;d!==0;){if(d===58){if(a=e.input.charCodeAt(e.position+1),Yt(a)||r&&Qe(a))break}else if(d===35){if(i=e.input.charCodeAt(e.position-1),Yt(i))break}else{if(e.position===e.lineStart&&Mi(e)||r&&Qe(d))break;if(he(d))if(c=e.line,l=e.lineStart,h=e.lineIndent,St(e,!1,-1),e.lineIndent>=t){s=!0,d=e.input.charCodeAt(e.position);continue}else{e.position=o,e.line=c,e.lineStart=l,e.lineIndent=h;break}}s&&(Oe(e,n,o,!1),Wa(e,e.line-c),n=o=e.position,s=!1),ze(d)||(o=e.position+1),d=e.input.charCodeAt(++e.position)}return Oe(e,n,o,!1),e.result?!0:(e.kind=u,e.result=p,!1)}f(Eh,"readPlainScalar");function Fh(e,t){var r,i,a;if(r=e.input.charCodeAt(e.position),r!==39)return!1;for(e.kind="scalar",e.result="",e.position++,i=a=e.position;(r=e.input.charCodeAt(e.position))!==0;)if(r===39)if(Oe(e,i,e.position,!0),r=e.input.charCodeAt(++e.position),r===39)i=e.position,e.position++,a=e.position;else return!0;else he(r)?(Oe(e,i,a,!0),Wa(e,St(e,!1,t)),i=a=e.position):e.position===e.lineStart&&Mi(e)?K(e,"unexpected end of the document within a single quoted scalar"):(e.position++,a=e.position);K(e,"unexpected end of the stream within a single quoted scalar")}f(Fh,"readSingleQuotedScalar");function $h(e,t){var r,i,a,n,o,s;if(s=e.input.charCodeAt(e.position),s!==34)return!1;for(e.kind="scalar",e.result="",e.position++,r=i=e.position;(s=e.input.charCodeAt(e.position))!==0;){if(s===34)return Oe(e,r,e.position,!0),e.position++,!0;if(s===92){if(Oe(e,r,e.position,!0),s=e.input.charCodeAt(++e.position),he(s))St(e,!1,t);else if(s<256&&Lh[s])e.result+=Ah[s],e.position++;else if((o=Th(s))>0){for(a=o,n=0;a>0;a--)s=e.input.charCodeAt(++e.position),(o=Sh(s))>=0?n=(n<<4)+o:K(e,"expected hexadecimal character");e.result+=Bh(n),e.position++}else K(e,"unknown escape sequence");r=i=e.position}else he(s)?(Oe(e,r,i,!0),Wa(e,St(e,!1,t)),r=i=e.position):e.position===e.lineStart&&Mi(e)?K(e,"unexpected end of the document within a double quoted scalar"):(e.position++,i=e.position)}K(e,"unexpected end of the stream within a double quoted scalar")}f($h,"readDoubleQuotedScalar");function Oh(e,t){var r=!0,i,a,n,o=e.tag,s,c=e.anchor,l,h,u,p,d,g=Object.create(null),m,y,x,b;if(b=e.input.charCodeAt(e.position),b===91)h=93,d=!1,s=[];else if(b===123)h=125,d=!0,s={};else return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=s),b=e.input.charCodeAt(++e.position);b!==0;){if(St(e,!0,t),b=e.input.charCodeAt(e.position),b===h)return e.position++,e.tag=o,e.anchor=c,e.kind=d?"mapping":"sequence",e.result=s,!0;r?b===44&&K(e,"expected the node content, but found ','"):K(e,"missed comma between flow collection entries"),y=m=x=null,u=p=!1,b===63&&(l=e.input.charCodeAt(e.position+1),Yt(l)&&(u=p=!0,e.position++,St(e,!0,t))),i=e.line,a=e.lineStart,n=e.position,rr(e,t,ga,!1,!0),y=e.tag,m=e.result,St(e,!0,t),b=e.input.charCodeAt(e.position),(p||e.line===i)&&b===58&&(u=!0,b=e.input.charCodeAt(++e.position),St(e,!0,t),rr(e,t,ga,!1,!0),x=e.result),d?Je(e,s,g,y,m,x,i,a,n):u?s.push(Je(e,null,g,y,m,x,i,a,n)):s.push(m),St(e,!0,t),b=e.input.charCodeAt(e.position),b===44?(r=!0,b=e.input.charCodeAt(++e.position)):r=!1}K(e,"unexpected end of the stream within a flow collection")}f(Oh,"readFlowCollection");function Dh(e,t){var r,i,a=Cn,n=!1,o=!1,s=t,c=0,l=!1,h,u;if(u=e.input.charCodeAt(e.position),u===124)i=!1;else if(u===62)i=!0;else return!1;for(e.kind="scalar",e.result="";u!==0;)if(u=e.input.charCodeAt(++e.position),u===43||u===45)Cn===a?a=u===43?Ko:yy:K(e,"repeat of a chomping mode identifier");else if((h=_h(u))>=0)h===0?K(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):o?K(e,"repeat of an indentation width identifier"):(s=t+h-1,o=!0);else break;if(ze(u)){do u=e.input.charCodeAt(++e.position);while(ze(u));if(u===35)do u=e.input.charCodeAt(++e.position);while(!he(u)&&u!==0)}for(;u!==0;){for(za(e),e.lineIndent=0,u=e.input.charCodeAt(e.position);(!o||e.lineIndent<s)&&u===32;)e.lineIndent++,u=e.input.charCodeAt(++e.position);if(!o&&e.lineIndent>s&&(s=e.lineIndent),he(u)){c++;continue}if(e.lineIndent<s){a===Ko?e.result+=Lt.repeat(`
`,n?1+c:c):a===Cn&&n&&(e.result+=`
`);break}for(i?ze(u)?(l=!0,e.result+=Lt.repeat(`
`,n?1+c:c)):l?(l=!1,e.result+=Lt.repeat(`
`,c+1)):c===0?n&&(e.result+=" "):e.result+=Lt.repeat(`
`,c):e.result+=Lt.repeat(`
`,n?1+c:c),n=!0,o=!0,c=0,r=e.position;!he(u)&&u!==0;)u=e.input.charCodeAt(++e.position);Oe(e,r,e.position,!1)}return!0}f(Dh,"readBlockScalar");function Yn(e,t){var r,i=e.tag,a=e.anchor,n=[],o,s=!1,c;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=n),c=e.input.charCodeAt(e.position);c!==0&&(e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,K(e,"tab characters must not be used in indentation")),!(c!==45||(o=e.input.charCodeAt(e.position+1),!Yt(o))));){if(s=!0,e.position++,St(e,!0,-1)&&e.lineIndent<=t){n.push(null),c=e.input.charCodeAt(e.position);continue}if(r=e.line,rr(e,t,kh,!1,!0),n.push(e.result),St(e,!0,-1),c=e.input.charCodeAt(e.position),(e.line===r||e.lineIndent>t)&&c!==0)K(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break}return s?(e.tag=i,e.anchor=a,e.kind="sequence",e.result=n,!0):!1}f(Yn,"readBlockSequence");function Rh(e,t,r){var i,a,n,o,s,c,l=e.tag,h=e.anchor,u={},p=Object.create(null),d=null,g=null,m=null,y=!1,x=!1,b;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=u),b=e.input.charCodeAt(e.position);b!==0;){if(!y&&e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,K(e,"tab characters must not be used in indentation")),i=e.input.charCodeAt(e.position+1),n=e.line,(b===63||b===58)&&Yt(i))b===63?(y&&(Je(e,u,p,d,g,null,o,s,c),d=g=m=null),x=!0,y=!0,a=!0):y?(y=!1,a=!0):K(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,b=i;else{if(o=e.line,s=e.lineStart,c=e.position,!rr(e,r,Ch,!1,!0))break;if(e.line===n){for(b=e.input.charCodeAt(e.position);ze(b);)b=e.input.charCodeAt(++e.position);if(b===58)b=e.input.charCodeAt(++e.position),Yt(b)||K(e,"a whitespace character is expected after the key-value separator within a block mapping"),y&&(Je(e,u,p,d,g,null,o,s,c),d=g=m=null),x=!0,y=!1,a=!1,d=e.tag,g=e.result;else if(x)K(e,"can not read an implicit mapping pair; a colon is missed");else return e.tag=l,e.anchor=h,!0}else if(x)K(e,"can not read a block mapping entry; a multiline key may not be an implicit key");else return e.tag=l,e.anchor=h,!0}if((e.line===n||e.lineIndent>t)&&(y&&(o=e.line,s=e.lineStart,c=e.position),rr(e,t,ma,!0,a)&&(y?g=e.result:m=e.result),y||(Je(e,u,p,d,g,m,o,s,c),d=g=m=null),St(e,!0,-1),b=e.input.charCodeAt(e.position)),(e.line===n||e.lineIndent>t)&&b!==0)K(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return y&&Je(e,u,p,d,g,null,o,s,c),x&&(e.tag=l,e.anchor=h,e.kind="mapping",e.result=u),x}f(Rh,"readBlockMapping");function Ih(e){var t,r=!1,i=!1,a,n,o;if(o=e.input.charCodeAt(e.position),o!==33)return!1;if(e.tag!==null&&K(e,"duplication of a tag property"),o=e.input.charCodeAt(++e.position),o===60?(r=!0,o=e.input.charCodeAt(++e.position)):o===33?(i=!0,a="!!",o=e.input.charCodeAt(++e.position)):a="!",t=e.position,r){do o=e.input.charCodeAt(++e.position);while(o!==0&&o!==62);e.position<e.length?(n=e.input.slice(t,e.position),o=e.input.charCodeAt(++e.position)):K(e,"unexpected end of the stream within a verbatim tag")}else{for(;o!==0&&!Yt(o);)o===33&&(i?K(e,"tag suffix cannot contain exclamation marks"):(a=e.input.slice(t-1,e.position+1),wh.test(a)||K(e,"named tag handle cannot contain such characters"),i=!0,t=e.position+1)),o=e.input.charCodeAt(++e.position);n=e.input.slice(t,e.position),Cy.test(n)&&K(e,"tag suffix cannot contain flow indicator characters")}n&&!vh.test(n)&&K(e,"tag name cannot contain such characters: "+n);try{n=decodeURIComponent(n)}catch{K(e,"tag name is malformed: "+n)}return r?e.tag=n:We.call(e.tagMap,a)?e.tag=e.tagMap[a]+n:a==="!"?e.tag="!"+n:a==="!!"?e.tag="tag:yaml.org,2002:"+n:K(e,'undeclared tag handle "'+a+'"'),!0}f(Ih,"readTagProperty");function Ph(e){var t,r;if(r=e.input.charCodeAt(e.position),r!==38)return!1;for(e.anchor!==null&&K(e,"duplication of an anchor property"),r=e.input.charCodeAt(++e.position),t=e.position;r!==0&&!Yt(r)&&!Qe(r);)r=e.input.charCodeAt(++e.position);return e.position===t&&K(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}f(Ph,"readAnchorProperty");function Nh(e){var t,r,i;if(i=e.input.charCodeAt(e.position),i!==42)return!1;for(i=e.input.charCodeAt(++e.position),t=e.position;i!==0&&!Yt(i)&&!Qe(i);)i=e.input.charCodeAt(++e.position);return e.position===t&&K(e,"name of an alias node must contain at least one character"),r=e.input.slice(t,e.position),We.call(e.anchorMap,r)||K(e,'unidentified alias "'+r+'"'),e.result=e.anchorMap[r],St(e,!0,-1),!0}f(Nh,"readAlias");function rr(e,t,r,i,a){var n,o,s,c=1,l=!1,h=!1,u,p,d,g,m,y;if(e.listener!==null&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,n=o=s=ma===r||kh===r,i&&St(e,!0,-1)&&(l=!0,e.lineIndent>t?c=1:e.lineIndent===t?c=0:e.lineIndent<t&&(c=-1)),c===1)for(;Ih(e)||Ph(e);)St(e,!0,-1)?(l=!0,s=n,e.lineIndent>t?c=1:e.lineIndent===t?c=0:e.lineIndent<t&&(c=-1)):s=!1;if(s&&(s=l||a),(c===1||ma===r)&&(ga===r||Ch===r?m=t:m=t+1,y=e.position-e.lineStart,c===1?s&&(Yn(e,y)||Rh(e,y,m))||Oh(e,m)?h=!0:(o&&Dh(e,m)||Fh(e,m)||$h(e,m)?h=!0:Nh(e)?(h=!0,(e.tag!==null||e.anchor!==null)&&K(e,"alias node should not have any properties")):Eh(e,m,ga===r)&&(h=!0,e.tag===null&&(e.tag="?")),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):c===0&&(h=s&&Yn(e,y))),e.tag===null)e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);else if(e.tag==="?"){for(e.result!==null&&e.kind!=="scalar"&&K(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),u=0,p=e.implicitTypes.length;u<p;u+=1)if(g=e.implicitTypes[u],g.resolve(e.result)){e.result=g.construct(e.result),e.tag=g.tag,e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);break}}else if(e.tag!=="!"){if(We.call(e.typeMap[e.kind||"fallback"],e.tag))g=e.typeMap[e.kind||"fallback"][e.tag];else for(g=null,d=e.typeMap.multi[e.kind||"fallback"],u=0,p=d.length;u<p;u+=1)if(e.tag.slice(0,d[u].tag.length)===d[u].tag){g=d[u];break}g||K(e,"unknown tag !<"+e.tag+">"),e.result!==null&&g.kind!==e.kind&&K(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+g.kind+'", not "'+e.kind+'"'),g.resolve(e.result,e.tag)?(e.result=g.construct(e.result,e.tag),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):K(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")}return e.listener!==null&&e.listener("close",e),e.tag!==null||e.anchor!==null||h}f(rr,"composeNode");function zh(e){var t=e.position,r,i,a,n=!1,o;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap=Object.create(null),e.anchorMap=Object.create(null);(o=e.input.charCodeAt(e.position))!==0&&(St(e,!0,-1),o=e.input.charCodeAt(e.position),!(e.lineIndent>0||o!==37));){for(n=!0,o=e.input.charCodeAt(++e.position),r=e.position;o!==0&&!Yt(o);)o=e.input.charCodeAt(++e.position);for(i=e.input.slice(r,e.position),a=[],i.length<1&&K(e,"directive name must not be less than one character in length");o!==0;){for(;ze(o);)o=e.input.charCodeAt(++e.position);if(o===35){do o=e.input.charCodeAt(++e.position);while(o!==0&&!he(o));break}if(he(o))break;for(r=e.position;o!==0&&!Yt(o);)o=e.input.charCodeAt(++e.position);a.push(e.input.slice(r,e.position))}o!==0&&za(e),We.call(Qo,i)?Qo[i](e,i,a):ui(e,'unknown document directive "'+i+'"')}if(St(e,!0,-1),e.lineIndent===0&&e.input.charCodeAt(e.position)===45&&e.input.charCodeAt(e.position+1)===45&&e.input.charCodeAt(e.position+2)===45?(e.position+=3,St(e,!0,-1)):n&&K(e,"directives end mark is expected"),rr(e,e.lineIndent-1,ma,!1,!0),St(e,!0,-1),e.checkLineBreaks&&by.test(e.input.slice(t,e.position))&&ui(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&Mi(e)){e.input.charCodeAt(e.position)===46&&(e.position+=3,St(e,!0,-1));return}if(e.position<e.length-1)K(e,"end of the stream or a document separator is expected");else return}f(zh,"readDocument");function Rs(e,t){e=String(e),t=t||{},e.length!==0&&(e.charCodeAt(e.length-1)!==10&&e.charCodeAt(e.length-1)!==13&&(e+=`
`),e.charCodeAt(0)===65279&&(e=e.slice(1)));var r=new Mh(e,t),i=e.indexOf("\0");for(i!==-1&&(r.position=i,K(r,"null byte is not allowed in input")),r.input+="\0";r.input.charCodeAt(r.position)===32;)r.lineIndent+=1,r.position+=1;for(;r.position<r.length-1;)zh(r);return r.documents}f(Rs,"loadDocuments");function Wh(e,t,r){t!==null&&typeof t=="object"&&typeof r>"u"&&(r=t,t=null);var i=Rs(e,r);if(typeof t!="function")return i;for(var a=0,n=i.length;a<n;a+=1)t(i[a])}f(Wh,"loadAll$1");function qh(e,t){var r=Rs(e,t);if(r.length!==0){if(r.length===1)return r[0];throw new Qt("expected a single document in the stream, but found more")}}f(qh,"load$1");var ky=Wh,wy=qh,vy={loadAll:ky,load:wy},Hh=Object.prototype.toString,jh=Object.prototype.hasOwnProperty,Is=65279,Sy=9,di=10,Ty=13,_y=32,By=33,Ly=34,Un=35,Ay=37,My=38,Ey=39,Fy=42,Yh=44,$y=45,ya=58,Oy=61,Dy=62,Ry=63,Iy=64,Uh=91,Gh=93,Py=96,Vh=123,Ny=124,Xh=125,Wt={};Wt[0]="\\0";Wt[7]="\\a";Wt[8]="\\b";Wt[9]="\\t";Wt[10]="\\n";Wt[11]="\\v";Wt[12]="\\f";Wt[13]="\\r";Wt[27]="\\e";Wt[34]='\\"';Wt[92]="\\\\";Wt[133]="\\N";Wt[160]="\\_";Wt[8232]="\\L";Wt[8233]="\\P";var zy=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],Wy=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function Zh(e,t){var r,i,a,n,o,s,c;if(t===null)return{};for(r={},i=Object.keys(t),a=0,n=i.length;a<n;a+=1)o=i[a],s=String(t[o]),o.slice(0,2)==="!!"&&(o="tag:yaml.org,2002:"+o.slice(2)),c=e.compiledTypeMap.fallback[o],c&&jh.call(c.styleAliases,s)&&(s=c.styleAliases[s]),r[o]=s;return r}f(Zh,"compileStyleMap");function Kh(e){var t,r,i;if(t=e.toString(16).toUpperCase(),e<=255)r="x",i=2;else if(e<=65535)r="u",i=4;else if(e<=4294967295)r="U",i=8;else throw new Qt("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+r+Lt.repeat("0",i-t.length)+t}f(Kh,"encodeHex");var qy=1,pi=2;function Qh(e){this.schema=e.schema||bh,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=Lt.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=Zh(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.quotingType=e.quotingType==='"'?pi:qy,this.forceQuotes=e.forceQuotes||!1,this.replacer=typeof e.replacer=="function"?e.replacer:null,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}f(Qh,"State");function Gn(e,t){for(var r=Lt.repeat(" ",t),i=0,a=-1,n="",o,s=e.length;i<s;)a=e.indexOf(`
`,i),a===-1?(o=e.slice(i),i=s):(o=e.slice(i,a+1),i=a+1),o.length&&o!==`
`&&(n+=r),n+=o;return n}f(Gn,"indentString");function xa(e,t){return`
`+Lt.repeat(" ",e.indent*t)}f(xa,"generateNextLine");function Jh(e,t){var r,i,a;for(r=0,i=e.implicitTypes.length;r<i;r+=1)if(a=e.implicitTypes[r],a.resolve(t))return!0;return!1}f(Jh,"testImplicitResolving");function fi(e){return e===_y||e===Sy}f(fi,"isWhitespace");function Lr(e){return 32<=e&&e<=126||161<=e&&e<=55295&&e!==8232&&e!==8233||57344<=e&&e<=65533&&e!==Is||65536<=e&&e<=1114111}f(Lr,"isPrintable");function Vn(e){return Lr(e)&&e!==Is&&e!==Ty&&e!==di}f(Vn,"isNsCharOrWhitespace");function Xn(e,t,r){var i=Vn(e),a=i&&!fi(e);return(r?i:i&&e!==Yh&&e!==Uh&&e!==Gh&&e!==Vh&&e!==Xh)&&e!==Un&&!(t===ya&&!a)||Vn(t)&&!fi(t)&&e===Un||t===ya&&a}f(Xn,"isPlainSafe");function tu(e){return Lr(e)&&e!==Is&&!fi(e)&&e!==$y&&e!==Ry&&e!==ya&&e!==Yh&&e!==Uh&&e!==Gh&&e!==Vh&&e!==Xh&&e!==Un&&e!==My&&e!==Fy&&e!==By&&e!==Ny&&e!==Oy&&e!==Dy&&e!==Ey&&e!==Ly&&e!==Ay&&e!==Iy&&e!==Py}f(tu,"isPlainSafeFirst");function eu(e){return!fi(e)&&e!==ya}f(eu,"isPlainSafeLast");function Cr(e,t){var r=e.charCodeAt(t),i;return r>=55296&&r<=56319&&t+1<e.length&&(i=e.charCodeAt(t+1),i>=56320&&i<=57343)?(r-55296)*1024+i-56320+65536:r}f(Cr,"codePointAt");function Ps(e){var t=/^\n* /;return t.test(e)}f(Ps,"needIndentIndicator");var ru=1,Zn=2,iu=3,au=4,xr=5;function nu(e,t,r,i,a,n,o,s){var c,l=0,h=null,u=!1,p=!1,d=i!==-1,g=-1,m=tu(Cr(e,0))&&eu(Cr(e,e.length-1));if(t||o)for(c=0;c<e.length;l>=65536?c+=2:c++){if(l=Cr(e,c),!Lr(l))return xr;m=m&&Xn(l,h,s),h=l}else{for(c=0;c<e.length;l>=65536?c+=2:c++){if(l=Cr(e,c),l===di)u=!0,d&&(p=p||c-g-1>i&&e[g+1]!==" ",g=c);else if(!Lr(l))return xr;m=m&&Xn(l,h,s),h=l}p=p||d&&c-g-1>i&&e[g+1]!==" "}return!u&&!p?m&&!o&&!a(e)?ru:n===pi?xr:Zn:r>9&&Ps(e)?xr:o?n===pi?xr:Zn:p?au:iu}f(nu,"chooseScalarStyle");function su(e,t,r,i,a){e.dump=function(){if(t.length===0)return e.quotingType===pi?'""':"''";if(!e.noCompatMode&&(zy.indexOf(t)!==-1||Wy.test(t)))return e.quotingType===pi?'"'+t+'"':"'"+t+"'";var n=e.indent*Math.max(1,r),o=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-n),s=i||e.flowLevel>-1&&r>=e.flowLevel;function c(l){return Jh(e,l)}switch(f(c,"testAmbiguity"),nu(t,s,e.indent,o,c,e.quotingType,e.forceQuotes&&!i,a)){case ru:return t;case Zn:return"'"+t.replace(/'/g,"''")+"'";case iu:return"|"+Kn(t,e.indent)+Qn(Gn(t,n));case au:return">"+Kn(t,e.indent)+Qn(Gn(ou(t,o),n));case xr:return'"'+lu(t)+'"';default:throw new Qt("impossible error: invalid scalar style")}}()}f(su,"writeScalar");function Kn(e,t){var r=Ps(e)?String(t):"",i=e[e.length-1]===`
`,a=i&&(e[e.length-2]===`
`||e===`
`),n=a?"+":i?"":"-";return r+n+`
`}f(Kn,"blockHeader");function Qn(e){return e[e.length-1]===`
`?e.slice(0,-1):e}f(Qn,"dropEndingNewline");function ou(e,t){for(var r=/(\n+)([^\n]*)/g,i=function(){var l=e.indexOf(`
`);return l=l!==-1?l:e.length,r.lastIndex=l,Jn(e.slice(0,l),t)}(),a=e[0]===`
`||e[0]===" ",n,o;o=r.exec(e);){var s=o[1],c=o[2];n=c[0]===" ",i+=s+(!a&&!n&&c!==""?`
`:"")+Jn(c,t),a=n}return i}f(ou,"foldString");function Jn(e,t){if(e===""||e[0]===" ")return e;for(var r=/ [^ ]/g,i,a=0,n,o=0,s=0,c="";i=r.exec(e);)s=i.index,s-a>t&&(n=o>a?o:s,c+=`
`+e.slice(a,n),a=n+1),o=s;return c+=`
`,e.length-a>t&&o>a?c+=e.slice(a,o)+`
`+e.slice(o+1):c+=e.slice(a),c.slice(1)}f(Jn,"foldLine");function lu(e){for(var t="",r=0,i,a=0;a<e.length;r>=65536?a+=2:a++)r=Cr(e,a),i=Wt[r],!i&&Lr(r)?(t+=e[a],r>=65536&&(t+=e[a+1])):t+=i||Kh(r);return t}f(lu,"escapeString");function cu(e,t,r){var i="",a=e.tag,n,o,s;for(n=0,o=r.length;n<o;n+=1)s=r[n],e.replacer&&(s=e.replacer.call(r,String(n),s)),(Ce(e,t,s,!1,!1)||typeof s>"u"&&Ce(e,t,null,!1,!1))&&(i!==""&&(i+=","+(e.condenseFlow?"":" ")),i+=e.dump);e.tag=a,e.dump="["+i+"]"}f(cu,"writeFlowSequence");function ts(e,t,r,i){var a="",n=e.tag,o,s,c;for(o=0,s=r.length;o<s;o+=1)c=r[o],e.replacer&&(c=e.replacer.call(r,String(o),c)),(Ce(e,t+1,c,!0,!0,!1,!0)||typeof c>"u"&&Ce(e,t+1,null,!0,!0,!1,!0))&&((!i||a!=="")&&(a+=xa(e,t)),e.dump&&di===e.dump.charCodeAt(0)?a+="-":a+="- ",a+=e.dump);e.tag=n,e.dump=a||"[]"}f(ts,"writeBlockSequence");function hu(e,t,r){var i="",a=e.tag,n=Object.keys(r),o,s,c,l,h;for(o=0,s=n.length;o<s;o+=1)h="",i!==""&&(h+=", "),e.condenseFlow&&(h+='"'),c=n[o],l=r[c],e.replacer&&(l=e.replacer.call(r,c,l)),Ce(e,t,c,!1,!1)&&(e.dump.length>1024&&(h+="? "),h+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),Ce(e,t,l,!1,!1)&&(h+=e.dump,i+=h));e.tag=a,e.dump="{"+i+"}"}f(hu,"writeFlowMapping");function uu(e,t,r,i){var a="",n=e.tag,o=Object.keys(r),s,c,l,h,u,p;if(e.sortKeys===!0)o.sort();else if(typeof e.sortKeys=="function")o.sort(e.sortKeys);else if(e.sortKeys)throw new Qt("sortKeys must be a boolean or a function");for(s=0,c=o.length;s<c;s+=1)p="",(!i||a!=="")&&(p+=xa(e,t)),l=o[s],h=r[l],e.replacer&&(h=e.replacer.call(r,l,h)),Ce(e,t+1,l,!0,!0,!0)&&(u=e.tag!==null&&e.tag!=="?"||e.dump&&e.dump.length>1024,u&&(e.dump&&di===e.dump.charCodeAt(0)?p+="?":p+="? "),p+=e.dump,u&&(p+=xa(e,t)),Ce(e,t+1,h,!0,u)&&(e.dump&&di===e.dump.charCodeAt(0)?p+=":":p+=": ",p+=e.dump,a+=p));e.tag=n,e.dump=a||"{}"}f(uu,"writeBlockMapping");function es(e,t,r){var i,a,n,o,s,c;for(a=r?e.explicitTypes:e.implicitTypes,n=0,o=a.length;n<o;n+=1)if(s=a[n],(s.instanceOf||s.predicate)&&(!s.instanceOf||typeof t=="object"&&t instanceof s.instanceOf)&&(!s.predicate||s.predicate(t))){if(r?s.multi&&s.representName?e.tag=s.representName(t):e.tag=s.tag:e.tag="?",s.represent){if(c=e.styleMap[s.tag]||s.defaultStyle,Hh.call(s.represent)==="[object Function]")i=s.represent(t,c);else if(jh.call(s.represent,c))i=s.represent[c](t,c);else throw new Qt("!<"+s.tag+'> tag resolver accepts not "'+c+'" style');e.dump=i}return!0}return!1}f(es,"detectType");function Ce(e,t,r,i,a,n,o){e.tag=null,e.dump=r,es(e,r,!1)||es(e,r,!0);var s=Hh.call(e.dump),c=i,l;i&&(i=e.flowLevel<0||e.flowLevel>t);var h=s==="[object Object]"||s==="[object Array]",u,p;if(h&&(u=e.duplicates.indexOf(r),p=u!==-1),(e.tag!==null&&e.tag!=="?"||p||e.indent!==2&&t>0)&&(a=!1),p&&e.usedDuplicates[u])e.dump="*ref_"+u;else{if(h&&p&&!e.usedDuplicates[u]&&(e.usedDuplicates[u]=!0),s==="[object Object]")i&&Object.keys(e.dump).length!==0?(uu(e,t,e.dump,a),p&&(e.dump="&ref_"+u+e.dump)):(hu(e,t,e.dump),p&&(e.dump="&ref_"+u+" "+e.dump));else if(s==="[object Array]")i&&e.dump.length!==0?(e.noArrayIndent&&!o&&t>0?ts(e,t-1,e.dump,a):ts(e,t,e.dump,a),p&&(e.dump="&ref_"+u+e.dump)):(cu(e,t,e.dump),p&&(e.dump="&ref_"+u+" "+e.dump));else if(s==="[object String]")e.tag!=="?"&&su(e,e.dump,t,n,c);else{if(s==="[object Undefined]")return!1;if(e.skipInvalid)return!1;throw new Qt("unacceptable kind of an object to dump "+s)}e.tag!==null&&e.tag!=="?"&&(l=encodeURI(e.tag[0]==="!"?e.tag.slice(1):e.tag).replace(/!/g,"%21"),e.tag[0]==="!"?l="!"+l:l.slice(0,18)==="tag:yaml.org,2002:"?l="!!"+l.slice(18):l="!<"+l+">",e.dump=l+" "+e.dump)}return!0}f(Ce,"writeNode");function du(e,t){var r=[],i=[],a,n;for(ba(e,r,i),a=0,n=i.length;a<n;a+=1)t.duplicates.push(r[i[a]]);t.usedDuplicates=new Array(n)}f(du,"getDuplicateReferences");function ba(e,t,r){var i,a,n;if(e!==null&&typeof e=="object")if(a=t.indexOf(e),a!==-1)r.indexOf(a)===-1&&r.push(a);else if(t.push(e),Array.isArray(e))for(a=0,n=e.length;a<n;a+=1)ba(e[a],t,r);else for(i=Object.keys(e),a=0,n=i.length;a<n;a+=1)ba(e[i[a]],t,r)}f(ba,"inspectNode");function Hy(e,t){t=t||{};var r=new Qh(t);r.noRefs||du(e,r);var i=e;return r.replacer&&(i=r.replacer.call({"":i},"",i)),Ce(r,0,i,!0,!0)?r.dump+`
`:""}f(Hy,"dump$1");function jy(e,t){return function(){throw new Error("Function yaml."+e+" is removed in js-yaml 4. Use yaml."+t+" instead, which is now safe by default.")}}f(jy,"renamed");var Yy=rh,Uy=vy.load;/*! Bundled license information:

js-yaml/dist/js-yaml.mjs:
  (*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT *)
*/var ie={aggregation:18,extension:18,composition:18,dependency:6,lollipop:13.5,arrow_point:4};function Jr(e,t){if(e===void 0||t===void 0)return{angle:0,deltaX:0,deltaY:0};e=kt(e),t=kt(t);const[r,i]=[e.x,e.y],[a,n]=[t.x,t.y],o=a-r,s=n-i;return{angle:Math.atan(s/o),deltaX:o,deltaY:s}}f(Jr,"calculateDeltaAndAngle");var kt=f(e=>Array.isArray(e)?{x:e[0],y:e[1]}:e,"pointTransformer"),Gy=f(e=>({x:f(function(t,r,i){let a=0;const n=kt(i[0]).x<kt(i[i.length-1]).x?"left":"right";if(r===0&&Object.hasOwn(ie,e.arrowTypeStart)){const{angle:d,deltaX:g}=Jr(i[0],i[1]);a=ie[e.arrowTypeStart]*Math.cos(d)*(g>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(ie,e.arrowTypeEnd)){const{angle:d,deltaX:g}=Jr(i[i.length-1],i[i.length-2]);a=ie[e.arrowTypeEnd]*Math.cos(d)*(g>=0?1:-1)}const o=Math.abs(kt(t).x-kt(i[i.length-1]).x),s=Math.abs(kt(t).y-kt(i[i.length-1]).y),c=Math.abs(kt(t).x-kt(i[0]).x),l=Math.abs(kt(t).y-kt(i[0]).y),h=ie[e.arrowTypeStart],u=ie[e.arrowTypeEnd],p=1;if(o<u&&o>0&&s<u){let d=u+p-o;d*=n==="right"?-1:1,a-=d}if(c<h&&c>0&&l<h){let d=h+p-c;d*=n==="right"?-1:1,a+=d}return kt(t).x+a},"x"),y:f(function(t,r,i){let a=0;const n=kt(i[0]).y<kt(i[i.length-1]).y?"down":"up";if(r===0&&Object.hasOwn(ie,e.arrowTypeStart)){const{angle:d,deltaY:g}=Jr(i[0],i[1]);a=ie[e.arrowTypeStart]*Math.abs(Math.sin(d))*(g>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(ie,e.arrowTypeEnd)){const{angle:d,deltaY:g}=Jr(i[i.length-1],i[i.length-2]);a=ie[e.arrowTypeEnd]*Math.abs(Math.sin(d))*(g>=0?1:-1)}const o=Math.abs(kt(t).y-kt(i[i.length-1]).y),s=Math.abs(kt(t).x-kt(i[i.length-1]).x),c=Math.abs(kt(t).y-kt(i[0]).y),l=Math.abs(kt(t).x-kt(i[0]).x),h=ie[e.arrowTypeStart],u=ie[e.arrowTypeEnd],p=1;if(o<u&&o>0&&s<u){let d=u+p-o;d*=n==="up"?-1:1,a-=d}if(c<h&&c>0&&l<h){let d=h+p-c;d*=n==="up"?-1:1,a+=d}return kt(t).y+a},"y")}),"getLineFunctionsWithOffset"),Ns=f(({flowchart:e})=>{var a,n;const t=((a=e==null?void 0:e.subGraphTitleMargin)==null?void 0:a.top)??0,r=((n=e==null?void 0:e.subGraphTitleMargin)==null?void 0:n.bottom)??0,i=t+r;return{subGraphTitleTopMargin:t,subGraphTitleBottomMargin:r,subGraphTitleTotalMargin:i}},"getSubGraphTitleMargins"),Vy=f(e=>{const{handDrawnSeed:t}=ut();return{fill:e,hachureAngle:120,hachureGap:4,fillWeight:2,roughness:.7,stroke:e,seed:t}},"solidStateFill"),$r=f(e=>{const t=Xy([...e.cssCompiledStyles||[],...e.cssStyles||[]]);return{stylesMap:t,stylesArray:[...t]}},"compileStyles"),Xy=f(e=>{const t=new Map;return e.forEach(r=>{const[i,a]=r.split(":");t.set(i.trim(),a==null?void 0:a.trim())}),t},"styles2Map"),pu=f(e=>e==="color"||e==="font-size"||e==="font-family"||e==="font-weight"||e==="font-style"||e==="text-decoration"||e==="text-align"||e==="text-transform"||e==="line-height"||e==="letter-spacing"||e==="word-spacing"||e==="text-shadow"||e==="text-overflow"||e==="white-space"||e==="word-wrap"||e==="word-break"||e==="overflow-wrap"||e==="hyphens","isLabelStyle"),Z=f(e=>{const{stylesArray:t}=$r(e),r=[],i=[],a=[],n=[];return t.forEach(o=>{const s=o[0];pu(s)?r.push(o.join(":")+" !important"):(i.push(o.join(":")+" !important"),s.includes("stroke")&&a.push(o.join(":")+" !important"),s==="fill"&&n.push(o.join(":")+" !important"))}),{labelStyles:r.join(";"),nodeStyles:i.join(";"),stylesArray:t,borderStyles:a,backgroundStyles:n}},"styles2String"),Y=f((e,t)=>{var c;const{themeVariables:r,handDrawnSeed:i}=ut(),{nodeBorder:a,mainBkg:n}=r,{stylesMap:o}=$r(e);return Object.assign({roughness:.7,fill:o.get("fill")||n,fillStyle:"hachure",fillWeight:4,hachureGap:5.2,stroke:o.get("stroke")||a,seed:i,strokeWidth:((c=o.get("stroke-width"))==null?void 0:c.replace("px",""))||1.3,fillLineDash:[0,0],strokeLineDash:Zy(o.get("stroke-dasharray"))},t)},"userNodeOverrides"),Zy=f(e=>{if(!e)return[0,0];const t=e.trim().split(/\s+/).map(Number);if(t.length===1){const a=isNaN(t[0])?0:t[0];return[a,a]}const r=isNaN(t[0])?0:t[0],i=isNaN(t[1])?0:t[1];return[r,i]},"getStrokeDashArray"),zs={},Et={};Object.defineProperty(Et,"__esModule",{value:!0});Et.BLANK_URL=Et.relativeFirstCharacters=Et.whitespaceEscapeCharsRegex=Et.urlSchemeRegex=Et.ctrlCharactersRegex=Et.htmlCtrlEntityRegex=Et.htmlEntitiesRegex=Et.invalidProtocolRegex=void 0;Et.invalidProtocolRegex=/^([^\w]*)(javascript|data|vbscript)/im;Et.htmlEntitiesRegex=/&#(\w+)(^\w|;)?/g;Et.htmlCtrlEntityRegex=/&(newline|tab);/gi;Et.ctrlCharactersRegex=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim;Et.urlSchemeRegex=/^.+(:|&colon;)/gim;Et.whitespaceEscapeCharsRegex=/(\\|%5[cC])((%(6[eE]|72|74))|[nrt])/g;Et.relativeFirstCharacters=[".","/"];Et.BLANK_URL="about:blank";Object.defineProperty(zs,"__esModule",{value:!0});var fu=zs.sanitizeUrl=void 0,Pt=Et;function Ky(e){return Pt.relativeFirstCharacters.indexOf(e[0])>-1}function Qy(e){var t=e.replace(Pt.ctrlCharactersRegex,"");return t.replace(Pt.htmlEntitiesRegex,function(r,i){return String.fromCharCode(i)})}function Jy(e){return URL.canParse(e)}function Jo(e){try{return decodeURIComponent(e)}catch{return e}}function t0(e){if(!e)return Pt.BLANK_URL;var t,r=Jo(e.trim());do r=Qy(r).replace(Pt.htmlCtrlEntityRegex,"").replace(Pt.ctrlCharactersRegex,"").replace(Pt.whitespaceEscapeCharsRegex,"").trim(),r=Jo(r),t=r.match(Pt.ctrlCharactersRegex)||r.match(Pt.htmlEntitiesRegex)||r.match(Pt.htmlCtrlEntityRegex)||r.match(Pt.whitespaceEscapeCharsRegex);while(t&&t.length>0);var i=r;if(!i)return Pt.BLANK_URL;if(Ky(i))return i;var a=i.trimStart(),n=a.match(Pt.urlSchemeRegex);if(!n)return i;var o=n[0].toLowerCase().trim();if(Pt.invalidProtocolRegex.test(o))return Pt.BLANK_URL;var s=a.replace(/\\/g,"/");if(o==="mailto:"||o.includes("://"))return s;if(o==="http:"||o==="https:"){if(!Jy(s))return Pt.BLANK_URL;var c=new URL(s);return c.protocol=c.protocol.toLowerCase(),c.hostname=c.hostname.toLowerCase(),c.toString()}return s}fu=zs.sanitizeUrl=t0;function tl(e,t,r){var i=new Zf;return t=t==null?0:+t,i.restart(a=>{i.stop(),e(a+t)},t,r),i}var e0=Xf("start","end","cancel","interrupt"),r0=[],gu=0,el=1,rs=2,ra=3,rl=4,is=5,ia=6;function qa(e,t,r,i,a,n){var o=e.__transition;if(!o)e.__transition={};else if(r in o)return;i0(e,r,{name:t,index:i,group:a,on:e0,tween:r0,time:n.time,delay:n.delay,duration:n.duration,ease:n.ease,timer:null,state:gu})}function Ws(e,t){var r=de(e,t);if(r.state>gu)throw new Error("too late; already scheduled");return r}function ke(e,t){var r=de(e,t);if(r.state>ra)throw new Error("too late; already running");return r}function de(e,t){var r=e.__transition;if(!r||!(r=r[t]))throw new Error("transition not found");return r}function i0(e,t,r){var i=e.__transition,a;i[t]=r,r.timer=Kf(n,0,r.time);function n(l){r.state=el,r.timer.restart(o,r.delay,r.time),r.delay<=l&&o(l-r.delay)}function o(l){var h,u,p,d;if(r.state!==el)return c();for(h in i)if(d=i[h],d.name===r.name){if(d.state===ra)return tl(o);d.state===rl?(d.state=ia,d.timer.stop(),d.on.call("interrupt",e,e.__data__,d.index,d.group),delete i[h]):+h<t&&(d.state=ia,d.timer.stop(),d.on.call("cancel",e,e.__data__,d.index,d.group),delete i[h])}if(tl(function(){r.state===ra&&(r.state=rl,r.timer.restart(s,r.delay,r.time),s(l))}),r.state=rs,r.on.call("start",e,e.__data__,r.index,r.group),r.state===rs){for(r.state=ra,a=new Array(p=r.tween.length),h=0,u=-1;h<p;++h)(d=r.tween[h].value.call(e,e.__data__,r.index,r.group))&&(a[++u]=d);a.length=u+1}}function s(l){for(var h=l<r.duration?r.ease.call(null,l/r.duration):(r.timer.restart(c),r.state=is,1),u=-1,p=a.length;++u<p;)a[u].call(e,h);r.state===is&&(r.on.call("end",e,e.__data__,r.index,r.group),c())}function c(){r.state=ia,r.timer.stop(),delete i[t];for(var l in i)return;delete e.__transition}}function a0(e,t){var r=e.__transition,i,a,n=!0,o;if(r){t=t==null?null:t+"";for(o in r){if((i=r[o]).name!==t){n=!1;continue}a=i.state>rs&&i.state<is,i.state=ia,i.timer.stop(),i.on.call(a?"interrupt":"cancel",e,e.__data__,i.index,i.group),delete r[o]}n&&delete e.__transition}}function n0(e){return this.each(function(){a0(this,e)})}function s0(e,t){var r,i;return function(){var a=ke(this,e),n=a.tween;if(n!==r){i=r=n;for(var o=0,s=i.length;o<s;++o)if(i[o].name===t){i=i.slice(),i.splice(o,1);break}}a.tween=i}}function o0(e,t,r){var i,a;if(typeof r!="function")throw new Error;return function(){var n=ke(this,e),o=n.tween;if(o!==i){a=(i=o).slice();for(var s={name:t,value:r},c=0,l=a.length;c<l;++c)if(a[c].name===t){a[c]=s;break}c===l&&a.push(s)}n.tween=a}}function l0(e,t){var r=this._id;if(e+="",arguments.length<2){for(var i=de(this.node(),r).tween,a=0,n=i.length,o;a<n;++a)if((o=i[a]).name===e)return o.value;return null}return this.each((t==null?s0:o0)(r,e,t))}function qs(e,t,r){var i=e._id;return e.each(function(){var a=ke(this,i);(a.value||(a.value={}))[t]=r.apply(this,arguments)}),function(a){return de(a,i).value[t]}}function mu(e,t){var r;return(typeof t=="number"?Qf:t instanceof Do?Ro:(r=Do(t))?(t=r,Ro):Jf)(e,t)}function c0(e){return function(){this.removeAttribute(e)}}function h0(e){return function(){this.removeAttributeNS(e.space,e.local)}}function u0(e,t,r){var i,a=r+"",n;return function(){var o=this.getAttribute(e);return o===a?null:o===i?n:n=t(i=o,r)}}function d0(e,t,r){var i,a=r+"",n;return function(){var o=this.getAttributeNS(e.space,e.local);return o===a?null:o===i?n:n=t(i=o,r)}}function p0(e,t,r){var i,a,n;return function(){var o,s=r(this),c;return s==null?void this.removeAttribute(e):(o=this.getAttribute(e),c=s+"",o===c?null:o===i&&c===a?n:(a=c,n=t(i=o,s)))}}function f0(e,t,r){var i,a,n;return function(){var o,s=r(this),c;return s==null?void this.removeAttributeNS(e.space,e.local):(o=this.getAttributeNS(e.space,e.local),c=s+"",o===c?null:o===i&&c===a?n:(a=c,n=t(i=o,s)))}}function g0(e,t){var r=ac(e),i=r==="transform"?tg:mu;return this.attrTween(e,typeof t=="function"?(r.local?f0:p0)(r,i,qs(this,"attr."+e,t)):t==null?(r.local?h0:c0)(r):(r.local?d0:u0)(r,i,t))}function m0(e,t){return function(r){this.setAttribute(e,t.call(this,r))}}function y0(e,t){return function(r){this.setAttributeNS(e.space,e.local,t.call(this,r))}}function x0(e,t){var r,i;function a(){var n=t.apply(this,arguments);return n!==i&&(r=(i=n)&&y0(e,n)),r}return a._value=t,a}function b0(e,t){var r,i;function a(){var n=t.apply(this,arguments);return n!==i&&(r=(i=n)&&m0(e,n)),r}return a._value=t,a}function C0(e,t){var r="attr."+e;if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;var i=ac(e);return this.tween(r,(i.local?x0:b0)(i,t))}function k0(e,t){return function(){Ws(this,e).delay=+t.apply(this,arguments)}}function w0(e,t){return t=+t,function(){Ws(this,e).delay=t}}function v0(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?k0:w0)(t,e)):de(this.node(),t).delay}function S0(e,t){return function(){ke(this,e).duration=+t.apply(this,arguments)}}function T0(e,t){return t=+t,function(){ke(this,e).duration=t}}function _0(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?S0:T0)(t,e)):de(this.node(),t).duration}function B0(e,t){if(typeof t!="function")throw new Error;return function(){ke(this,e).ease=t}}function L0(e){var t=this._id;return arguments.length?this.each(B0(t,e)):de(this.node(),t).ease}function A0(e,t){return function(){var r=t.apply(this,arguments);if(typeof r!="function")throw new Error;ke(this,e).ease=r}}function M0(e){if(typeof e!="function")throw new Error;return this.each(A0(this._id,e))}function E0(e){typeof e!="function"&&(e=dg(e));for(var t=this._groups,r=t.length,i=new Array(r),a=0;a<r;++a)for(var n=t[a],o=n.length,s=i[a]=[],c,l=0;l<o;++l)(c=n[l])&&e.call(c,c.__data__,l,n)&&s.push(c);return new De(i,this._parents,this._name,this._id)}function F0(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,r=e._groups,i=t.length,a=r.length,n=Math.min(i,a),o=new Array(i),s=0;s<n;++s)for(var c=t[s],l=r[s],h=c.length,u=o[s]=new Array(h),p,d=0;d<h;++d)(p=c[d]||l[d])&&(u[d]=p);for(;s<i;++s)o[s]=t[s];return new De(o,this._parents,this._name,this._id)}function $0(e){return(e+"").trim().split(/^|\s+/).every(function(t){var r=t.indexOf(".");return r>=0&&(t=t.slice(0,r)),!t||t==="start"})}function O0(e,t,r){var i,a,n=$0(t)?Ws:ke;return function(){var o=n(this,e),s=o.on;s!==i&&(a=(i=s).copy()).on(t,r),o.on=a}}function D0(e,t){var r=this._id;return arguments.length<2?de(this.node(),r).on.on(e):this.each(O0(r,e,t))}function R0(e){return function(){var t=this.parentNode;for(var r in this.__transition)if(+r!==e)return;t&&t.removeChild(this)}}function I0(){return this.on("end.remove",R0(this._id))}function P0(e){var t=this._name,r=this._id;typeof e!="function"&&(e=pg(e));for(var i=this._groups,a=i.length,n=new Array(a),o=0;o<a;++o)for(var s=i[o],c=s.length,l=n[o]=new Array(c),h,u,p=0;p<c;++p)(h=s[p])&&(u=e.call(h,h.__data__,p,s))&&("__data__"in h&&(u.__data__=h.__data__),l[p]=u,qa(l[p],t,r,p,l,de(h,r)));return new De(n,this._parents,t,r)}function N0(e){var t=this._name,r=this._id;typeof e!="function"&&(e=fg(e));for(var i=this._groups,a=i.length,n=[],o=[],s=0;s<a;++s)for(var c=i[s],l=c.length,h,u=0;u<l;++u)if(h=c[u]){for(var p=e.call(h,h.__data__,u,c),d,g=de(h,r),m=0,y=p.length;m<y;++m)(d=p[m])&&qa(d,t,r,m,p,g);n.push(p),o.push(h)}return new De(n,o,t,r)}var z0=Da.prototype.constructor;function W0(){return new z0(this._groups,this._parents)}function q0(e,t){var r,i,a;return function(){var n=hi(this,e),o=(this.style.removeProperty(e),hi(this,e));return n===o?null:n===r&&o===i?a:a=t(r=n,i=o)}}function yu(e){return function(){this.style.removeProperty(e)}}function H0(e,t,r){var i,a=r+"",n;return function(){var o=hi(this,e);return o===a?null:o===i?n:n=t(i=o,r)}}function j0(e,t,r){var i,a,n;return function(){var o=hi(this,e),s=r(this),c=s+"";return s==null&&(c=s=(this.style.removeProperty(e),hi(this,e))),o===c?null:o===i&&c===a?n:(a=c,n=t(i=o,s))}}function Y0(e,t){var r,i,a,n="style."+t,o="end."+n,s;return function(){var c=ke(this,e),l=c.on,h=c.value[n]==null?s||(s=yu(t)):void 0;(l!==r||a!==h)&&(i=(r=l).copy()).on(o,a=h),c.on=i}}function U0(e,t,r){var i=(e+="")=="transform"?eg:mu;return t==null?this.styleTween(e,q0(e,i)).on("end.style."+e,yu(e)):typeof t=="function"?this.styleTween(e,j0(e,i,qs(this,"style."+e,t))).each(Y0(this._id,e)):this.styleTween(e,H0(e,i,t),r).on("end.style."+e,null)}function G0(e,t,r){return function(i){this.style.setProperty(e,t.call(this,i),r)}}function V0(e,t,r){var i,a;function n(){var o=t.apply(this,arguments);return o!==a&&(i=(a=o)&&G0(e,o,r)),i}return n._value=t,n}function X0(e,t,r){var i="style."+(e+="");if(arguments.length<2)return(i=this.tween(i))&&i._value;if(t==null)return this.tween(i,null);if(typeof t!="function")throw new Error;return this.tween(i,V0(e,t,r??""))}function Z0(e){return function(){this.textContent=e}}function K0(e){return function(){var t=e(this);this.textContent=t??""}}function Q0(e){return this.tween("text",typeof e=="function"?K0(qs(this,"text",e)):Z0(e==null?"":e+""))}function J0(e){return function(t){this.textContent=e.call(this,t)}}function tx(e){var t,r;function i(){var a=e.apply(this,arguments);return a!==r&&(t=(r=a)&&J0(a)),t}return i._value=e,i}function ex(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,tx(e))}function rx(){for(var e=this._name,t=this._id,r=xu(),i=this._groups,a=i.length,n=0;n<a;++n)for(var o=i[n],s=o.length,c,l=0;l<s;++l)if(c=o[l]){var h=de(c,t);qa(c,e,r,l,o,{time:h.time+h.delay+h.duration,delay:0,duration:h.duration,ease:h.ease})}return new De(i,this._parents,e,r)}function ix(){var e,t,r=this,i=r._id,a=r.size();return new Promise(function(n,o){var s={value:o},c={value:function(){--a===0&&n()}};r.each(function(){var l=ke(this,i),h=l.on;h!==e&&(t=(e=h).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(c)),l.on=t}),a===0&&n()})}var ax=0;function De(e,t,r,i){this._groups=e,this._parents=t,this._name=r,this._id=i}function xu(){return++ax}var Be=Da.prototype;De.prototype={constructor:De,select:P0,selectAll:N0,selectChild:Be.selectChild,selectChildren:Be.selectChildren,filter:E0,merge:F0,selection:W0,transition:rx,call:Be.call,nodes:Be.nodes,node:Be.node,size:Be.size,empty:Be.empty,each:Be.each,on:D0,attr:g0,attrTween:C0,style:U0,styleTween:X0,text:Q0,textTween:ex,remove:I0,tween:l0,delay:v0,duration:_0,ease:L0,easeVarying:M0,end:ix,[Symbol.iterator]:Be[Symbol.iterator]};function nx(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var sx={time:null,delay:0,duration:250,ease:nx};function ox(e,t){for(var r;!(r=e.__transition)||!(r=r[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return r}function lx(e){var t,r;e instanceof De?(t=e._id,e=e._name):(t=xu(),(r=sx).time=rg(),e=e==null?null:e+"");for(var i=this._groups,a=i.length,n=0;n<a;++n)for(var o=i[n],s=o.length,c,l=0;l<s;++l)(c=o[l])&&qa(c,e,t,l,o,r||ox(c,t));return new De(i,this._parents,e,t)}Da.prototype.interrupt=n0;Da.prototype.transition=lx;class bu{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function Cu(e){return new bu(e,!0)}function ku(e){return new bu(e,!1)}function ti(e,t,r){this.k=e,this.x=t,this.y=r}ti.prototype={constructor:ti,scale:function(e){return e===1?this:new ti(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new ti(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};ti.prototype;var wu=typeof global=="object"&&global&&global.Object===Object&&global,cx=typeof self=="object"&&self&&self.Object===Object&&self,we=wu||cx||Function("return this")(),Ca=we.Symbol,vu=Object.prototype,hx=vu.hasOwnProperty,ux=vu.toString,Ur=Ca?Ca.toStringTag:void 0;function dx(e){var t=hx.call(e,Ur),r=e[Ur];try{e[Ur]=void 0;var i=!0}catch{}var a=ux.call(e);return i&&(t?e[Ur]=r:delete e[Ur]),a}var px=Object.prototype,fx=px.toString;function gx(e){return fx.call(e)}var mx="[object Null]",yx="[object Undefined]",il=Ca?Ca.toStringTag:void 0;function Or(e){return e==null?e===void 0?yx:mx:il&&il in Object(e)?dx(e):gx(e)}function sr(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var xx="[object AsyncFunction]",bx="[object Function]",Cx="[object GeneratorFunction]",kx="[object Proxy]";function Hs(e){if(!sr(e))return!1;var t=Or(e);return t==bx||t==Cx||t==xx||t==kx}var kn=we["__core-js_shared__"],al=function(){var e=/[^.]+$/.exec(kn&&kn.keys&&kn.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function wx(e){return!!al&&al in e}var vx=Function.prototype,Sx=vx.toString;function or(e){if(e!=null){try{return Sx.call(e)}catch{}try{return e+""}catch{}}return""}var Tx=/[\\^$.*+?()[\]{}|]/g,_x=/^\[object .+?Constructor\]$/,Bx=Function.prototype,Lx=Object.prototype,Ax=Bx.toString,Mx=Lx.hasOwnProperty,Ex=RegExp("^"+Ax.call(Mx).replace(Tx,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Fx(e){if(!sr(e)||wx(e))return!1;var t=Hs(e)?Ex:_x;return t.test(or(e))}function $x(e,t){return e==null?void 0:e[t]}function lr(e,t){var r=$x(e,t);return Fx(r)?r:void 0}var gi=lr(Object,"create");function Ox(){this.__data__=gi?gi(null):{},this.size=0}function Dx(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var Rx="__lodash_hash_undefined__",Ix=Object.prototype,Px=Ix.hasOwnProperty;function Nx(e){var t=this.__data__;if(gi){var r=t[e];return r===Rx?void 0:r}return Px.call(t,e)?t[e]:void 0}var zx=Object.prototype,Wx=zx.hasOwnProperty;function qx(e){var t=this.__data__;return gi?t[e]!==void 0:Wx.call(t,e)}var Hx="__lodash_hash_undefined__";function jx(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=gi&&t===void 0?Hx:t,this}function ir(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}ir.prototype.clear=Ox;ir.prototype.delete=Dx;ir.prototype.get=Nx;ir.prototype.has=qx;ir.prototype.set=jx;function Yx(){this.__data__=[],this.size=0}function Ha(e,t){return e===t||e!==e&&t!==t}function ja(e,t){for(var r=e.length;r--;)if(Ha(e[r][0],t))return r;return-1}var Ux=Array.prototype,Gx=Ux.splice;function Vx(e){var t=this.__data__,r=ja(t,e);if(r<0)return!1;var i=t.length-1;return r==i?t.pop():Gx.call(t,r,1),--this.size,!0}function Xx(e){var t=this.__data__,r=ja(t,e);return r<0?void 0:t[r][1]}function Zx(e){return ja(this.__data__,e)>-1}function Kx(e,t){var r=this.__data__,i=ja(r,e);return i<0?(++this.size,r.push([e,t])):r[i][1]=t,this}function Ie(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}Ie.prototype.clear=Yx;Ie.prototype.delete=Vx;Ie.prototype.get=Xx;Ie.prototype.has=Zx;Ie.prototype.set=Kx;var mi=lr(we,"Map");function Qx(){this.size=0,this.__data__={hash:new ir,map:new(mi||Ie),string:new ir}}function Jx(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function Ya(e,t){var r=e.__data__;return Jx(t)?r[typeof t=="string"?"string":"hash"]:r.map}function tb(e){var t=Ya(this,e).delete(e);return this.size-=t?1:0,t}function eb(e){return Ya(this,e).get(e)}function rb(e){return Ya(this,e).has(e)}function ib(e,t){var r=Ya(this,e),i=r.size;return r.set(e,t),this.size+=r.size==i?0:1,this}function je(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}je.prototype.clear=Qx;je.prototype.delete=tb;je.prototype.get=eb;je.prototype.has=rb;je.prototype.set=ib;var ab="Expected a function";function Ei(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(ab);var r=function(){var i=arguments,a=t?t.apply(this,i):i[0],n=r.cache;if(n.has(a))return n.get(a);var o=e.apply(this,i);return r.cache=n.set(a,o)||n,o};return r.cache=new(Ei.Cache||je),r}Ei.Cache=je;function nb(){this.__data__=new Ie,this.size=0}function sb(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}function ob(e){return this.__data__.get(e)}function lb(e){return this.__data__.has(e)}var cb=200;function hb(e,t){var r=this.__data__;if(r instanceof Ie){var i=r.__data__;if(!mi||i.length<cb-1)return i.push([e,t]),this.size=++r.size,this;r=this.__data__=new je(i)}return r.set(e,t),this.size=r.size,this}function Dr(e){var t=this.__data__=new Ie(e);this.size=t.size}Dr.prototype.clear=nb;Dr.prototype.delete=sb;Dr.prototype.get=ob;Dr.prototype.has=lb;Dr.prototype.set=hb;var ka=function(){try{var e=lr(Object,"defineProperty");return e({},"",{}),e}catch{}}();function js(e,t,r){t=="__proto__"&&ka?ka(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}function as(e,t,r){(r!==void 0&&!Ha(e[t],r)||r===void 0&&!(t in e))&&js(e,t,r)}function ub(e){return function(t,r,i){for(var a=-1,n=Object(t),o=i(t),s=o.length;s--;){var c=o[++a];if(r(n[c],c,n)===!1)break}return t}}var db=ub(),Su=typeof exports=="object"&&exports&&!exports.nodeType&&exports,nl=Su&&typeof module=="object"&&module&&!module.nodeType&&module,pb=nl&&nl.exports===Su,sl=pb?we.Buffer:void 0,ol=sl?sl.allocUnsafe:void 0;function fb(e,t){if(t)return e.slice();var r=e.length,i=ol?ol(r):new e.constructor(r);return e.copy(i),i}var ll=we.Uint8Array;function gb(e){var t=new e.constructor(e.byteLength);return new ll(t).set(new ll(e)),t}function mb(e,t){var r=t?gb(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}function yb(e,t){var r=-1,i=e.length;for(t||(t=Array(i));++r<i;)t[r]=e[r];return t}var cl=Object.create,xb=function(){function e(){}return function(t){if(!sr(t))return{};if(cl)return cl(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();function Tu(e,t){return function(r){return e(t(r))}}var _u=Tu(Object.getPrototypeOf,Object),bb=Object.prototype;function Ua(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||bb;return e===r}function Cb(e){return typeof e.constructor=="function"&&!Ua(e)?xb(_u(e)):{}}function Fi(e){return e!=null&&typeof e=="object"}var kb="[object Arguments]";function hl(e){return Fi(e)&&Or(e)==kb}var Bu=Object.prototype,wb=Bu.hasOwnProperty,vb=Bu.propertyIsEnumerable,wa=hl(function(){return arguments}())?hl:function(e){return Fi(e)&&wb.call(e,"callee")&&!vb.call(e,"callee")},va=Array.isArray,Sb=9007199254740991;function Lu(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Sb}function Ga(e){return e!=null&&Lu(e.length)&&!Hs(e)}function Tb(e){return Fi(e)&&Ga(e)}function _b(){return!1}var Au=typeof exports=="object"&&exports&&!exports.nodeType&&exports,ul=Au&&typeof module=="object"&&module&&!module.nodeType&&module,Bb=ul&&ul.exports===Au,dl=Bb?we.Buffer:void 0,Lb=dl?dl.isBuffer:void 0,Ys=Lb||_b,Ab="[object Object]",Mb=Function.prototype,Eb=Object.prototype,Mu=Mb.toString,Fb=Eb.hasOwnProperty,$b=Mu.call(Object);function Ob(e){if(!Fi(e)||Or(e)!=Ab)return!1;var t=_u(e);if(t===null)return!0;var r=Fb.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&Mu.call(r)==$b}var Db="[object Arguments]",Rb="[object Array]",Ib="[object Boolean]",Pb="[object Date]",Nb="[object Error]",zb="[object Function]",Wb="[object Map]",qb="[object Number]",Hb="[object Object]",jb="[object RegExp]",Yb="[object Set]",Ub="[object String]",Gb="[object WeakMap]",Vb="[object ArrayBuffer]",Xb="[object DataView]",Zb="[object Float32Array]",Kb="[object Float64Array]",Qb="[object Int8Array]",Jb="[object Int16Array]",t1="[object Int32Array]",e1="[object Uint8Array]",r1="[object Uint8ClampedArray]",i1="[object Uint16Array]",a1="[object Uint32Array]",bt={};bt[Zb]=bt[Kb]=bt[Qb]=bt[Jb]=bt[t1]=bt[e1]=bt[r1]=bt[i1]=bt[a1]=!0;bt[Db]=bt[Rb]=bt[Vb]=bt[Ib]=bt[Xb]=bt[Pb]=bt[Nb]=bt[zb]=bt[Wb]=bt[qb]=bt[Hb]=bt[jb]=bt[Yb]=bt[Ub]=bt[Gb]=!1;function n1(e){return Fi(e)&&Lu(e.length)&&!!bt[Or(e)]}function s1(e){return function(t){return e(t)}}var Eu=typeof exports=="object"&&exports&&!exports.nodeType&&exports,li=Eu&&typeof module=="object"&&module&&!module.nodeType&&module,o1=li&&li.exports===Eu,wn=o1&&wu.process,pl=function(){try{var e=li&&li.require&&li.require("util").types;return e||wn&&wn.binding&&wn.binding("util")}catch{}}(),fl=pl&&pl.isTypedArray,Us=fl?s1(fl):n1;function ns(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}var l1=Object.prototype,c1=l1.hasOwnProperty;function h1(e,t,r){var i=e[t];(!(c1.call(e,t)&&Ha(i,r))||r===void 0&&!(t in e))&&js(e,t,r)}function u1(e,t,r,i){var a=!r;r||(r={});for(var n=-1,o=t.length;++n<o;){var s=t[n],c=void 0;c===void 0&&(c=e[s]),a?js(r,s,c):h1(r,s,c)}return r}function d1(e,t){for(var r=-1,i=Array(e);++r<e;)i[r]=t(r);return i}var p1=9007199254740991,f1=/^(?:0|[1-9]\d*)$/;function Fu(e,t){var r=typeof e;return t=t??p1,!!t&&(r=="number"||r!="symbol"&&f1.test(e))&&e>-1&&e%1==0&&e<t}var g1=Object.prototype,m1=g1.hasOwnProperty;function y1(e,t){var r=va(e),i=!r&&wa(e),a=!r&&!i&&Ys(e),n=!r&&!i&&!a&&Us(e),o=r||i||a||n,s=o?d1(e.length,String):[],c=s.length;for(var l in e)(t||m1.call(e,l))&&!(o&&(l=="length"||a&&(l=="offset"||l=="parent")||n&&(l=="buffer"||l=="byteLength"||l=="byteOffset")||Fu(l,c)))&&s.push(l);return s}function x1(e){var t=[];if(e!=null)for(var r in Object(e))t.push(r);return t}var b1=Object.prototype,C1=b1.hasOwnProperty;function k1(e){if(!sr(e))return x1(e);var t=Ua(e),r=[];for(var i in e)i=="constructor"&&(t||!C1.call(e,i))||r.push(i);return r}function $u(e){return Ga(e)?y1(e,!0):k1(e)}function w1(e){return u1(e,$u(e))}function v1(e,t,r,i,a,n,o){var s=ns(e,r),c=ns(t,r),l=o.get(c);if(l){as(e,r,l);return}var h=n?n(s,c,r+"",e,t,o):void 0,u=h===void 0;if(u){var p=va(c),d=!p&&Ys(c),g=!p&&!d&&Us(c);h=c,p||d||g?va(s)?h=s:Tb(s)?h=yb(s):d?(u=!1,h=fb(c,!0)):g?(u=!1,h=mb(c,!0)):h=[]:Ob(c)||wa(c)?(h=s,wa(s)?h=w1(s):(!sr(s)||Hs(s))&&(h=Cb(c))):u=!1}u&&(o.set(c,h),a(h,c,i,n,o),o.delete(c)),as(e,r,h)}function Ou(e,t,r,i,a){e!==t&&db(t,function(n,o){if(a||(a=new Dr),sr(n))v1(e,t,o,r,Ou,i,a);else{var s=i?i(ns(e,o),n,o+"",e,t,a):void 0;s===void 0&&(s=n),as(e,o,s)}},$u)}function Du(e){return e}function S1(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var gl=Math.max;function T1(e,t,r){return t=gl(t===void 0?e.length-1:t,0),function(){for(var i=arguments,a=-1,n=gl(i.length-t,0),o=Array(n);++a<n;)o[a]=i[t+a];a=-1;for(var s=Array(t+1);++a<t;)s[a]=i[a];return s[t]=r(o),S1(e,this,s)}}function _1(e){return function(){return e}}var B1=ka?function(e,t){return ka(e,"toString",{configurable:!0,enumerable:!1,value:_1(t),writable:!0})}:Du,L1=800,A1=16,M1=Date.now;function E1(e){var t=0,r=0;return function(){var i=M1(),a=A1-(i-r);if(r=i,a>0){if(++t>=L1)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var F1=E1(B1);function $1(e,t){return F1(T1(e,t,Du),e+"")}function O1(e,t,r){if(!sr(r))return!1;var i=typeof t;return(i=="number"?Ga(r)&&Fu(t,r.length):i=="string"&&t in r)?Ha(r[t],e):!1}function D1(e){return $1(function(t,r){var i=-1,a=r.length,n=a>1?r[a-1]:void 0,o=a>2?r[2]:void 0;for(n=e.length>3&&typeof n=="function"?(a--,n):void 0,o&&O1(r[0],r[1],o)&&(n=a<3?void 0:n,a=1),t=Object(t);++i<a;){var s=r[i];s&&e(t,s,i,n)}return t})}var R1=D1(function(e,t,r){Ou(e,t,r)}),I1="​",P1={curveBasis:Zi,curveBasisClosed:ig,curveBasisOpen:ag,curveBumpX:Cu,curveBumpY:ku,curveBundle:ng,curveCardinalClosed:sg,curveCardinalOpen:og,curveCardinal:Zl,curveCatmullRomClosed:lg,curveCatmullRomOpen:cg,curveCatmullRom:Kl,curveLinear:$n,curveLinearClosed:hg,curveMonotoneX:Ql,curveMonotoneY:Jl,curveNatural:tc,curveStep:ec,curveStepAfter:rc,curveStepBefore:ic},N1=/\s*(?:(\w+)(?=:):|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,z1=f(function(e,t){const r=Ru(e,/(?:init\b)|(?:initialize\b)/);let i={};if(Array.isArray(r)){const o=r.map(s=>s.args);ha(o),i=Ot(i,[...o])}else i=r.args;if(!i)return;let a=Ts(e,t);const n="config";return i[n]!==void 0&&(a==="flowchart-v2"&&(a="flowchart"),i[a]=i[n],delete i[n]),i},"detectInit"),Ru=f(function(e,t=null){var r,i;try{const a=new RegExp(`[%]{2}(?![{]${N1.source})(?=[}][%]{2}).*
`,"ig");e=e.trim().replace(a,"").replace(/'/gm,'"'),$.debug(`Detecting diagram directive${t!==null?" type:"+t:""} based on the text:${e}`);let n;const o=[];for(;(n=si.exec(e))!==null;)if(n.index===si.lastIndex&&si.lastIndex++,n&&!t||t&&((r=n[1])!=null&&r.match(t))||t&&((i=n[2])!=null&&i.match(t))){const s=n[1]?n[1]:n[2],c=n[3]?n[3].trim():n[4]?JSON.parse(n[4].trim()):null;o.push({type:s,args:c})}return o.length===0?{type:e,args:null}:o.length===1?o[0]:o}catch(a){return $.error(`ERROR: ${a.message} - Unable to parse directive type: '${t}' based on the text: '${e}'`),{type:void 0,args:null}}},"detectDirective"),W1=f(function(e){return e.replace(si,"")},"removeDirectives"),q1=f(function(e,t){for(const[r,i]of t.entries())if(i.match(e))return r;return-1},"isSubstringInArray");function Gs(e,t){if(!e)return t;const r=`curve${e.charAt(0).toUpperCase()+e.slice(1)}`;return P1[r]??t}f(Gs,"interpolateToCurve");function Iu(e,t){const r=e.trim();if(r)return t.securityLevel!=="loose"?fu(r):r}f(Iu,"formatUrl");var H1=f((e,...t)=>{const r=e.split("."),i=r.length-1,a=r[i];let n=window;for(let o=0;o<i;o++)if(n=n[r[o]],!n){$.error(`Function name: ${e} not found in window`);return}n[a](...t)},"runFunc");function Vs(e,t){return!e||!t?0:Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}f(Vs,"distance");function Pu(e){let t,r=0;e.forEach(a=>{r+=Vs(a,t),t=a});const i=r/2;return Xs(e,i)}f(Pu,"traverseEdge");function Nu(e){return e.length===1?e[0]:Pu(e)}f(Nu,"calcLabelPosition");var ml=f((e,t=2)=>{const r=Math.pow(10,t);return Math.round(e*r)/r},"roundNumber"),Xs=f((e,t)=>{let r,i=t;for(const a of e){if(r){const n=Vs(a,r);if(n===0)return r;if(n<i)i-=n;else{const o=i/n;if(o<=0)return r;if(o>=1)return{x:a.x,y:a.y};if(o>0&&o<1)return{x:ml((1-o)*r.x+o*a.x,5),y:ml((1-o)*r.y+o*a.y,5)}}}r=a}throw new Error("Could not find a suitable point for the given distance")},"calculatePoint"),j1=f((e,t,r)=>{$.info(`our points ${JSON.stringify(t)}`),t[0]!==r&&(t=t.reverse());const a=Xs(t,25),n=e?10:5,o=Math.atan2(t[0].y-a.y,t[0].x-a.x),s={x:0,y:0};return s.x=Math.sin(o)*n+(t[0].x+a.x)/2,s.y=-Math.cos(o)*n+(t[0].y+a.y)/2,s},"calcCardinalityPosition");function zu(e,t,r){const i=structuredClone(r);$.info("our points",i),t!=="start_left"&&t!=="start_right"&&i.reverse();const a=25+e,n=Xs(i,a),o=10+e*.5,s=Math.atan2(i[0].y-n.y,i[0].x-n.x),c={x:0,y:0};return t==="start_left"?(c.x=Math.sin(s+Math.PI)*o+(i[0].x+n.x)/2,c.y=-Math.cos(s+Math.PI)*o+(i[0].y+n.y)/2):t==="end_right"?(c.x=Math.sin(s-Math.PI)*o+(i[0].x+n.x)/2-5,c.y=-Math.cos(s-Math.PI)*o+(i[0].y+n.y)/2-5):t==="end_left"?(c.x=Math.sin(s)*o+(i[0].x+n.x)/2-5,c.y=-Math.cos(s)*o+(i[0].y+n.y)/2-5):(c.x=Math.sin(s)*o+(i[0].x+n.x)/2,c.y=-Math.cos(s)*o+(i[0].y+n.y)/2),c}f(zu,"calcTerminalLabelPosition");function Wu(e){let t="",r="";for(const i of e)i!==void 0&&(i.startsWith("color:")||i.startsWith("text-align:")?r=r+i+";":t=t+i+";");return{style:t,labelStyle:r}}f(Wu,"getStylesFromArray");var yl=0,Y1=f(()=>(yl++,"id-"+Math.random().toString(36).substr(2,12)+"-"+yl),"generateId");function qu(e){let t="";const r="0123456789abcdef",i=r.length;for(let a=0;a<e;a++)t+=r.charAt(Math.floor(Math.random()*i));return t}f(qu,"makeRandomHex");var U1=f(e=>qu(e.length),"random"),G1=f(function(){return{x:0,y:0,fill:void 0,anchor:"start",style:"#666",width:100,height:100,textMargin:0,rx:0,ry:0,valign:void 0,text:""}},"getTextObj"),V1=f(function(e,t){const r=t.text.replace(Fr.lineBreakRegex," "),[,i]=Va(t.fontSize),a=e.append("text");a.attr("x",t.x),a.attr("y",t.y),a.style("text-anchor",t.anchor),a.style("font-family",t.fontFamily),a.style("font-size",i),a.style("font-weight",t.fontWeight),a.attr("fill",t.fill),t.class!==void 0&&a.attr("class",t.class);const n=a.append("tspan");return n.attr("x",t.x+t.textMargin*2),n.attr("fill",t.fill),n.text(r),a},"drawSimpleText"),X1=Ei((e,t,r)=>{if(!e||(r=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",joinWith:"<br/>"},r),Fr.lineBreakRegex.test(e)))return e;const i=e.split(" ").filter(Boolean),a=[];let n="";return i.forEach((o,s)=>{const c=Re(`${o} `,r),l=Re(n,r);if(c>t){const{hyphenatedStrings:p,remainingWord:d}=Z1(o,t,"-",r);a.push(n,...p),n=d}else l+c>=t?(a.push(n),n=o):n=[n,o].filter(Boolean).join(" ");s+1===i.length&&a.push(n)}),a.filter(o=>o!=="").join(r.joinWith)},(e,t,r)=>`${e}${t}${r.fontSize}${r.fontWeight}${r.fontFamily}${r.joinWith}`),Z1=Ei((e,t,r="-",i)=>{i=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",margin:0},i);const a=[...e],n=[];let o="";return a.forEach((s,c)=>{const l=`${o}${s}`;if(Re(l,i)>=t){const u=c+1,p=a.length===u,d=`${l}${r}`;n.push(p?l:d),o=""}else o=l}),{hyphenatedStrings:n,remainingWord:o}},(e,t,r="-",i)=>`${e}${t}${r}${i.fontSize}${i.fontWeight}${i.fontFamily}`);function Hu(e,t){return Zs(e,t).height}f(Hu,"calculateTextHeight");function Re(e,t){return Zs(e,t).width}f(Re,"calculateTextWidth");var Zs=Ei((e,t)=>{const{fontSize:r=12,fontFamily:i="Arial",fontWeight:a=400}=t;if(!e)return{width:0,height:0};const[,n]=Va(r),o=["sans-serif",i],s=e.split(Fr.lineBreakRegex),c=[],l=ht("body");if(!l.remove)return{width:0,height:0,lineHeight:0};const h=l.append("svg");for(const p of o){let d=0;const g={width:0,height:0,lineHeight:0};for(const m of s){const y=G1();y.text=m||I1;const x=V1(h,y).style("font-size",n).style("font-weight",a).style("font-family",p),b=(x._groups||x)[0][0].getBBox();if(b.width===0&&b.height===0)throw new Error("svg element not in render tree");g.width=Math.round(Math.max(g.width,b.width)),d=Math.round(b.height),g.height+=d,g.lineHeight=Math.round(Math.max(g.lineHeight,d))}c.push(g)}h.remove();const u=isNaN(c[1].height)||isNaN(c[1].width)||isNaN(c[1].lineHeight)||c[0].height>c[1].height&&c[0].width>c[1].width&&c[0].lineHeight>c[1].lineHeight?0:1;return c[u]},(e,t)=>`${e}${t.fontSize}${t.fontWeight}${t.fontFamily}`),Bi,K1=(Bi=class{constructor(t=!1,r){this.count=0,this.count=r?r.length:0,this.next=t?()=>this.count++:()=>Date.now()}},f(Bi,"InitIDGenerator"),Bi),ji,Q1=f(function(e){return ji=ji||document.createElement("div"),e=escape(e).replace(/%26/g,"&").replace(/%23/g,"#").replace(/%3B/g,";"),ji.innerHTML=e,unescape(ji.textContent)},"entityDecode");function Ks(e){return"str"in e}f(Ks,"isDetailedError");var J1=f((e,t,r,i)=>{var n;if(!i)return;const a=(n=e.node())==null?void 0:n.getBBox();a&&e.append("text").text(i).attr("text-anchor","middle").attr("x",a.x+a.width/2).attr("y",-r).attr("class",t)},"insertTitle"),Va=f(e=>{if(typeof e=="number")return[e,e+"px"];const t=parseInt(e??"",10);return Number.isNaN(t)?[void 0,void 0]:e===String(t)?[t,e+"px"]:[t,e]},"parseFontSize");function Qs(e,t){return R1({},e,t)}f(Qs,"cleanAndMerge");var ce={assignWithDepth:Ot,wrapLabel:X1,calculateTextHeight:Hu,calculateTextWidth:Re,calculateTextDimensions:Zs,cleanAndMerge:Qs,detectInit:z1,detectDirective:Ru,isSubstringInArray:q1,interpolateToCurve:Gs,calcLabelPosition:Nu,calcCardinalityPosition:j1,calcTerminalLabelPosition:zu,formatUrl:Iu,getStylesFromArray:Wu,generateId:Y1,random:U1,runFunc:H1,entityDecode:Q1,insertTitle:J1,isLabelCoordinateInPath:ju,parseFontSize:Va,InitIDGenerator:K1},tC=f(function(e){let t=e;return t=t.replace(/style.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/classDef.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/#\w+;/g,function(r){const i=r.substring(1,r.length-1);return/^\+?\d+$/.test(i)?"ﬂ°°"+i+"¶ß":"ﬂ°"+i+"¶ß"}),t},"encodeEntities"),cr=f(function(e){return e.replace(/ﬂ°°/g,"&#").replace(/ﬂ°/g,"&").replace(/¶ß/g,";")},"decodeEntities"),US=f((e,t,{counter:r=0,prefix:i,suffix:a},n)=>n||`${i?`${i}_`:""}${e}_${t}_${r}${a?`_${a}`:""}`,"getEdgeId");function Jt(e){return e??null}f(Jt,"handleUndefinedAttr");function ju(e,t){const r=Math.round(e.x),i=Math.round(e.y),a=t.replace(/(\d+\.\d+)/g,n=>Math.round(parseFloat(n)).toString());return a.includes(r.toString())||a.includes(i.toString())}f(ju,"isLabelCoordinateInPath");const eC=Object.freeze({left:0,top:0,width:16,height:16}),Sa=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),Yu=Object.freeze({...eC,...Sa}),rC=Object.freeze({...Yu,body:"",hidden:!1}),iC=Object.freeze({width:null,height:null}),aC=Object.freeze({...iC,...Sa}),nC=(e,t,r,i="")=>{const a=e.split(":");if(e.slice(0,1)==="@"){if(a.length<2||a.length>3)return null;i=a.shift().slice(1)}if(a.length>3||!a.length)return null;if(a.length>1){const s=a.pop(),c=a.pop(),l={provider:a.length>0?a[0]:i,prefix:c,name:s};return vn(l)?l:null}const n=a[0],o=n.split("-");if(o.length>1){const s={provider:i,prefix:o.shift(),name:o.join("-")};return vn(s)?s:null}if(r&&i===""){const s={provider:i,prefix:"",name:n};return vn(s,r)?s:null}return null},vn=(e,t)=>e?!!((t&&e.prefix===""||e.prefix)&&e.name):!1;function sC(e,t){const r={};!e.hFlip!=!t.hFlip&&(r.hFlip=!0),!e.vFlip!=!t.vFlip&&(r.vFlip=!0);const i=((e.rotate||0)+(t.rotate||0))%4;return i&&(r.rotate=i),r}function xl(e,t){const r=sC(e,t);for(const i in rC)i in Sa?i in e&&!(i in r)&&(r[i]=Sa[i]):i in t?r[i]=t[i]:i in e&&(r[i]=e[i]);return r}function oC(e,t){const r=e.icons,i=e.aliases||Object.create(null),a=Object.create(null);function n(o){if(r[o])return a[o]=[];if(!(o in a)){a[o]=null;const s=i[o]&&i[o].parent,c=s&&n(s);c&&(a[o]=[s].concat(c))}return a[o]}return(t||Object.keys(r).concat(Object.keys(i))).forEach(n),a}function bl(e,t,r){const i=e.icons,a=e.aliases||Object.create(null);let n={};function o(s){n=xl(i[s]||a[s],n)}return o(t),r.forEach(o),xl(e,n)}function lC(e,t){if(e.icons[t])return bl(e,t,[]);const r=oC(e,[t])[t];return r?bl(e,t,r):null}const cC=/(-?[0-9.]*[0-9]+[0-9.]*)/g,hC=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function Cl(e,t,r){if(t===1)return e;if(r=r||100,typeof e=="number")return Math.ceil(e*t*r)/r;if(typeof e!="string")return e;const i=e.split(cC);if(i===null||!i.length)return e;const a=[];let n=i.shift(),o=hC.test(n);for(;;){if(o){const s=parseFloat(n);isNaN(s)?a.push(n):a.push(Math.ceil(s*t*r)/r)}else a.push(n);if(n=i.shift(),n===void 0)return a.join("");o=!o}}function uC(e,t="defs"){let r="";const i=e.indexOf("<"+t);for(;i>=0;){const a=e.indexOf(">",i),n=e.indexOf("</"+t);if(a===-1||n===-1)break;const o=e.indexOf(">",n);if(o===-1)break;r+=e.slice(a+1,n).trim(),e=e.slice(0,i).trim()+e.slice(o+1)}return{defs:r,content:e}}function dC(e,t){return e?"<defs>"+e+"</defs>"+t:t}function pC(e,t,r){const i=uC(e);return dC(i.defs,t+i.content+r)}const fC=e=>e==="unset"||e==="undefined"||e==="none";function gC(e,t){const r={...Yu,...e},i={...aC,...t},a={left:r.left,top:r.top,width:r.width,height:r.height};let n=r.body;[r,i].forEach(m=>{const y=[],x=m.hFlip,b=m.vFlip;let k=m.rotate;x?b?k+=2:(y.push("translate("+(a.width+a.left).toString()+" "+(0-a.top).toString()+")"),y.push("scale(-1 1)"),a.top=a.left=0):b&&(y.push("translate("+(0-a.left).toString()+" "+(a.height+a.top).toString()+")"),y.push("scale(1 -1)"),a.top=a.left=0);let T;switch(k<0&&(k-=Math.floor(k/4)*4),k=k%4,k){case 1:T=a.height/2+a.top,y.unshift("rotate(90 "+T.toString()+" "+T.toString()+")");break;case 2:y.unshift("rotate(180 "+(a.width/2+a.left).toString()+" "+(a.height/2+a.top).toString()+")");break;case 3:T=a.width/2+a.left,y.unshift("rotate(-90 "+T.toString()+" "+T.toString()+")");break}k%2===1&&(a.left!==a.top&&(T=a.left,a.left=a.top,a.top=T),a.width!==a.height&&(T=a.width,a.width=a.height,a.height=T)),y.length&&(n=pC(n,'<g transform="'+y.join(" ")+'">',"</g>"))});const o=i.width,s=i.height,c=a.width,l=a.height;let h,u;o===null?(u=s===null?"1em":s==="auto"?l:s,h=Cl(u,c/l)):(h=o==="auto"?c:o,u=s===null?Cl(h,l/c):s==="auto"?l:s);const p={},d=(m,y)=>{fC(y)||(p[m]=y.toString())};d("width",h),d("height",u);const g=[a.left,a.top,c,l];return p.viewBox=g.join(" "),{attributes:p,viewBox:g,body:n}}const mC=/\sid="(\S+)"/g,yC="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16);let xC=0;function bC(e,t=yC){const r=[];let i;for(;i=mC.exec(e);)r.push(i[1]);if(!r.length)return e;const a="suffix"+(Math.random()*16777216|Date.now()).toString(16);return r.forEach(n=>{const o=typeof t=="function"?t(n):t+(xC++).toString(),s=n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");e=e.replace(new RegExp('([#;"])('+s+')([")]|\\.[a-z])',"g"),"$1"+o+a+"$3")}),e=e.replace(new RegExp(a,"g"),""),e}function CC(e,t){let r=e.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const i in t)r+=" "+i+'="'+t[i]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+r+">"+e+"</svg>"}function Js(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var hr=Js();function Uu(e){hr=e}var ci={exec:()=>null};function pt(e,t=""){let r=typeof e=="string"?e:e.source,i={replace:(a,n)=>{let o=typeof n=="string"?n:n.source;return o=o.replace(Ut.caret,"$1"),r=r.replace(a,o),i},getRegex:()=>new RegExp(r,t)};return i}var Ut={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},kC=/^(?:[ \t]*(?:\n|$))+/,wC=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,vC=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,$i=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,SC=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,to=/(?:[*+-]|\d{1,9}[.)])/,Gu=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Vu=pt(Gu).replace(/bull/g,to).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),TC=pt(Gu).replace(/bull/g,to).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),eo=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,_C=/^[^\n]+/,ro=/(?!\s*\])(?:\\[\s\S]|[^\[\]\\])+/,BC=pt(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",ro).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),LC=pt(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,to).getRegex(),Xa="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",io=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,AC=pt("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",io).replace("tag",Xa).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Xu=pt(eo).replace("hr",$i).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Xa).getRegex(),MC=pt(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Xu).getRegex(),ao={blockquote:MC,code:wC,def:BC,fences:vC,heading:SC,hr:$i,html:AC,lheading:Vu,list:LC,newline:kC,paragraph:Xu,table:ci,text:_C},kl=pt("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",$i).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Xa).getRegex(),EC={...ao,lheading:TC,table:kl,paragraph:pt(eo).replace("hr",$i).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",kl).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Xa).getRegex()},FC={...ao,html:pt(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",io).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ci,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:pt(eo).replace("hr",$i).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Vu).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},$C=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,OC=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Zu=/^( {2,}|\\)\n(?!\s*$)/,DC=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,Za=/[\p{P}\p{S}]/u,no=/[\s\p{P}\p{S}]/u,Ku=/[^\s\p{P}\p{S}]/u,RC=pt(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,no).getRegex(),Qu=/(?!~)[\p{P}\p{S}]/u,IC=/(?!~)[\s\p{P}\p{S}]/u,PC=/(?:[^\s\p{P}\p{S}]|~)/u,NC=/\[[^\[\]]*?\]\((?:\\[\s\S]|[^\\\(\)]|\((?:\\[\s\S]|[^\\\(\)])*\))*\)|`[^`]*?`|<(?! )[^<>]*?>/g,Ju=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,zC=pt(Ju,"u").replace(/punct/g,Za).getRegex(),WC=pt(Ju,"u").replace(/punct/g,Qu).getRegex(),td="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",qC=pt(td,"gu").replace(/notPunctSpace/g,Ku).replace(/punctSpace/g,no).replace(/punct/g,Za).getRegex(),HC=pt(td,"gu").replace(/notPunctSpace/g,PC).replace(/punctSpace/g,IC).replace(/punct/g,Qu).getRegex(),jC=pt("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Ku).replace(/punctSpace/g,no).replace(/punct/g,Za).getRegex(),YC=pt(/\\(punct)/,"gu").replace(/punct/g,Za).getRegex(),UC=pt(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),GC=pt(io).replace("(?:-->|$)","-->").getRegex(),VC=pt("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",GC).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Ta=/(?:\[(?:\\[\s\S]|[^\[\]\\])*\]|\\[\s\S]|`[^`]*`|[^\[\]\\`])*?/,XC=pt(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",Ta).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),ed=pt(/^!?\[(label)\]\[(ref)\]/).replace("label",Ta).replace("ref",ro).getRegex(),rd=pt(/^!?\[(ref)\](?:\[\])?/).replace("ref",ro).getRegex(),ZC=pt("reflink|nolink(?!\\()","g").replace("reflink",ed).replace("nolink",rd).getRegex(),so={_backpedal:ci,anyPunctuation:YC,autolink:UC,blockSkip:NC,br:Zu,code:OC,del:ci,emStrongLDelim:zC,emStrongRDelimAst:qC,emStrongRDelimUnd:jC,escape:$C,link:XC,nolink:rd,punctuation:RC,reflink:ed,reflinkSearch:ZC,tag:VC,text:DC,url:ci},KC={...so,link:pt(/^!?\[(label)\]\((.*?)\)/).replace("label",Ta).getRegex(),reflink:pt(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Ta).getRegex()},ss={...so,emStrongRDelimAst:HC,emStrongLDelim:WC,url:pt(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\[\s\S]|[^\\])*?(?:\\[\s\S]|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},QC={...ss,br:pt(Zu).replace("{2,}","*").getRegex(),text:pt(ss.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Yi={normal:ao,gfm:EC,pedantic:FC},Gr={normal:so,gfm:ss,breaks:QC,pedantic:KC},JC={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},wl=e=>JC[e];function me(e,t){if(t){if(Ut.escapeTest.test(e))return e.replace(Ut.escapeReplace,wl)}else if(Ut.escapeTestNoEncode.test(e))return e.replace(Ut.escapeReplaceNoEncode,wl);return e}function vl(e){try{e=encodeURI(e).replace(Ut.percentDecode,"%")}catch{return null}return e}function Sl(e,t){var n;let r=e.replace(Ut.findPipe,(o,s,c)=>{let l=!1,h=s;for(;--h>=0&&c[h]==="\\";)l=!l;return l?"|":" |"}),i=r.split(Ut.splitPipe),a=0;if(i[0].trim()||i.shift(),i.length>0&&!((n=i.at(-1))!=null&&n.trim())&&i.pop(),t)if(i.length>t)i.splice(t);else for(;i.length<t;)i.push("");for(;a<i.length;a++)i[a]=i[a].trim().replace(Ut.slashPipe,"|");return i}function Vr(e,t,r){let i=e.length;if(i===0)return"";let a=0;for(;a<i;){let n=e.charAt(i-a-1);if(n===t&&!r)a++;else if(n!==t&&r)a++;else break}return e.slice(0,i-a)}function t2(e,t){if(e.indexOf(t[1])===-1)return-1;let r=0;for(let i=0;i<e.length;i++)if(e[i]==="\\")i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&(r--,r<0))return i;return r>0?-2:-1}function Tl(e,t,r,i,a){let n=t.href,o=t.title||null,s=e[1].replace(a.other.outputLinkReplace,"$1");i.state.inLink=!0;let c={type:e[0].charAt(0)==="!"?"image":"link",raw:r,href:n,title:o,text:s,tokens:i.inlineTokens(s)};return i.state.inLink=!1,c}function e2(e,t,r){let i=e.match(r.other.indentCodeCompensation);if(i===null)return t;let a=i[1];return t.split(`
`).map(n=>{let o=n.match(r.other.beginningSpace);if(o===null)return n;let[s]=o;return s.length>=a.length?n.slice(a.length):n}).join(`
`)}var _a=class{constructor(t){mt(this,"options");mt(this,"rules");mt(this,"lexer");this.options=t||hr}space(t){let r=this.rules.block.newline.exec(t);if(r&&r[0].length>0)return{type:"space",raw:r[0]}}code(t){let r=this.rules.block.code.exec(t);if(r){let i=r[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:r[0],codeBlockStyle:"indented",text:this.options.pedantic?i:Vr(i,`
`)}}}fences(t){let r=this.rules.block.fences.exec(t);if(r){let i=r[0],a=e2(i,r[3]||"",this.rules);return{type:"code",raw:i,lang:r[2]?r[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):r[2],text:a}}}heading(t){let r=this.rules.block.heading.exec(t);if(r){let i=r[2].trim();if(this.rules.other.endingHash.test(i)){let a=Vr(i,"#");(this.options.pedantic||!a||this.rules.other.endingSpaceChar.test(a))&&(i=a.trim())}return{type:"heading",raw:r[0],depth:r[1].length,text:i,tokens:this.lexer.inline(i)}}}hr(t){let r=this.rules.block.hr.exec(t);if(r)return{type:"hr",raw:Vr(r[0],`
`)}}blockquote(t){let r=this.rules.block.blockquote.exec(t);if(r){let i=Vr(r[0],`
`).split(`
`),a="",n="",o=[];for(;i.length>0;){let s=!1,c=[],l;for(l=0;l<i.length;l++)if(this.rules.other.blockquoteStart.test(i[l]))c.push(i[l]),s=!0;else if(!s)c.push(i[l]);else break;i=i.slice(l);let h=c.join(`
`),u=h.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");a=a?`${a}
${h}`:h,n=n?`${n}
${u}`:u;let p=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(u,o,!0),this.lexer.state.top=p,i.length===0)break;let d=o.at(-1);if((d==null?void 0:d.type)==="code")break;if((d==null?void 0:d.type)==="blockquote"){let g=d,m=g.raw+`
`+i.join(`
`),y=this.blockquote(m);o[o.length-1]=y,a=a.substring(0,a.length-g.raw.length)+y.raw,n=n.substring(0,n.length-g.text.length)+y.text;break}else if((d==null?void 0:d.type)==="list"){let g=d,m=g.raw+`
`+i.join(`
`),y=this.list(m);o[o.length-1]=y,a=a.substring(0,a.length-d.raw.length)+y.raw,n=n.substring(0,n.length-g.raw.length)+y.raw,i=m.substring(o.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:a,tokens:o,text:n}}}list(t){let r=this.rules.block.list.exec(t);if(r){let i=r[1].trim(),a=i.length>1,n={type:"list",raw:"",ordered:a,start:a?+i.slice(0,-1):"",loose:!1,items:[]};i=a?`\\d{1,9}\\${i.slice(-1)}`:`\\${i}`,this.options.pedantic&&(i=a?i:"[*+-]");let o=this.rules.other.listItemRegex(i),s=!1;for(;t;){let l=!1,h="",u="";if(!(r=o.exec(t))||this.rules.block.hr.test(t))break;h=r[0],t=t.substring(h.length);let p=r[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,b=>" ".repeat(3*b.length)),d=t.split(`
`,1)[0],g=!p.trim(),m=0;if(this.options.pedantic?(m=2,u=p.trimStart()):g?m=r[1].length+1:(m=r[2].search(this.rules.other.nonSpaceChar),m=m>4?1:m,u=p.slice(m),m+=r[1].length),g&&this.rules.other.blankLine.test(d)&&(h+=d+`
`,t=t.substring(d.length+1),l=!0),!l){let b=this.rules.other.nextBulletRegex(m),k=this.rules.other.hrRegex(m),T=this.rules.other.fencesBeginRegex(m),v=this.rules.other.headingBeginRegex(m),C=this.rules.other.htmlBeginRegex(m);for(;t;){let S=t.split(`
`,1)[0],O;if(d=S,this.options.pedantic?(d=d.replace(this.rules.other.listReplaceNesting,"  "),O=d):O=d.replace(this.rules.other.tabCharGlobal,"    "),T.test(d)||v.test(d)||C.test(d)||b.test(d)||k.test(d))break;if(O.search(this.rules.other.nonSpaceChar)>=m||!d.trim())u+=`
`+O.slice(m);else{if(g||p.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||T.test(p)||v.test(p)||k.test(p))break;u+=`
`+d}!g&&!d.trim()&&(g=!0),h+=S+`
`,t=t.substring(S.length+1),p=O.slice(m)}}n.loose||(s?n.loose=!0:this.rules.other.doubleBlankLine.test(h)&&(s=!0));let y=null,x;this.options.gfm&&(y=this.rules.other.listIsTask.exec(u),y&&(x=y[0]!=="[ ] ",u=u.replace(this.rules.other.listReplaceTask,""))),n.items.push({type:"list_item",raw:h,task:!!y,checked:x,loose:!1,text:u,tokens:[]}),n.raw+=h}let c=n.items.at(-1);if(c)c.raw=c.raw.trimEnd(),c.text=c.text.trimEnd();else return;n.raw=n.raw.trimEnd();for(let l=0;l<n.items.length;l++)if(this.lexer.state.top=!1,n.items[l].tokens=this.lexer.blockTokens(n.items[l].text,[]),!n.loose){let h=n.items[l].tokens.filter(p=>p.type==="space"),u=h.length>0&&h.some(p=>this.rules.other.anyLine.test(p.raw));n.loose=u}if(n.loose)for(let l=0;l<n.items.length;l++)n.items[l].loose=!0;return n}}html(t){let r=this.rules.block.html.exec(t);if(r)return{type:"html",block:!0,raw:r[0],pre:r[1]==="pre"||r[1]==="script"||r[1]==="style",text:r[0]}}def(t){let r=this.rules.block.def.exec(t);if(r){let i=r[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),a=r[2]?r[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",n=r[3]?r[3].substring(1,r[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):r[3];return{type:"def",tag:i,raw:r[0],href:a,title:n}}}table(t){var s;let r=this.rules.block.table.exec(t);if(!r||!this.rules.other.tableDelimiter.test(r[2]))return;let i=Sl(r[1]),a=r[2].replace(this.rules.other.tableAlignChars,"").split("|"),n=(s=r[3])!=null&&s.trim()?r[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],o={type:"table",raw:r[0],header:[],align:[],rows:[]};if(i.length===a.length){for(let c of a)this.rules.other.tableAlignRight.test(c)?o.align.push("right"):this.rules.other.tableAlignCenter.test(c)?o.align.push("center"):this.rules.other.tableAlignLeft.test(c)?o.align.push("left"):o.align.push(null);for(let c=0;c<i.length;c++)o.header.push({text:i[c],tokens:this.lexer.inline(i[c]),header:!0,align:o.align[c]});for(let c of n)o.rows.push(Sl(c,o.header.length).map((l,h)=>({text:l,tokens:this.lexer.inline(l),header:!1,align:o.align[h]})));return o}}lheading(t){let r=this.rules.block.lheading.exec(t);if(r)return{type:"heading",raw:r[0],depth:r[2].charAt(0)==="="?1:2,text:r[1],tokens:this.lexer.inline(r[1])}}paragraph(t){let r=this.rules.block.paragraph.exec(t);if(r){let i=r[1].charAt(r[1].length-1)===`
`?r[1].slice(0,-1):r[1];return{type:"paragraph",raw:r[0],text:i,tokens:this.lexer.inline(i)}}}text(t){let r=this.rules.block.text.exec(t);if(r)return{type:"text",raw:r[0],text:r[0],tokens:this.lexer.inline(r[0])}}escape(t){let r=this.rules.inline.escape.exec(t);if(r)return{type:"escape",raw:r[0],text:r[1]}}tag(t){let r=this.rules.inline.tag.exec(t);if(r)return!this.lexer.state.inLink&&this.rules.other.startATag.test(r[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(r[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(r[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(r[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:r[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:r[0]}}link(t){let r=this.rules.inline.link.exec(t);if(r){let i=r[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(i)){if(!this.rules.other.endAngleBracket.test(i))return;let o=Vr(i.slice(0,-1),"\\");if((i.length-o.length)%2===0)return}else{let o=t2(r[2],"()");if(o===-2)return;if(o>-1){let s=(r[0].indexOf("!")===0?5:4)+r[1].length+o;r[2]=r[2].substring(0,o),r[0]=r[0].substring(0,s).trim(),r[3]=""}}let a=r[2],n="";if(this.options.pedantic){let o=this.rules.other.pedanticHrefTitle.exec(a);o&&(a=o[1],n=o[3])}else n=r[3]?r[3].slice(1,-1):"";return a=a.trim(),this.rules.other.startAngleBracket.test(a)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(i)?a=a.slice(1):a=a.slice(1,-1)),Tl(r,{href:a&&a.replace(this.rules.inline.anyPunctuation,"$1"),title:n&&n.replace(this.rules.inline.anyPunctuation,"$1")},r[0],this.lexer,this.rules)}}reflink(t,r){let i;if((i=this.rules.inline.reflink.exec(t))||(i=this.rules.inline.nolink.exec(t))){let a=(i[2]||i[1]).replace(this.rules.other.multipleSpaceGlobal," "),n=r[a.toLowerCase()];if(!n){let o=i[0].charAt(0);return{type:"text",raw:o,text:o}}return Tl(i,n,i[0],this.lexer,this.rules)}}emStrong(t,r,i=""){let a=this.rules.inline.emStrongLDelim.exec(t);if(!(!a||a[3]&&i.match(this.rules.other.unicodeAlphaNumeric))&&(!(a[1]||a[2])||!i||this.rules.inline.punctuation.exec(i))){let n=[...a[0]].length-1,o,s,c=n,l=0,h=a[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(h.lastIndex=0,r=r.slice(-1*t.length+n);(a=h.exec(r))!=null;){if(o=a[1]||a[2]||a[3]||a[4]||a[5]||a[6],!o)continue;if(s=[...o].length,a[3]||a[4]){c+=s;continue}else if((a[5]||a[6])&&n%3&&!((n+s)%3)){l+=s;continue}if(c-=s,c>0)continue;s=Math.min(s,s+c+l);let u=[...a[0]][0].length,p=t.slice(0,n+a.index+u+s);if(Math.min(n,s)%2){let g=p.slice(1,-1);return{type:"em",raw:p,text:g,tokens:this.lexer.inlineTokens(g)}}let d=p.slice(2,-2);return{type:"strong",raw:p,text:d,tokens:this.lexer.inlineTokens(d)}}}}codespan(t){let r=this.rules.inline.code.exec(t);if(r){let i=r[2].replace(this.rules.other.newLineCharGlobal," "),a=this.rules.other.nonSpaceChar.test(i),n=this.rules.other.startingSpaceChar.test(i)&&this.rules.other.endingSpaceChar.test(i);return a&&n&&(i=i.substring(1,i.length-1)),{type:"codespan",raw:r[0],text:i}}}br(t){let r=this.rules.inline.br.exec(t);if(r)return{type:"br",raw:r[0]}}del(t){let r=this.rules.inline.del.exec(t);if(r)return{type:"del",raw:r[0],text:r[2],tokens:this.lexer.inlineTokens(r[2])}}autolink(t){let r=this.rules.inline.autolink.exec(t);if(r){let i,a;return r[2]==="@"?(i=r[1],a="mailto:"+i):(i=r[1],a=i),{type:"link",raw:r[0],text:i,href:a,tokens:[{type:"text",raw:i,text:i}]}}}url(t){var i;let r;if(r=this.rules.inline.url.exec(t)){let a,n;if(r[2]==="@")a=r[0],n="mailto:"+a;else{let o;do o=r[0],r[0]=((i=this.rules.inline._backpedal.exec(r[0]))==null?void 0:i[0])??"";while(o!==r[0]);a=r[0],r[1]==="www."?n="http://"+r[0]:n=r[0]}return{type:"link",raw:r[0],text:a,href:n,tokens:[{type:"text",raw:a,text:a}]}}}inlineText(t){let r=this.rules.inline.text.exec(t);if(r){let i=this.lexer.state.inRawBlock;return{type:"text",raw:r[0],text:r[0],escaped:i}}}},Ee=class os{constructor(t){mt(this,"tokens");mt(this,"options");mt(this,"state");mt(this,"tokenizer");mt(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||hr,this.options.tokenizer=this.options.tokenizer||new _a,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let r={other:Ut,block:Yi.normal,inline:Gr.normal};this.options.pedantic?(r.block=Yi.pedantic,r.inline=Gr.pedantic):this.options.gfm&&(r.block=Yi.gfm,this.options.breaks?r.inline=Gr.breaks:r.inline=Gr.gfm),this.tokenizer.rules=r}static get rules(){return{block:Yi,inline:Gr}}static lex(t,r){return new os(r).lex(t)}static lexInline(t,r){return new os(r).inlineTokens(t)}lex(t){t=t.replace(Ut.carriageReturn,`
`),this.blockTokens(t,this.tokens);for(let r=0;r<this.inlineQueue.length;r++){let i=this.inlineQueue[r];this.inlineTokens(i.src,i.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,r=[],i=!1){var a,n,o;for(this.options.pedantic&&(t=t.replace(Ut.tabCharGlobal,"    ").replace(Ut.spaceLine,""));t;){let s;if((n=(a=this.options.extensions)==null?void 0:a.block)!=null&&n.some(l=>(s=l.call({lexer:this},t,r))?(t=t.substring(s.raw.length),r.push(s),!0):!1))continue;if(s=this.tokenizer.space(t)){t=t.substring(s.raw.length);let l=r.at(-1);s.raw.length===1&&l!==void 0?l.raw+=`
`:r.push(s);continue}if(s=this.tokenizer.code(t)){t=t.substring(s.raw.length);let l=r.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=(l.raw.endsWith(`
`)?"":`
`)+s.raw,l.text+=`
`+s.text,this.inlineQueue.at(-1).src=l.text):r.push(s);continue}if(s=this.tokenizer.fences(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.heading(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.hr(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.blockquote(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.list(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.html(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.def(t)){t=t.substring(s.raw.length);let l=r.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=(l.raw.endsWith(`
`)?"":`
`)+s.raw,l.text+=`
`+s.raw,this.inlineQueue.at(-1).src=l.text):this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title},r.push(s));continue}if(s=this.tokenizer.table(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.lheading(t)){t=t.substring(s.raw.length),r.push(s);continue}let c=t;if((o=this.options.extensions)!=null&&o.startBlock){let l=1/0,h=t.slice(1),u;this.options.extensions.startBlock.forEach(p=>{u=p.call({lexer:this},h),typeof u=="number"&&u>=0&&(l=Math.min(l,u))}),l<1/0&&l>=0&&(c=t.substring(0,l+1))}if(this.state.top&&(s=this.tokenizer.paragraph(c))){let l=r.at(-1);i&&(l==null?void 0:l.type)==="paragraph"?(l.raw+=(l.raw.endsWith(`
`)?"":`
`)+s.raw,l.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):r.push(s),i=c.length!==t.length,t=t.substring(s.raw.length);continue}if(s=this.tokenizer.text(t)){t=t.substring(s.raw.length);let l=r.at(-1);(l==null?void 0:l.type)==="text"?(l.raw+=(l.raw.endsWith(`
`)?"":`
`)+s.raw,l.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):r.push(s);continue}if(t){let l="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(l);break}else throw new Error(l)}}return this.state.top=!0,r}inline(t,r=[]){return this.inlineQueue.push({src:t,tokens:r}),r}inlineTokens(t,r=[]){var s,c,l;let i=t,a=null;if(this.tokens.links){let h=Object.keys(this.tokens.links);if(h.length>0)for(;(a=this.tokenizer.rules.inline.reflinkSearch.exec(i))!=null;)h.includes(a[0].slice(a[0].lastIndexOf("[")+1,-1))&&(i=i.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(a=this.tokenizer.rules.inline.anyPunctuation.exec(i))!=null;)i=i.slice(0,a.index)+"++"+i.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(a=this.tokenizer.rules.inline.blockSkip.exec(i))!=null;)i=i.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let n=!1,o="";for(;t;){n||(o=""),n=!1;let h;if((c=(s=this.options.extensions)==null?void 0:s.inline)!=null&&c.some(p=>(h=p.call({lexer:this},t,r))?(t=t.substring(h.raw.length),r.push(h),!0):!1))continue;if(h=this.tokenizer.escape(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.tag(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.link(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(h.raw.length);let p=r.at(-1);h.type==="text"&&(p==null?void 0:p.type)==="text"?(p.raw+=h.raw,p.text+=h.text):r.push(h);continue}if(h=this.tokenizer.emStrong(t,i,o)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.codespan(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.br(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.del(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.autolink(t)){t=t.substring(h.raw.length),r.push(h);continue}if(!this.state.inLink&&(h=this.tokenizer.url(t))){t=t.substring(h.raw.length),r.push(h);continue}let u=t;if((l=this.options.extensions)!=null&&l.startInline){let p=1/0,d=t.slice(1),g;this.options.extensions.startInline.forEach(m=>{g=m.call({lexer:this},d),typeof g=="number"&&g>=0&&(p=Math.min(p,g))}),p<1/0&&p>=0&&(u=t.substring(0,p+1))}if(h=this.tokenizer.inlineText(u)){t=t.substring(h.raw.length),h.raw.slice(-1)!=="_"&&(o=h.raw.slice(-1)),n=!0;let p=r.at(-1);(p==null?void 0:p.type)==="text"?(p.raw+=h.raw,p.text+=h.text):r.push(h);continue}if(t){let p="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(p);break}else throw new Error(p)}}return r}},Ba=class{constructor(t){mt(this,"options");mt(this,"parser");this.options=t||hr}space(t){return""}code({text:t,lang:r,escaped:i}){var o;let a=(o=(r||"").match(Ut.notSpaceStart))==null?void 0:o[0],n=t.replace(Ut.endingNewline,"")+`
`;return a?'<pre><code class="language-'+me(a)+'">'+(i?n:me(n,!0))+`</code></pre>
`:"<pre><code>"+(i?n:me(n,!0))+`</code></pre>
`}blockquote({tokens:t}){return`<blockquote>
${this.parser.parse(t)}</blockquote>
`}html({text:t}){return t}def(t){return""}heading({tokens:t,depth:r}){return`<h${r}>${this.parser.parseInline(t)}</h${r}>
`}hr(t){return`<hr>
`}list(t){let r=t.ordered,i=t.start,a="";for(let s=0;s<t.items.length;s++){let c=t.items[s];a+=this.listitem(c)}let n=r?"ol":"ul",o=r&&i!==1?' start="'+i+'"':"";return"<"+n+o+`>
`+a+"</"+n+`>
`}listitem(t){var i;let r="";if(t.task){let a=this.checkbox({checked:!!t.checked});t.loose?((i=t.tokens[0])==null?void 0:i.type)==="paragraph"?(t.tokens[0].text=a+" "+t.tokens[0].text,t.tokens[0].tokens&&t.tokens[0].tokens.length>0&&t.tokens[0].tokens[0].type==="text"&&(t.tokens[0].tokens[0].text=a+" "+me(t.tokens[0].tokens[0].text),t.tokens[0].tokens[0].escaped=!0)):t.tokens.unshift({type:"text",raw:a+" ",text:a+" ",escaped:!0}):r+=a+" "}return r+=this.parser.parse(t.tokens,!!t.loose),`<li>${r}</li>
`}checkbox({checked:t}){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:t}){return`<p>${this.parser.parseInline(t)}</p>
`}table(t){let r="",i="";for(let n=0;n<t.header.length;n++)i+=this.tablecell(t.header[n]);r+=this.tablerow({text:i});let a="";for(let n=0;n<t.rows.length;n++){let o=t.rows[n];i="";for(let s=0;s<o.length;s++)i+=this.tablecell(o[s]);a+=this.tablerow({text:i})}return a&&(a=`<tbody>${a}</tbody>`),`<table>
<thead>
`+r+`</thead>
`+a+`</table>
`}tablerow({text:t}){return`<tr>
${t}</tr>
`}tablecell(t){let r=this.parser.parseInline(t.tokens),i=t.header?"th":"td";return(t.align?`<${i} align="${t.align}">`:`<${i}>`)+r+`</${i}>
`}strong({tokens:t}){return`<strong>${this.parser.parseInline(t)}</strong>`}em({tokens:t}){return`<em>${this.parser.parseInline(t)}</em>`}codespan({text:t}){return`<code>${me(t,!0)}</code>`}br(t){return"<br>"}del({tokens:t}){return`<del>${this.parser.parseInline(t)}</del>`}link({href:t,title:r,tokens:i}){let a=this.parser.parseInline(i),n=vl(t);if(n===null)return a;t=n;let o='<a href="'+t+'"';return r&&(o+=' title="'+me(r)+'"'),o+=">"+a+"</a>",o}image({href:t,title:r,text:i,tokens:a}){a&&(i=this.parser.parseInline(a,this.parser.textRenderer));let n=vl(t);if(n===null)return me(i);t=n;let o=`<img src="${t}" alt="${i}"`;return r&&(o+=` title="${me(r)}"`),o+=">",o}text(t){return"tokens"in t&&t.tokens?this.parser.parseInline(t.tokens):"escaped"in t&&t.escaped?t.text:me(t.text)}},oo=class{strong({text:t}){return t}em({text:t}){return t}codespan({text:t}){return t}del({text:t}){return t}html({text:t}){return t}text({text:t}){return t}link({text:t}){return""+t}image({text:t}){return""+t}br(){return""}},Fe=class ls{constructor(t){mt(this,"options");mt(this,"renderer");mt(this,"textRenderer");this.options=t||hr,this.options.renderer=this.options.renderer||new Ba,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new oo}static parse(t,r){return new ls(r).parse(t)}static parseInline(t,r){return new ls(r).parseInline(t)}parse(t,r=!0){var a,n;let i="";for(let o=0;o<t.length;o++){let s=t[o];if((n=(a=this.options.extensions)==null?void 0:a.renderers)!=null&&n[s.type]){let l=s,h=this.options.extensions.renderers[l.type].call({parser:this},l);if(h!==!1||!["space","hr","heading","code","table","blockquote","list","html","def","paragraph","text"].includes(l.type)){i+=h||"";continue}}let c=s;switch(c.type){case"space":{i+=this.renderer.space(c);continue}case"hr":{i+=this.renderer.hr(c);continue}case"heading":{i+=this.renderer.heading(c);continue}case"code":{i+=this.renderer.code(c);continue}case"table":{i+=this.renderer.table(c);continue}case"blockquote":{i+=this.renderer.blockquote(c);continue}case"list":{i+=this.renderer.list(c);continue}case"html":{i+=this.renderer.html(c);continue}case"def":{i+=this.renderer.def(c);continue}case"paragraph":{i+=this.renderer.paragraph(c);continue}case"text":{let l=c,h=this.renderer.text(l);for(;o+1<t.length&&t[o+1].type==="text";)l=t[++o],h+=`
`+this.renderer.text(l);r?i+=this.renderer.paragraph({type:"paragraph",raw:h,text:h,tokens:[{type:"text",raw:h,text:h,escaped:!0}]}):i+=h;continue}default:{let l='Token with "'+c.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return i}parseInline(t,r=this.renderer){var a,n;let i="";for(let o=0;o<t.length;o++){let s=t[o];if((n=(a=this.options.extensions)==null?void 0:a.renderers)!=null&&n[s.type]){let l=this.options.extensions.renderers[s.type].call({parser:this},s);if(l!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){i+=l||"";continue}}let c=s;switch(c.type){case"escape":{i+=r.text(c);break}case"html":{i+=r.html(c);break}case"link":{i+=r.link(c);break}case"image":{i+=r.image(c);break}case"strong":{i+=r.strong(c);break}case"em":{i+=r.em(c);break}case"codespan":{i+=r.codespan(c);break}case"br":{i+=r.br(c);break}case"del":{i+=r.del(c);break}case"text":{i+=r.text(c);break}default:{let l='Token with "'+c.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return i}},Fn,aa=(Fn=class{constructor(t){mt(this,"options");mt(this,"block");this.options=t||hr}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}provideLexer(){return this.block?Ee.lex:Ee.lexInline}provideParser(){return this.block?Fe.parse:Fe.parseInline}},mt(Fn,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),Fn),r2=class{constructor(...t){mt(this,"defaults",Js());mt(this,"options",this.setOptions);mt(this,"parse",this.parseMarkdown(!0));mt(this,"parseInline",this.parseMarkdown(!1));mt(this,"Parser",Fe);mt(this,"Renderer",Ba);mt(this,"TextRenderer",oo);mt(this,"Lexer",Ee);mt(this,"Tokenizer",_a);mt(this,"Hooks",aa);this.use(...t)}walkTokens(t,r){var a,n;let i=[];for(let o of t)switch(i=i.concat(r.call(this,o)),o.type){case"table":{let s=o;for(let c of s.header)i=i.concat(this.walkTokens(c.tokens,r));for(let c of s.rows)for(let l of c)i=i.concat(this.walkTokens(l.tokens,r));break}case"list":{let s=o;i=i.concat(this.walkTokens(s.items,r));break}default:{let s=o;(n=(a=this.defaults.extensions)==null?void 0:a.childTokens)!=null&&n[s.type]?this.defaults.extensions.childTokens[s.type].forEach(c=>{let l=s[c].flat(1/0);i=i.concat(this.walkTokens(l,r))}):s.tokens&&(i=i.concat(this.walkTokens(s.tokens,r)))}}return i}use(...t){let r=this.defaults.extensions||{renderers:{},childTokens:{}};return t.forEach(i=>{let a={...i};if(a.async=this.defaults.async||a.async||!1,i.extensions&&(i.extensions.forEach(n=>{if(!n.name)throw new Error("extension name required");if("renderer"in n){let o=r.renderers[n.name];o?r.renderers[n.name]=function(...s){let c=n.renderer.apply(this,s);return c===!1&&(c=o.apply(this,s)),c}:r.renderers[n.name]=n.renderer}if("tokenizer"in n){if(!n.level||n.level!=="block"&&n.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");let o=r[n.level];o?o.unshift(n.tokenizer):r[n.level]=[n.tokenizer],n.start&&(n.level==="block"?r.startBlock?r.startBlock.push(n.start):r.startBlock=[n.start]:n.level==="inline"&&(r.startInline?r.startInline.push(n.start):r.startInline=[n.start]))}"childTokens"in n&&n.childTokens&&(r.childTokens[n.name]=n.childTokens)}),a.extensions=r),i.renderer){let n=this.defaults.renderer||new Ba(this.defaults);for(let o in i.renderer){if(!(o in n))throw new Error(`renderer '${o}' does not exist`);if(["options","parser"].includes(o))continue;let s=o,c=i.renderer[s],l=n[s];n[s]=(...h)=>{let u=c.apply(n,h);return u===!1&&(u=l.apply(n,h)),u||""}}a.renderer=n}if(i.tokenizer){let n=this.defaults.tokenizer||new _a(this.defaults);for(let o in i.tokenizer){if(!(o in n))throw new Error(`tokenizer '${o}' does not exist`);if(["options","rules","lexer"].includes(o))continue;let s=o,c=i.tokenizer[s],l=n[s];n[s]=(...h)=>{let u=c.apply(n,h);return u===!1&&(u=l.apply(n,h)),u}}a.tokenizer=n}if(i.hooks){let n=this.defaults.hooks||new aa;for(let o in i.hooks){if(!(o in n))throw new Error(`hook '${o}' does not exist`);if(["options","block"].includes(o))continue;let s=o,c=i.hooks[s],l=n[s];aa.passThroughHooks.has(o)?n[s]=h=>{if(this.defaults.async)return Promise.resolve(c.call(n,h)).then(p=>l.call(n,p));let u=c.call(n,h);return l.call(n,u)}:n[s]=(...h)=>{let u=c.apply(n,h);return u===!1&&(u=l.apply(n,h)),u}}a.hooks=n}if(i.walkTokens){let n=this.defaults.walkTokens,o=i.walkTokens;a.walkTokens=function(s){let c=[];return c.push(o.call(this,s)),n&&(c=c.concat(n.call(this,s))),c}}this.defaults={...this.defaults,...a}}),this}setOptions(t){return this.defaults={...this.defaults,...t},this}lexer(t,r){return Ee.lex(t,r??this.defaults)}parser(t,r){return Fe.parse(t,r??this.defaults)}parseMarkdown(t){return(r,i)=>{let a={...i},n={...this.defaults,...a},o=this.onError(!!n.silent,!!n.async);if(this.defaults.async===!0&&a.async===!1)return o(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof r>"u"||r===null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof r!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(r)+", string expected"));n.hooks&&(n.hooks.options=n,n.hooks.block=t);let s=n.hooks?n.hooks.provideLexer():t?Ee.lex:Ee.lexInline,c=n.hooks?n.hooks.provideParser():t?Fe.parse:Fe.parseInline;if(n.async)return Promise.resolve(n.hooks?n.hooks.preprocess(r):r).then(l=>s(l,n)).then(l=>n.hooks?n.hooks.processAllTokens(l):l).then(l=>n.walkTokens?Promise.all(this.walkTokens(l,n.walkTokens)).then(()=>l):l).then(l=>c(l,n)).then(l=>n.hooks?n.hooks.postprocess(l):l).catch(o);try{n.hooks&&(r=n.hooks.preprocess(r));let l=s(r,n);n.hooks&&(l=n.hooks.processAllTokens(l)),n.walkTokens&&this.walkTokens(l,n.walkTokens);let h=c(l,n);return n.hooks&&(h=n.hooks.postprocess(h)),h}catch(l){return o(l)}}}onError(t,r){return i=>{if(i.message+=`
Please report this to https://github.com/markedjs/marked.`,t){let a="<p>An error occurred:</p><pre>"+me(i.message+"",!0)+"</pre>";return r?Promise.resolve(a):a}if(r)return Promise.reject(i);throw i}}},ar=new r2;function dt(e,t){return ar.parse(e,t)}dt.options=dt.setOptions=function(e){return ar.setOptions(e),dt.defaults=ar.defaults,Uu(dt.defaults),dt};dt.getDefaults=Js;dt.defaults=hr;dt.use=function(...e){return ar.use(...e),dt.defaults=ar.defaults,Uu(dt.defaults),dt};dt.walkTokens=function(e,t){return ar.walkTokens(e,t)};dt.parseInline=ar.parseInline;dt.Parser=Fe;dt.parser=Fe.parse;dt.Renderer=Ba;dt.TextRenderer=oo;dt.Lexer=Ee;dt.lexer=Ee.lex;dt.Tokenizer=_a;dt.Hooks=aa;dt.parse=dt;dt.options;dt.setOptions;dt.use;dt.walkTokens;dt.parseInline;Fe.parse;Ee.lex;function id(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var i=Array.from(typeof e=="string"?[e]:e);i[i.length-1]=i[i.length-1].replace(/\r?\n([\t ]*)$/,"");var a=i.reduce(function(s,c){var l=c.match(/\n([\t ]+|(?!\s).)/g);return l?s.concat(l.map(function(h){var u,p;return(p=(u=h.match(/[\t ]/g))===null||u===void 0?void 0:u.length)!==null&&p!==void 0?p:0})):s},[]);if(a.length){var n=new RegExp(`
[	 ]{`+Math.min.apply(Math,a)+"}","g");i=i.map(function(s){return s.replace(n,`
`)})}i[0]=i[0].replace(/^\r?\n/,"");var o=i[0];return t.forEach(function(s,c){var l=o.match(/(?:^|\n)( *)$/),h=l?l[1]:"",u=s;typeof s=="string"&&s.includes(`
`)&&(u=String(s).split(`
`).map(function(p,d){return d===0?p:""+h+p}).join(`
`)),o+=u+i[c+1]}),o}var i2={body:'<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/><text transform="translate(21.16 64.67)" style="fill: #fff; font-family: ArialMT, Arial; font-size: 67.75px;"><tspan x="0" y="0">?</tspan></text></g>',height:80,width:80},cs=new Map,ad=new Map,a2=f(e=>{for(const t of e){if(!t.name)throw new Error('Invalid icon loader. Must have a "name" property with non-empty string value.');if($.debug("Registering icon pack:",t.name),"loader"in t)ad.set(t.name,t.loader);else if("icons"in t)cs.set(t.name,t.icons);else throw $.error("Invalid icon loader:",t),new Error('Invalid icon loader. Must have either "icons" or "loader" property.')}},"registerIconPacks"),nd=f(async(e,t)=>{const r=nC(e,!0,t!==void 0);if(!r)throw new Error(`Invalid icon name: ${e}`);const i=r.prefix||t;if(!i)throw new Error(`Icon name must contain a prefix: ${e}`);let a=cs.get(i);if(!a){const o=ad.get(i);if(!o)throw new Error(`Icon set not found: ${r.prefix}`);try{a={...await o(),prefix:i},cs.set(i,a)}catch(s){throw $.error(s),new Error(`Failed to load icon set: ${r.prefix}`)}}const n=lC(a,r.name);if(!n)throw new Error(`Icon not found: ${e}`);return n},"getRegisteredIconData"),n2=f(async e=>{try{return await nd(e),!0}catch{return!1}},"isIconAvailable"),Oi=f(async(e,t,r)=>{let i;try{i=await nd(e,t==null?void 0:t.fallbackPrefix)}catch(o){$.error(o),i=i2}const a=gC(i,t),n=CC(bC(a.body),{...a.attributes,...r});return ne(n,Nt())},"getIconSVG");function sd(e,{markdownAutoWrap:t}){const i=e.replace(/<br\/>/g,`
`).replace(/\n{2,}/g,`
`),a=id(i);return t===!1?a.replace(/ /g,"&nbsp;"):a}f(sd,"preprocessMarkdown");function od(e,t={}){const r=sd(e,t),i=dt.lexer(r),a=[[]];let n=0;function o(s,c="normal"){s.type==="text"?s.text.split(`
`).forEach((h,u)=>{u!==0&&(n++,a.push([])),h.split(" ").forEach(p=>{p=p.replace(/&#39;/g,"'"),p&&a[n].push({content:p,type:c})})}):s.type==="strong"||s.type==="em"?s.tokens.forEach(l=>{o(l,s.type)}):s.type==="html"&&a[n].push({content:s.text,type:"normal"})}return f(o,"processNode"),i.forEach(s=>{var c;s.type==="paragraph"?(c=s.tokens)==null||c.forEach(l=>{o(l)}):s.type==="html"?a[n].push({content:s.text,type:"normal"}):a[n].push({content:s.raw,type:"normal"})}),a}f(od,"markdownToLines");function ld(e,{markdownAutoWrap:t}={}){const r=dt.lexer(e);function i(a){var n,o,s;return a.type==="text"?t===!1?a.text.replace(/\n */g,"<br/>").replace(/ /g,"&nbsp;"):a.text.replace(/\n */g,"<br/>"):a.type==="strong"?`<strong>${(n=a.tokens)==null?void 0:n.map(i).join("")}</strong>`:a.type==="em"?`<em>${(o=a.tokens)==null?void 0:o.map(i).join("")}</em>`:a.type==="paragraph"?`<p>${(s=a.tokens)==null?void 0:s.map(i).join("")}</p>`:a.type==="space"?"":a.type==="html"?`${a.text}`:a.type==="escape"?a.text:($.warn(`Unsupported markdown: ${a.type}`),a.raw)}return f(i,"output"),r.map(i).join("")}f(ld,"markdownToHTML");function cd(e){return Intl.Segmenter?[...new Intl.Segmenter().segment(e)].map(t=>t.segment):[...e]}f(cd,"splitTextToChars");function hd(e,t){const r=cd(t.content);return lo(e,[],r,t.type)}f(hd,"splitWordToFitWidth");function lo(e,t,r,i){if(r.length===0)return[{content:t.join(""),type:i},{content:"",type:i}];const[a,...n]=r,o=[...t,a];return e([{content:o.join(""),type:i}])?lo(e,o,n,i):(t.length===0&&a&&(t.push(a),r.shift()),[{content:t.join(""),type:i},{content:r.join(""),type:i}])}f(lo,"splitWordToFitWidthRecursion");function ud(e,t){if(e.some(({content:r})=>r.includes(`
`)))throw new Error("splitLineToFitWidth does not support newlines in the line");return La(e,t)}f(ud,"splitLineToFitWidth");function La(e,t,r=[],i=[]){if(e.length===0)return i.length>0&&r.push(i),r.length>0?r:[];let a="";e[0].content===" "&&(a=" ",e.shift());const n=e.shift()??{content:" ",type:"normal"},o=[...i];if(a!==""&&o.push({content:a,type:"normal"}),o.push(n),t(o))return La(e,t,r,o);if(i.length>0)r.push(i),e.unshift(n);else if(n.content){const[s,c]=hd(t,n);r.push([s]),c.content&&e.unshift(c)}return La(e,t,r)}f(La,"splitLineToFitWidthRecursion");function hs(e,t){t&&e.attr("style",t)}f(hs,"applyStyle");async function dd(e,t,r,i,a=!1,n=Nt()){const o=e.append("foreignObject");o.attr("width",`${10*r}px`),o.attr("height",`${10*r}px`);const s=o.append("xhtml:div"),c=_r(t.label)?await _s(t.label.replace(Fr.lineBreakRegex,`
`),n):ne(t.label,n),l=t.isNode?"nodeLabel":"edgeLabel",h=s.append("span");h.html(c),hs(h,t.labelStyle),h.attr("class",`${l} ${i}`),hs(s,t.labelStyle),s.style("display","table-cell"),s.style("white-space","nowrap"),s.style("line-height","1.5"),s.style("max-width",r+"px"),s.style("text-align","center"),s.attr("xmlns","http://www.w3.org/1999/xhtml"),a&&s.attr("class","labelBkg");let u=s.node().getBoundingClientRect();return u.width===r&&(s.style("display","table"),s.style("white-space","break-spaces"),s.style("width",r+"px"),u=s.node().getBoundingClientRect()),o.node()}f(dd,"addHtmlSpan");function Ka(e,t,r){return e.append("tspan").attr("class","text-outer-tspan").attr("x",0).attr("y",t*r-.1+"em").attr("dy",r+"em")}f(Ka,"createTspan");function pd(e,t,r){const i=e.append("text"),a=Ka(i,1,t);Qa(a,r);const n=a.node().getComputedTextLength();return i.remove(),n}f(pd,"computeWidthOfText");function s2(e,t,r){var o;const i=e.append("text"),a=Ka(i,1,t);Qa(a,[{content:r,type:"normal"}]);const n=(o=a.node())==null?void 0:o.getBoundingClientRect();return n&&i.remove(),n}f(s2,"computeDimensionOfText");function fd(e,t,r,i=!1){const n=t.append("g"),o=n.insert("rect").attr("class","background").attr("style","stroke: none"),s=n.append("text").attr("y","-10.1");let c=0;for(const l of r){const h=f(p=>pd(n,1.1,p)<=e,"checkWidth"),u=h(l)?[l]:ud(l,h);for(const p of u){const d=Ka(s,c,1.1);Qa(d,p),c++}}if(i){const l=s.node().getBBox(),h=2;return o.attr("x",l.x-h).attr("y",l.y-h).attr("width",l.width+2*h).attr("height",l.height+2*h),n.node()}else return s.node()}f(fd,"createFormattedText");function Qa(e,t){e.text(""),t.forEach((r,i)=>{const a=e.append("tspan").attr("font-style",r.type==="em"?"italic":"normal").attr("class","text-inner-tspan").attr("font-weight",r.type==="strong"?"bold":"normal");i===0?a.text(r.content):a.text(" "+r.content)})}f(Qa,"updateTextContentAndStyles");async function gd(e,t={}){const r=[];e.replace(/(fa[bklrs]?):fa-([\w-]+)/g,(a,n,o)=>(r.push((async()=>{const s=`${n}:${o}`;return await n2(s)?await Oi(s,void 0,{class:"label-icon"}):`<i class='${ne(a,t).replace(":"," ")}'></i>`})()),a));const i=await Promise.all(r);return e.replace(/(fa[bklrs]?):fa-([\w-]+)/g,()=>i.shift()??"")}f(gd,"replaceIconSubstring");var Ye=f(async(e,t="",{style:r="",isTitle:i=!1,classes:a="",useHtmlLabels:n=!0,isNode:o=!0,width:s=200,addSvgBackground:c=!1}={},l)=>{if($.debug("XYZ createText",t,r,i,a,n,o,"addSvgBackground: ",c),n){const h=ld(t,l),u=await gd(cr(h),l),p=t.replace(/\\\\/g,"\\"),d={isNode:o,label:_r(t)?p:u,labelStyle:r.replace("fill:","color:")};return await dd(e,d,s,a,c,l)}else{const h=t.replace(/<br\s*\/?>/g,"<br/>"),u=od(h.replace("<br>","<br/>"),l),p=fd(s,e,u,t?c:!1);if(o){/stroke:/.exec(r)&&(r=r.replace("stroke:","lineColor:"));const d=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");ht(p).attr("style",d)}else{const d=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/background:/g,"fill:");ht(p).select("rect").attr("style",d.replace(/background:/g,"fill:"));const g=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");ht(p).select("text").attr("style",g)}return p}},"createText");function Sn(e,t,r){if(e&&e.length){const[i,a]=t,n=Math.PI/180*r,o=Math.cos(n),s=Math.sin(n);for(const c of e){const[l,h]=c;c[0]=(l-i)*o-(h-a)*s+i,c[1]=(l-i)*s+(h-a)*o+a}}}function o2(e,t){return e[0]===t[0]&&e[1]===t[1]}function l2(e,t,r,i=1){const a=r,n=Math.max(t,.1),o=e[0]&&e[0][0]&&typeof e[0][0]=="number"?[e]:e,s=[0,0];if(a)for(const l of o)Sn(l,s,a);const c=function(l,h,u){const p=[];for(const b of l){const k=[...b];o2(k[0],k[k.length-1])||k.push([k[0][0],k[0][1]]),k.length>2&&p.push(k)}const d=[];h=Math.max(h,.1);const g=[];for(const b of p)for(let k=0;k<b.length-1;k++){const T=b[k],v=b[k+1];if(T[1]!==v[1]){const C=Math.min(T[1],v[1]);g.push({ymin:C,ymax:Math.max(T[1],v[1]),x:C===T[1]?T[0]:v[0],islope:(v[0]-T[0])/(v[1]-T[1])})}}if(g.sort((b,k)=>b.ymin<k.ymin?-1:b.ymin>k.ymin?1:b.x<k.x?-1:b.x>k.x?1:b.ymax===k.ymax?0:(b.ymax-k.ymax)/Math.abs(b.ymax-k.ymax)),!g.length)return d;let m=[],y=g[0].ymin,x=0;for(;m.length||g.length;){if(g.length){let b=-1;for(let k=0;k<g.length&&!(g[k].ymin>y);k++)b=k;g.splice(0,b+1).forEach(k=>{m.push({s:y,edge:k})})}if(m=m.filter(b=>!(b.edge.ymax<=y)),m.sort((b,k)=>b.edge.x===k.edge.x?0:(b.edge.x-k.edge.x)/Math.abs(b.edge.x-k.edge.x)),(u!==1||x%h==0)&&m.length>1)for(let b=0;b<m.length;b+=2){const k=b+1;if(k>=m.length)break;const T=m[b].edge,v=m[k].edge;d.push([[Math.round(T.x),y],[Math.round(v.x),y]])}y+=u,m.forEach(b=>{b.edge.x=b.edge.x+u*b.edge.islope}),x++}return d}(o,n,i);if(a){for(const l of o)Sn(l,s,-a);(function(l,h,u){const p=[];l.forEach(d=>p.push(...d)),Sn(p,h,u)})(c,s,-a)}return c}function Di(e,t){var r;const i=t.hachureAngle+90;let a=t.hachureGap;a<0&&(a=4*t.strokeWidth),a=Math.round(Math.max(a,.1));let n=1;return t.roughness>=1&&(((r=t.randomizer)===null||r===void 0?void 0:r.next())||Math.random())>.7&&(n=a),l2(e,a,i,n||1)}class co{constructor(t){this.helper=t}fillPolygons(t,r){return this._fillPolygons(t,r)}_fillPolygons(t,r){const i=Di(t,r);return{type:"fillSketch",ops:this.renderLines(i,r)}}renderLines(t,r){const i=[];for(const a of t)i.push(...this.helper.doubleLineOps(a[0][0],a[0][1],a[1][0],a[1][1],r));return i}}function Ja(e){const t=e[0],r=e[1];return Math.sqrt(Math.pow(t[0]-r[0],2)+Math.pow(t[1]-r[1],2))}class c2 extends co{fillPolygons(t,r){let i=r.hachureGap;i<0&&(i=4*r.strokeWidth),i=Math.max(i,.1);const a=Di(t,Object.assign({},r,{hachureGap:i})),n=Math.PI/180*r.hachureAngle,o=[],s=.5*i*Math.cos(n),c=.5*i*Math.sin(n);for(const[l,h]of a)Ja([l,h])&&o.push([[l[0]-s,l[1]+c],[...h]],[[l[0]+s,l[1]-c],[...h]]);return{type:"fillSketch",ops:this.renderLines(o,r)}}}class h2 extends co{fillPolygons(t,r){const i=this._fillPolygons(t,r),a=Object.assign({},r,{hachureAngle:r.hachureAngle+90}),n=this._fillPolygons(t,a);return i.ops=i.ops.concat(n.ops),i}}class u2{constructor(t){this.helper=t}fillPolygons(t,r){const i=Di(t,r=Object.assign({},r,{hachureAngle:0}));return this.dotsOnLines(i,r)}dotsOnLines(t,r){const i=[];let a=r.hachureGap;a<0&&(a=4*r.strokeWidth),a=Math.max(a,.1);let n=r.fillWeight;n<0&&(n=r.strokeWidth/2);const o=a/4;for(const s of t){const c=Ja(s),l=c/a,h=Math.ceil(l)-1,u=c-h*a,p=(s[0][0]+s[1][0])/2-a/4,d=Math.min(s[0][1],s[1][1]);for(let g=0;g<h;g++){const m=d+u+g*a,y=p-o+2*Math.random()*o,x=m-o+2*Math.random()*o,b=this.helper.ellipse(y,x,n,n,r);i.push(...b.ops)}}return{type:"fillSketch",ops:i}}}class d2{constructor(t){this.helper=t}fillPolygons(t,r){const i=Di(t,r);return{type:"fillSketch",ops:this.dashedLine(i,r)}}dashedLine(t,r){const i=r.dashOffset<0?r.hachureGap<0?4*r.strokeWidth:r.hachureGap:r.dashOffset,a=r.dashGap<0?r.hachureGap<0?4*r.strokeWidth:r.hachureGap:r.dashGap,n=[];return t.forEach(o=>{const s=Ja(o),c=Math.floor(s/(i+a)),l=(s+a-c*(i+a))/2;let h=o[0],u=o[1];h[0]>u[0]&&(h=o[1],u=o[0]);const p=Math.atan((u[1]-h[1])/(u[0]-h[0]));for(let d=0;d<c;d++){const g=d*(i+a),m=g+i,y=[h[0]+g*Math.cos(p)+l*Math.cos(p),h[1]+g*Math.sin(p)+l*Math.sin(p)],x=[h[0]+m*Math.cos(p)+l*Math.cos(p),h[1]+m*Math.sin(p)+l*Math.sin(p)];n.push(...this.helper.doubleLineOps(y[0],y[1],x[0],x[1],r))}}),n}}class p2{constructor(t){this.helper=t}fillPolygons(t,r){const i=r.hachureGap<0?4*r.strokeWidth:r.hachureGap,a=r.zigzagOffset<0?i:r.zigzagOffset,n=Di(t,r=Object.assign({},r,{hachureGap:i+a}));return{type:"fillSketch",ops:this.zigzagLines(n,a,r)}}zigzagLines(t,r,i){const a=[];return t.forEach(n=>{const o=Ja(n),s=Math.round(o/(2*r));let c=n[0],l=n[1];c[0]>l[0]&&(c=n[1],l=n[0]);const h=Math.atan((l[1]-c[1])/(l[0]-c[0]));for(let u=0;u<s;u++){const p=2*u*r,d=2*(u+1)*r,g=Math.sqrt(2*Math.pow(r,2)),m=[c[0]+p*Math.cos(h),c[1]+p*Math.sin(h)],y=[c[0]+d*Math.cos(h),c[1]+d*Math.sin(h)],x=[m[0]+g*Math.cos(h+Math.PI/4),m[1]+g*Math.sin(h+Math.PI/4)];a.push(...this.helper.doubleLineOps(m[0],m[1],x[0],x[1],i),...this.helper.doubleLineOps(x[0],x[1],y[0],y[1],i))}}),a}}const Zt={};class f2{constructor(t){this.seed=t}next(){return this.seed?(2**31-1&(this.seed=Math.imul(48271,this.seed)))/2**31:Math.random()}}const g2=0,Tn=1,_l=2,Ui={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0};function _n(e,t){return e.type===t}function ho(e){const t=[],r=function(o){const s=new Array;for(;o!=="";)if(o.match(/^([ \t\r\n,]+)/))o=o.substr(RegExp.$1.length);else if(o.match(/^([aAcChHlLmMqQsStTvVzZ])/))s[s.length]={type:g2,text:RegExp.$1},o=o.substr(RegExp.$1.length);else{if(!o.match(/^(([-+]?[0-9]+(\.[0-9]*)?|[-+]?\.[0-9]+)([eE][-+]?[0-9]+)?)/))return[];s[s.length]={type:Tn,text:`${parseFloat(RegExp.$1)}`},o=o.substr(RegExp.$1.length)}return s[s.length]={type:_l,text:""},s}(e);let i="BOD",a=0,n=r[a];for(;!_n(n,_l);){let o=0;const s=[];if(i==="BOD"){if(n.text!=="M"&&n.text!=="m")return ho("M0,0"+e);a++,o=Ui[n.text],i=n.text}else _n(n,Tn)?o=Ui[i]:(a++,o=Ui[n.text],i=n.text);if(!(a+o<r.length))throw new Error("Path data ended short");for(let c=a;c<a+o;c++){const l=r[c];if(!_n(l,Tn))throw new Error("Param not a number: "+i+","+l.text);s[s.length]=+l.text}if(typeof Ui[i]!="number")throw new Error("Bad segment: "+i);{const c={key:i,data:s};t.push(c),a+=o,n=r[a],i==="M"&&(i="L"),i==="m"&&(i="l")}}return t}function md(e){let t=0,r=0,i=0,a=0;const n=[];for(const{key:o,data:s}of e)switch(o){case"M":n.push({key:"M",data:[...s]}),[t,r]=s,[i,a]=s;break;case"m":t+=s[0],r+=s[1],n.push({key:"M",data:[t,r]}),i=t,a=r;break;case"L":n.push({key:"L",data:[...s]}),[t,r]=s;break;case"l":t+=s[0],r+=s[1],n.push({key:"L",data:[t,r]});break;case"C":n.push({key:"C",data:[...s]}),t=s[4],r=s[5];break;case"c":{const c=s.map((l,h)=>h%2?l+r:l+t);n.push({key:"C",data:c}),t=c[4],r=c[5];break}case"Q":n.push({key:"Q",data:[...s]}),t=s[2],r=s[3];break;case"q":{const c=s.map((l,h)=>h%2?l+r:l+t);n.push({key:"Q",data:c}),t=c[2],r=c[3];break}case"A":n.push({key:"A",data:[...s]}),t=s[5],r=s[6];break;case"a":t+=s[5],r+=s[6],n.push({key:"A",data:[s[0],s[1],s[2],s[3],s[4],t,r]});break;case"H":n.push({key:"H",data:[...s]}),t=s[0];break;case"h":t+=s[0],n.push({key:"H",data:[t]});break;case"V":n.push({key:"V",data:[...s]}),r=s[0];break;case"v":r+=s[0],n.push({key:"V",data:[r]});break;case"S":n.push({key:"S",data:[...s]}),t=s[2],r=s[3];break;case"s":{const c=s.map((l,h)=>h%2?l+r:l+t);n.push({key:"S",data:c}),t=c[2],r=c[3];break}case"T":n.push({key:"T",data:[...s]}),t=s[0],r=s[1];break;case"t":t+=s[0],r+=s[1],n.push({key:"T",data:[t,r]});break;case"Z":case"z":n.push({key:"Z",data:[]}),t=i,r=a}return n}function yd(e){const t=[];let r="",i=0,a=0,n=0,o=0,s=0,c=0;for(const{key:l,data:h}of e){switch(l){case"M":t.push({key:"M",data:[...h]}),[i,a]=h,[n,o]=h;break;case"C":t.push({key:"C",data:[...h]}),i=h[4],a=h[5],s=h[2],c=h[3];break;case"L":t.push({key:"L",data:[...h]}),[i,a]=h;break;case"H":i=h[0],t.push({key:"L",data:[i,a]});break;case"V":a=h[0],t.push({key:"L",data:[i,a]});break;case"S":{let u=0,p=0;r==="C"||r==="S"?(u=i+(i-s),p=a+(a-c)):(u=i,p=a),t.push({key:"C",data:[u,p,...h]}),s=h[0],c=h[1],i=h[2],a=h[3];break}case"T":{const[u,p]=h;let d=0,g=0;r==="Q"||r==="T"?(d=i+(i-s),g=a+(a-c)):(d=i,g=a);const m=i+2*(d-i)/3,y=a+2*(g-a)/3,x=u+2*(d-u)/3,b=p+2*(g-p)/3;t.push({key:"C",data:[m,y,x,b,u,p]}),s=d,c=g,i=u,a=p;break}case"Q":{const[u,p,d,g]=h,m=i+2*(u-i)/3,y=a+2*(p-a)/3,x=d+2*(u-d)/3,b=g+2*(p-g)/3;t.push({key:"C",data:[m,y,x,b,d,g]}),s=u,c=p,i=d,a=g;break}case"A":{const u=Math.abs(h[0]),p=Math.abs(h[1]),d=h[2],g=h[3],m=h[4],y=h[5],x=h[6];u===0||p===0?(t.push({key:"C",data:[i,a,y,x,y,x]}),i=y,a=x):(i!==y||a!==x)&&(xd(i,a,y,x,u,p,d,g,m).forEach(function(b){t.push({key:"C",data:b})}),i=y,a=x);break}case"Z":t.push({key:"Z",data:[]}),i=n,a=o}r=l}return t}function Xr(e,t,r){return[e*Math.cos(r)-t*Math.sin(r),e*Math.sin(r)+t*Math.cos(r)]}function xd(e,t,r,i,a,n,o,s,c,l){const h=(u=o,Math.PI*u/180);var u;let p=[],d=0,g=0,m=0,y=0;if(l)[d,g,m,y]=l;else{[e,t]=Xr(e,t,-h),[r,i]=Xr(r,i,-h);const D=(e-r)/2,L=(t-i)/2;let A=D*D/(a*a)+L*L/(n*n);A>1&&(A=Math.sqrt(A),a*=A,n*=A);const B=a*a,F=n*n,M=B*F-B*L*L-F*D*D,W=B*L*L+F*D*D,V=(s===c?-1:1)*Math.sqrt(Math.abs(M/W));m=V*a*L/n+(e+r)/2,y=V*-n*D/a+(t+i)/2,d=Math.asin(parseFloat(((t-y)/n).toFixed(9))),g=Math.asin(parseFloat(((i-y)/n).toFixed(9))),e<m&&(d=Math.PI-d),r<m&&(g=Math.PI-g),d<0&&(d=2*Math.PI+d),g<0&&(g=2*Math.PI+g),c&&d>g&&(d-=2*Math.PI),!c&&g>d&&(g-=2*Math.PI)}let x=g-d;if(Math.abs(x)>120*Math.PI/180){const D=g,L=r,A=i;g=c&&g>d?d+120*Math.PI/180*1:d+120*Math.PI/180*-1,p=xd(r=m+a*Math.cos(g),i=y+n*Math.sin(g),L,A,a,n,o,0,c,[g,D,m,y])}x=g-d;const b=Math.cos(d),k=Math.sin(d),T=Math.cos(g),v=Math.sin(g),C=Math.tan(x/4),S=4/3*a*C,O=4/3*n*C,P=[e,t],R=[e+S*k,t-O*b],E=[r+S*v,i-O*T],N=[r,i];if(R[0]=2*P[0]-R[0],R[1]=2*P[1]-R[1],l)return[R,E,N].concat(p);{p=[R,E,N].concat(p);const D=[];for(let L=0;L<p.length;L+=3){const A=Xr(p[L][0],p[L][1],h),B=Xr(p[L+1][0],p[L+1][1],h),F=Xr(p[L+2][0],p[L+2][1],h);D.push([A[0],A[1],B[0],B[1],F[0],F[1]])}return D}}const m2={randOffset:function(e,t){return et(e,t)},randOffsetWithRange:function(e,t,r){return Aa(e,t,r)},ellipse:function(e,t,r,i,a){const n=Cd(r,i,a);return us(e,t,a,n).opset},doubleLineOps:function(e,t,r,i,a){return qe(e,t,r,i,a,!0)}};function bd(e,t,r,i,a){return{type:"path",ops:qe(e,t,r,i,a)}}function na(e,t,r){const i=(e||[]).length;if(i>2){const a=[];for(let n=0;n<i-1;n++)a.push(...qe(e[n][0],e[n][1],e[n+1][0],e[n+1][1],r));return t&&a.push(...qe(e[i-1][0],e[i-1][1],e[0][0],e[0][1],r)),{type:"path",ops:a}}return i===2?bd(e[0][0],e[0][1],e[1][0],e[1][1],r):{type:"path",ops:[]}}function y2(e,t,r,i,a){return function(n,o){return na(n,!0,o)}([[e,t],[e+r,t],[e+r,t+i],[e,t+i]],a)}function Bl(e,t){if(e.length){const r=typeof e[0][0]=="number"?[e]:e,i=Gi(r[0],1*(1+.2*t.roughness),t),a=t.disableMultiStroke?[]:Gi(r[0],1.5*(1+.22*t.roughness),Ml(t));for(let n=1;n<r.length;n++){const o=r[n];if(o.length){const s=Gi(o,1*(1+.2*t.roughness),t),c=t.disableMultiStroke?[]:Gi(o,1.5*(1+.22*t.roughness),Ml(t));for(const l of s)l.op!=="move"&&i.push(l);for(const l of c)l.op!=="move"&&a.push(l)}}return{type:"path",ops:i.concat(a)}}return{type:"path",ops:[]}}function Cd(e,t,r){const i=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(e/2,2)+Math.pow(t/2,2))/2)),a=Math.ceil(Math.max(r.curveStepCount,r.curveStepCount/Math.sqrt(200)*i)),n=2*Math.PI/a;let o=Math.abs(e/2),s=Math.abs(t/2);const c=1-r.curveFitting;return o+=et(o*c,r),s+=et(s*c,r),{increment:n,rx:o,ry:s}}function us(e,t,r,i){const[a,n]=El(i.increment,e,t,i.rx,i.ry,1,i.increment*Aa(.1,Aa(.4,1,r),r),r);let o=Ma(a,null,r);if(!r.disableMultiStroke&&r.roughness!==0){const[s]=El(i.increment,e,t,i.rx,i.ry,1.5,0,r),c=Ma(s,null,r);o=o.concat(c)}return{estimatedPoints:n,opset:{type:"path",ops:o}}}function Ll(e,t,r,i,a,n,o,s,c){const l=e,h=t;let u=Math.abs(r/2),p=Math.abs(i/2);u+=et(.01*u,c),p+=et(.01*p,c);let d=a,g=n;for(;d<0;)d+=2*Math.PI,g+=2*Math.PI;g-d>2*Math.PI&&(d=0,g=2*Math.PI);const m=2*Math.PI/c.curveStepCount,y=Math.min(m/2,(g-d)/2),x=Fl(y,l,h,u,p,d,g,1,c);if(!c.disableMultiStroke){const b=Fl(y,l,h,u,p,d,g,1.5,c);x.push(...b)}return o&&(s?x.push(...qe(l,h,l+u*Math.cos(d),h+p*Math.sin(d),c),...qe(l,h,l+u*Math.cos(g),h+p*Math.sin(g),c)):x.push({op:"lineTo",data:[l,h]},{op:"lineTo",data:[l+u*Math.cos(d),h+p*Math.sin(d)]})),{type:"path",ops:x}}function Al(e,t){const r=yd(md(ho(e))),i=[];let a=[0,0],n=[0,0];for(const{key:o,data:s}of r)switch(o){case"M":n=[s[0],s[1]],a=[s[0],s[1]];break;case"L":i.push(...qe(n[0],n[1],s[0],s[1],t)),n=[s[0],s[1]];break;case"C":{const[c,l,h,u,p,d]=s;i.push(...x2(c,l,h,u,p,d,n,t)),n=[p,d];break}case"Z":i.push(...qe(n[0],n[1],a[0],a[1],t)),n=[a[0],a[1]]}return{type:"path",ops:i}}function Bn(e,t){const r=[];for(const i of e)if(i.length){const a=t.maxRandomnessOffset||0,n=i.length;if(n>2){r.push({op:"move",data:[i[0][0]+et(a,t),i[0][1]+et(a,t)]});for(let o=1;o<n;o++)r.push({op:"lineTo",data:[i[o][0]+et(a,t),i[o][1]+et(a,t)]})}}return{type:"fillPath",ops:r}}function yr(e,t){return function(r,i){let a=r.fillStyle||"hachure";if(!Zt[a])switch(a){case"zigzag":Zt[a]||(Zt[a]=new c2(i));break;case"cross-hatch":Zt[a]||(Zt[a]=new h2(i));break;case"dots":Zt[a]||(Zt[a]=new u2(i));break;case"dashed":Zt[a]||(Zt[a]=new d2(i));break;case"zigzag-line":Zt[a]||(Zt[a]=new p2(i));break;default:a="hachure",Zt[a]||(Zt[a]=new co(i))}return Zt[a]}(t,m2).fillPolygons(e,t)}function Ml(e){const t=Object.assign({},e);return t.randomizer=void 0,e.seed&&(t.seed=e.seed+1),t}function kd(e){return e.randomizer||(e.randomizer=new f2(e.seed||0)),e.randomizer.next()}function Aa(e,t,r,i=1){return r.roughness*i*(kd(r)*(t-e)+e)}function et(e,t,r=1){return Aa(-e,e,t,r)}function qe(e,t,r,i,a,n=!1){const o=n?a.disableMultiStrokeFill:a.disableMultiStroke,s=ds(e,t,r,i,a,!0,!1);if(o)return s;const c=ds(e,t,r,i,a,!0,!0);return s.concat(c)}function ds(e,t,r,i,a,n,o){const s=Math.pow(e-r,2)+Math.pow(t-i,2),c=Math.sqrt(s);let l=1;l=c<200?1:c>500?.4:-.0016668*c+1.233334;let h=a.maxRandomnessOffset||0;h*h*100>s&&(h=c/10);const u=h/2,p=.2+.2*kd(a);let d=a.bowing*a.maxRandomnessOffset*(i-t)/200,g=a.bowing*a.maxRandomnessOffset*(e-r)/200;d=et(d,a,l),g=et(g,a,l);const m=[],y=()=>et(u,a,l),x=()=>et(h,a,l),b=a.preserveVertices;return o?m.push({op:"move",data:[e+(b?0:y()),t+(b?0:y())]}):m.push({op:"move",data:[e+(b?0:et(h,a,l)),t+(b?0:et(h,a,l))]}),o?m.push({op:"bcurveTo",data:[d+e+(r-e)*p+y(),g+t+(i-t)*p+y(),d+e+2*(r-e)*p+y(),g+t+2*(i-t)*p+y(),r+(b?0:y()),i+(b?0:y())]}):m.push({op:"bcurveTo",data:[d+e+(r-e)*p+x(),g+t+(i-t)*p+x(),d+e+2*(r-e)*p+x(),g+t+2*(i-t)*p+x(),r+(b?0:x()),i+(b?0:x())]}),m}function Gi(e,t,r){if(!e.length)return[];const i=[];i.push([e[0][0]+et(t,r),e[0][1]+et(t,r)]),i.push([e[0][0]+et(t,r),e[0][1]+et(t,r)]);for(let a=1;a<e.length;a++)i.push([e[a][0]+et(t,r),e[a][1]+et(t,r)]),a===e.length-1&&i.push([e[a][0]+et(t,r),e[a][1]+et(t,r)]);return Ma(i,null,r)}function Ma(e,t,r){const i=e.length,a=[];if(i>3){const n=[],o=1-r.curveTightness;a.push({op:"move",data:[e[1][0],e[1][1]]});for(let s=1;s+2<i;s++){const c=e[s];n[0]=[c[0],c[1]],n[1]=[c[0]+(o*e[s+1][0]-o*e[s-1][0])/6,c[1]+(o*e[s+1][1]-o*e[s-1][1])/6],n[2]=[e[s+1][0]+(o*e[s][0]-o*e[s+2][0])/6,e[s+1][1]+(o*e[s][1]-o*e[s+2][1])/6],n[3]=[e[s+1][0],e[s+1][1]],a.push({op:"bcurveTo",data:[n[1][0],n[1][1],n[2][0],n[2][1],n[3][0],n[3][1]]})}}else i===3?(a.push({op:"move",data:[e[1][0],e[1][1]]}),a.push({op:"bcurveTo",data:[e[1][0],e[1][1],e[2][0],e[2][1],e[2][0],e[2][1]]})):i===2&&a.push(...ds(e[0][0],e[0][1],e[1][0],e[1][1],r,!0,!0));return a}function El(e,t,r,i,a,n,o,s){const c=[],l=[];if(s.roughness===0){e/=4,l.push([t+i*Math.cos(-e),r+a*Math.sin(-e)]);for(let h=0;h<=2*Math.PI;h+=e){const u=[t+i*Math.cos(h),r+a*Math.sin(h)];c.push(u),l.push(u)}l.push([t+i*Math.cos(0),r+a*Math.sin(0)]),l.push([t+i*Math.cos(e),r+a*Math.sin(e)])}else{const h=et(.5,s)-Math.PI/2;l.push([et(n,s)+t+.9*i*Math.cos(h-e),et(n,s)+r+.9*a*Math.sin(h-e)]);const u=2*Math.PI+h-.01;for(let p=h;p<u;p+=e){const d=[et(n,s)+t+i*Math.cos(p),et(n,s)+r+a*Math.sin(p)];c.push(d),l.push(d)}l.push([et(n,s)+t+i*Math.cos(h+2*Math.PI+.5*o),et(n,s)+r+a*Math.sin(h+2*Math.PI+.5*o)]),l.push([et(n,s)+t+.98*i*Math.cos(h+o),et(n,s)+r+.98*a*Math.sin(h+o)]),l.push([et(n,s)+t+.9*i*Math.cos(h+.5*o),et(n,s)+r+.9*a*Math.sin(h+.5*o)])}return[l,c]}function Fl(e,t,r,i,a,n,o,s,c){const l=n+et(.1,c),h=[];h.push([et(s,c)+t+.9*i*Math.cos(l-e),et(s,c)+r+.9*a*Math.sin(l-e)]);for(let u=l;u<=o;u+=e)h.push([et(s,c)+t+i*Math.cos(u),et(s,c)+r+a*Math.sin(u)]);return h.push([t+i*Math.cos(o),r+a*Math.sin(o)]),h.push([t+i*Math.cos(o),r+a*Math.sin(o)]),Ma(h,null,c)}function x2(e,t,r,i,a,n,o,s){const c=[],l=[s.maxRandomnessOffset||1,(s.maxRandomnessOffset||1)+.3];let h=[0,0];const u=s.disableMultiStroke?1:2,p=s.preserveVertices;for(let d=0;d<u;d++)d===0?c.push({op:"move",data:[o[0],o[1]]}):c.push({op:"move",data:[o[0]+(p?0:et(l[0],s)),o[1]+(p?0:et(l[0],s))]}),h=p?[a,n]:[a+et(l[d],s),n+et(l[d],s)],c.push({op:"bcurveTo",data:[e+et(l[d],s),t+et(l[d],s),r+et(l[d],s),i+et(l[d],s),h[0],h[1]]});return c}function Zr(e){return[...e]}function $l(e,t=0){const r=e.length;if(r<3)throw new Error("A curve must have at least three points.");const i=[];if(r===3)i.push(Zr(e[0]),Zr(e[1]),Zr(e[2]),Zr(e[2]));else{const a=[];a.push(e[0],e[0]);for(let s=1;s<e.length;s++)a.push(e[s]),s===e.length-1&&a.push(e[s]);const n=[],o=1-t;i.push(Zr(a[0]));for(let s=1;s+2<a.length;s++){const c=a[s];n[0]=[c[0],c[1]],n[1]=[c[0]+(o*a[s+1][0]-o*a[s-1][0])/6,c[1]+(o*a[s+1][1]-o*a[s-1][1])/6],n[2]=[a[s+1][0]+(o*a[s][0]-o*a[s+2][0])/6,a[s+1][1]+(o*a[s][1]-o*a[s+2][1])/6],n[3]=[a[s+1][0],a[s+1][1]],i.push(n[1],n[2],n[3])}}return i}function sa(e,t){return Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2)}function b2(e,t,r){const i=sa(t,r);if(i===0)return sa(e,t);let a=((e[0]-t[0])*(r[0]-t[0])+(e[1]-t[1])*(r[1]-t[1]))/i;return a=Math.max(0,Math.min(1,a)),sa(e,Xe(t,r,a))}function Xe(e,t,r){return[e[0]+(t[0]-e[0])*r,e[1]+(t[1]-e[1])*r]}function ps(e,t,r,i){const a=i||[];if(function(s,c){const l=s[c+0],h=s[c+1],u=s[c+2],p=s[c+3];let d=3*h[0]-2*l[0]-p[0];d*=d;let g=3*h[1]-2*l[1]-p[1];g*=g;let m=3*u[0]-2*p[0]-l[0];m*=m;let y=3*u[1]-2*p[1]-l[1];return y*=y,d<m&&(d=m),g<y&&(g=y),d+g}(e,t)<r){const s=e[t+0];a.length?(n=a[a.length-1],o=s,Math.sqrt(sa(n,o))>1&&a.push(s)):a.push(s),a.push(e[t+3])}else{const c=e[t+0],l=e[t+1],h=e[t+2],u=e[t+3],p=Xe(c,l,.5),d=Xe(l,h,.5),g=Xe(h,u,.5),m=Xe(p,d,.5),y=Xe(d,g,.5),x=Xe(m,y,.5);ps([c,p,m,x],0,r,a),ps([x,y,g,u],0,r,a)}var n,o;return a}function C2(e,t){return Ea(e,0,e.length,t)}function Ea(e,t,r,i,a){const n=a||[],o=e[t],s=e[r-1];let c=0,l=1;for(let h=t+1;h<r-1;++h){const u=b2(e[h],o,s);u>c&&(c=u,l=h)}return Math.sqrt(c)>i?(Ea(e,t,l+1,i,n),Ea(e,l,r,i,n)):(n.length||n.push(o),n.push(s)),n}function Ln(e,t=.15,r){const i=[],a=(e.length-1)/3;for(let n=0;n<a;n++)ps(e,3*n,t,i);return r&&r>0?Ea(i,0,i.length,r):i}const te="none";class Fa{constructor(t){this.defaultOptions={maxRandomnessOffset:2,roughness:1,bowing:1,stroke:"#000",strokeWidth:1,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:"hachure",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,seed:0,disableMultiStroke:!1,disableMultiStrokeFill:!1,preserveVertices:!1,fillShapeRoughnessGain:.8},this.config=t||{},this.config.options&&(this.defaultOptions=this._o(this.config.options))}static newSeed(){return Math.floor(Math.random()*2**31)}_o(t){return t?Object.assign({},this.defaultOptions,t):this.defaultOptions}_d(t,r,i){return{shape:t,sets:r||[],options:i||this.defaultOptions}}line(t,r,i,a,n){const o=this._o(n);return this._d("line",[bd(t,r,i,a,o)],o)}rectangle(t,r,i,a,n){const o=this._o(n),s=[],c=y2(t,r,i,a,o);if(o.fill){const l=[[t,r],[t+i,r],[t+i,r+a],[t,r+a]];o.fillStyle==="solid"?s.push(Bn([l],o)):s.push(yr([l],o))}return o.stroke!==te&&s.push(c),this._d("rectangle",s,o)}ellipse(t,r,i,a,n){const o=this._o(n),s=[],c=Cd(i,a,o),l=us(t,r,o,c);if(o.fill)if(o.fillStyle==="solid"){const h=us(t,r,o,c).opset;h.type="fillPath",s.push(h)}else s.push(yr([l.estimatedPoints],o));return o.stroke!==te&&s.push(l.opset),this._d("ellipse",s,o)}circle(t,r,i,a){const n=this.ellipse(t,r,i,i,a);return n.shape="circle",n}linearPath(t,r){const i=this._o(r);return this._d("linearPath",[na(t,!1,i)],i)}arc(t,r,i,a,n,o,s=!1,c){const l=this._o(c),h=[],u=Ll(t,r,i,a,n,o,s,!0,l);if(s&&l.fill)if(l.fillStyle==="solid"){const p=Object.assign({},l);p.disableMultiStroke=!0;const d=Ll(t,r,i,a,n,o,!0,!1,p);d.type="fillPath",h.push(d)}else h.push(function(p,d,g,m,y,x,b){const k=p,T=d;let v=Math.abs(g/2),C=Math.abs(m/2);v+=et(.01*v,b),C+=et(.01*C,b);let S=y,O=x;for(;S<0;)S+=2*Math.PI,O+=2*Math.PI;O-S>2*Math.PI&&(S=0,O=2*Math.PI);const P=(O-S)/b.curveStepCount,R=[];for(let E=S;E<=O;E+=P)R.push([k+v*Math.cos(E),T+C*Math.sin(E)]);return R.push([k+v*Math.cos(O),T+C*Math.sin(O)]),R.push([k,T]),yr([R],b)}(t,r,i,a,n,o,l));return l.stroke!==te&&h.push(u),this._d("arc",h,l)}curve(t,r){const i=this._o(r),a=[],n=Bl(t,i);if(i.fill&&i.fill!==te)if(i.fillStyle==="solid"){const o=Bl(t,Object.assign(Object.assign({},i),{disableMultiStroke:!0,roughness:i.roughness?i.roughness+i.fillShapeRoughnessGain:0}));a.push({type:"fillPath",ops:this._mergedShape(o.ops)})}else{const o=[],s=t;if(s.length){const c=typeof s[0][0]=="number"?[s]:s;for(const l of c)l.length<3?o.push(...l):l.length===3?o.push(...Ln($l([l[0],l[0],l[1],l[2]]),10,(1+i.roughness)/2)):o.push(...Ln($l(l),10,(1+i.roughness)/2))}o.length&&a.push(yr([o],i))}return i.stroke!==te&&a.push(n),this._d("curve",a,i)}polygon(t,r){const i=this._o(r),a=[],n=na(t,!0,i);return i.fill&&(i.fillStyle==="solid"?a.push(Bn([t],i)):a.push(yr([t],i))),i.stroke!==te&&a.push(n),this._d("polygon",a,i)}path(t,r){const i=this._o(r),a=[];if(!t)return this._d("path",a,i);t=(t||"").replace(/\n/g," ").replace(/(-\s)/g,"-").replace("/(ss)/g"," ");const n=i.fill&&i.fill!=="transparent"&&i.fill!==te,o=i.stroke!==te,s=!!(i.simplification&&i.simplification<1),c=function(h,u,p){const d=yd(md(ho(h))),g=[];let m=[],y=[0,0],x=[];const b=()=>{x.length>=4&&m.push(...Ln(x,u)),x=[]},k=()=>{b(),m.length&&(g.push(m),m=[])};for(const{key:v,data:C}of d)switch(v){case"M":k(),y=[C[0],C[1]],m.push(y);break;case"L":b(),m.push([C[0],C[1]]);break;case"C":if(!x.length){const S=m.length?m[m.length-1]:y;x.push([S[0],S[1]])}x.push([C[0],C[1]]),x.push([C[2],C[3]]),x.push([C[4],C[5]]);break;case"Z":b(),m.push([y[0],y[1]])}if(k(),!p)return g;const T=[];for(const v of g){const C=C2(v,p);C.length&&T.push(C)}return T}(t,1,s?4-4*(i.simplification||1):(1+i.roughness)/2),l=Al(t,i);if(n)if(i.fillStyle==="solid")if(c.length===1){const h=Al(t,Object.assign(Object.assign({},i),{disableMultiStroke:!0,roughness:i.roughness?i.roughness+i.fillShapeRoughnessGain:0}));a.push({type:"fillPath",ops:this._mergedShape(h.ops)})}else a.push(Bn(c,i));else a.push(yr(c,i));return o&&(s?c.forEach(h=>{a.push(na(h,!1,i))}):a.push(l)),this._d("path",a,i)}opsToPath(t,r){let i="";for(const a of t.ops){const n=typeof r=="number"&&r>=0?a.data.map(o=>+o.toFixed(r)):a.data;switch(a.op){case"move":i+=`M${n[0]} ${n[1]} `;break;case"bcurveTo":i+=`C${n[0]} ${n[1]}, ${n[2]} ${n[3]}, ${n[4]} ${n[5]} `;break;case"lineTo":i+=`L${n[0]} ${n[1]} `}}return i.trim()}toPaths(t){const r=t.sets||[],i=t.options||this.defaultOptions,a=[];for(const n of r){let o=null;switch(n.type){case"path":o={d:this.opsToPath(n),stroke:i.stroke,strokeWidth:i.strokeWidth,fill:te};break;case"fillPath":o={d:this.opsToPath(n),stroke:te,strokeWidth:0,fill:i.fill||te};break;case"fillSketch":o=this.fillSketch(n,i)}o&&a.push(o)}return a}fillSketch(t,r){let i=r.fillWeight;return i<0&&(i=r.strokeWidth/2),{d:this.opsToPath(t),stroke:r.fill||te,strokeWidth:i,fill:te}}_mergedShape(t){return t.filter((r,i)=>i===0||r.op!=="move")}}class k2{constructor(t,r){this.canvas=t,this.ctx=this.canvas.getContext("2d"),this.gen=new Fa(r)}draw(t){const r=t.sets||[],i=t.options||this.getDefaultOptions(),a=this.ctx,n=t.options.fixedDecimalPlaceDigits;for(const o of r)switch(o.type){case"path":a.save(),a.strokeStyle=i.stroke==="none"?"transparent":i.stroke,a.lineWidth=i.strokeWidth,i.strokeLineDash&&a.setLineDash(i.strokeLineDash),i.strokeLineDashOffset&&(a.lineDashOffset=i.strokeLineDashOffset),this._drawToContext(a,o,n),a.restore();break;case"fillPath":{a.save(),a.fillStyle=i.fill||"";const s=t.shape==="curve"||t.shape==="polygon"||t.shape==="path"?"evenodd":"nonzero";this._drawToContext(a,o,n,s),a.restore();break}case"fillSketch":this.fillSketch(a,o,i)}}fillSketch(t,r,i){let a=i.fillWeight;a<0&&(a=i.strokeWidth/2),t.save(),i.fillLineDash&&t.setLineDash(i.fillLineDash),i.fillLineDashOffset&&(t.lineDashOffset=i.fillLineDashOffset),t.strokeStyle=i.fill||"",t.lineWidth=a,this._drawToContext(t,r,i.fixedDecimalPlaceDigits),t.restore()}_drawToContext(t,r,i,a="nonzero"){t.beginPath();for(const n of r.ops){const o=typeof i=="number"&&i>=0?n.data.map(s=>+s.toFixed(i)):n.data;switch(n.op){case"move":t.moveTo(o[0],o[1]);break;case"bcurveTo":t.bezierCurveTo(o[0],o[1],o[2],o[3],o[4],o[5]);break;case"lineTo":t.lineTo(o[0],o[1])}}r.type==="fillPath"?t.fill(a):t.stroke()}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}line(t,r,i,a,n){const o=this.gen.line(t,r,i,a,n);return this.draw(o),o}rectangle(t,r,i,a,n){const o=this.gen.rectangle(t,r,i,a,n);return this.draw(o),o}ellipse(t,r,i,a,n){const o=this.gen.ellipse(t,r,i,a,n);return this.draw(o),o}circle(t,r,i,a){const n=this.gen.circle(t,r,i,a);return this.draw(n),n}linearPath(t,r){const i=this.gen.linearPath(t,r);return this.draw(i),i}polygon(t,r){const i=this.gen.polygon(t,r);return this.draw(i),i}arc(t,r,i,a,n,o,s=!1,c){const l=this.gen.arc(t,r,i,a,n,o,s,c);return this.draw(l),l}curve(t,r){const i=this.gen.curve(t,r);return this.draw(i),i}path(t,r){const i=this.gen.path(t,r);return this.draw(i),i}}const Vi="http://www.w3.org/2000/svg";class w2{constructor(t,r){this.svg=t,this.gen=new Fa(r)}draw(t){const r=t.sets||[],i=t.options||this.getDefaultOptions(),a=this.svg.ownerDocument||window.document,n=a.createElementNS(Vi,"g"),o=t.options.fixedDecimalPlaceDigits;for(const s of r){let c=null;switch(s.type){case"path":c=a.createElementNS(Vi,"path"),c.setAttribute("d",this.opsToPath(s,o)),c.setAttribute("stroke",i.stroke),c.setAttribute("stroke-width",i.strokeWidth+""),c.setAttribute("fill","none"),i.strokeLineDash&&c.setAttribute("stroke-dasharray",i.strokeLineDash.join(" ").trim()),i.strokeLineDashOffset&&c.setAttribute("stroke-dashoffset",`${i.strokeLineDashOffset}`);break;case"fillPath":c=a.createElementNS(Vi,"path"),c.setAttribute("d",this.opsToPath(s,o)),c.setAttribute("stroke","none"),c.setAttribute("stroke-width","0"),c.setAttribute("fill",i.fill||""),t.shape!=="curve"&&t.shape!=="polygon"||c.setAttribute("fill-rule","evenodd");break;case"fillSketch":c=this.fillSketch(a,s,i)}c&&n.appendChild(c)}return n}fillSketch(t,r,i){let a=i.fillWeight;a<0&&(a=i.strokeWidth/2);const n=t.createElementNS(Vi,"path");return n.setAttribute("d",this.opsToPath(r,i.fixedDecimalPlaceDigits)),n.setAttribute("stroke",i.fill||""),n.setAttribute("stroke-width",a+""),n.setAttribute("fill","none"),i.fillLineDash&&n.setAttribute("stroke-dasharray",i.fillLineDash.join(" ").trim()),i.fillLineDashOffset&&n.setAttribute("stroke-dashoffset",`${i.fillLineDashOffset}`),n}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}opsToPath(t,r){return this.gen.opsToPath(t,r)}line(t,r,i,a,n){const o=this.gen.line(t,r,i,a,n);return this.draw(o)}rectangle(t,r,i,a,n){const o=this.gen.rectangle(t,r,i,a,n);return this.draw(o)}ellipse(t,r,i,a,n){const o=this.gen.ellipse(t,r,i,a,n);return this.draw(o)}circle(t,r,i,a){const n=this.gen.circle(t,r,i,a);return this.draw(n)}linearPath(t,r){const i=this.gen.linearPath(t,r);return this.draw(i)}polygon(t,r){const i=this.gen.polygon(t,r);return this.draw(i)}arc(t,r,i,a,n,o,s=!1,c){const l=this.gen.arc(t,r,i,a,n,o,s,c);return this.draw(l)}curve(t,r){const i=this.gen.curve(t,r);return this.draw(i)}path(t,r){const i=this.gen.path(t,r);return this.draw(i)}}var j={canvas:(e,t)=>new k2(e,t),svg:(e,t)=>new w2(e,t),generator:e=>new Fa(e),newSeed:()=>Fa.newSeed()},rt=f(async(e,t,r)=>{var u,p;let i;const a=t.useHtmlLabels||At((u=ut())==null?void 0:u.htmlLabels);r?i=r:i="node default";const n=e.insert("g").attr("class",i).attr("id",t.domId||t.id),o=n.insert("g").attr("class","label").attr("style",Jt(t.labelStyle));let s;t.label===void 0?s="":s=typeof t.label=="string"?t.label:t.label[0];const c=await Ye(o,ne(cr(s),ut()),{useHtmlLabels:a,width:t.width||((p=ut().flowchart)==null?void 0:p.wrappingWidth),cssClasses:"markdown-node-label",style:t.labelStyle,addSvgBackground:!!t.icon||!!t.img});let l=c.getBBox();const h=((t==null?void 0:t.padding)??0)/2;if(a){const d=c.children[0],g=ht(c),m=d.getElementsByTagName("img");if(m){const y=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...m].map(x=>new Promise(b=>{function k(){if(x.style.display="flex",x.style.flexDirection="column",y){const T=ut().fontSize?ut().fontSize:window.getComputedStyle(document.body).fontSize,v=5,[C=bc.fontSize]=Va(T),S=C*v+"px";x.style.minWidth=S,x.style.maxWidth=S}else x.style.width="100%";b(x)}f(k,"setupImage"),setTimeout(()=>{x.complete&&k()}),x.addEventListener("error",k),x.addEventListener("load",k)})))}l=d.getBoundingClientRect(),g.attr("width",l.width),g.attr("height",l.height)}return a?o.attr("transform","translate("+-l.width/2+", "+-l.height/2+")"):o.attr("transform","translate(0, "+-l.height/2+")"),t.centerLabel&&o.attr("transform","translate("+-l.width/2+", "+-l.height/2+")"),o.insert("rect",":first-child"),{shapeSvg:n,bbox:l,halfPadding:h,label:o}},"labelHelper"),An=f(async(e,t,r)=>{var c,l,h,u,p,d;const i=r.useHtmlLabels||At((l=(c=ut())==null?void 0:c.flowchart)==null?void 0:l.htmlLabels),a=e.insert("g").attr("class","label").attr("style",r.labelStyle||""),n=await Ye(a,ne(cr(t),ut()),{useHtmlLabels:i,width:r.width||((u=(h=ut())==null?void 0:h.flowchart)==null?void 0:u.wrappingWidth),style:r.labelStyle,addSvgBackground:!!r.icon||!!r.img});let o=n.getBBox();const s=r.padding/2;if(At((d=(p=ut())==null?void 0:p.flowchart)==null?void 0:d.htmlLabels)){const g=n.children[0],m=ht(n);o=g.getBoundingClientRect(),m.attr("width",o.width),m.attr("height",o.height)}return i?a.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"):a.attr("transform","translate(0, "+-o.height/2+")"),r.centerLabel&&a.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"),a.insert("rect",":first-child"),{shapeSvg:e,bbox:o,halfPadding:s,label:a}},"insertLabel"),X=f((e,t)=>{const r=t.node().getBBox();e.width=r.width,e.height=r.height},"updateNodeBounds"),tt=f((e,t)=>(e.look==="handDrawn"?"rough-node":"node")+" "+e.cssClasses+" "+(t||""),"getNodeClasses");function lt(e){const t=e.map((r,i)=>`${i===0?"M":"L"}${r.x},${r.y}`);return t.push("Z"),t.join(" ")}f(lt,"createPathFromPoints");function He(e,t,r,i,a,n){const o=[],c=r-e,l=i-t,h=c/n,u=2*Math.PI/h,p=t+l/2;for(let d=0;d<=50;d++){const g=d/50,m=e+g*c,y=p+a*Math.sin(u*(m-e));o.push({x:m,y})}return o}f(He,"generateFullSineWavePoints");function yi(e,t,r,i,a,n){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const p=s+u*h,d=e+r*Math.cos(p),g=t+r*Math.sin(p);o.push({x:-d,y:-g})}return o}f(yi,"generateCirclePoints");var v2=f((e,t)=>{var r=e.x,i=e.y,a=t.x-r,n=t.y-i,o=e.width/2,s=e.height/2,c,l;return Math.abs(n)*o>Math.abs(a)*s?(n<0&&(s=-s),c=n===0?0:s*a/n,l=s):(a<0&&(o=-o),c=o,l=a===0?0:o*n/a),{x:r+c,y:i+l}},"intersectRect"),Rr=v2;function wd(e,t){t&&e.attr("style",t)}f(wd,"applyStyle");async function vd(e){const t=ht(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),r=t.append("xhtml:div"),i=ut();let a=e.label;e.label&&_r(e.label)&&(a=await _s(e.label.replace(Fr.lineBreakRegex,`
`),i));const o='<span class="'+(e.isNode?"nodeLabel":"edgeLabel")+'" '+(e.labelStyle?'style="'+e.labelStyle+'"':"")+">"+a+"</span>";return r.html(ne(o,i)),wd(r,e.labelStyle),r.style("display","inline-block"),r.style("padding-right","1px"),r.style("white-space","nowrap"),r.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}f(vd,"addHtmlLabel");var S2=f(async(e,t,r,i)=>{let a=e||"";if(typeof a=="object"&&(a=a[0]),At(ut().flowchart.htmlLabels)){a=a.replace(/\\n|\n/g,"<br />"),$.info("vertexText"+a);const n={isNode:i,label:cr(a).replace(/fa[blrs]?:fa-[\w-]+/g,s=>`<i class='${s.replace(":"," ")}'></i>`),labelStyle:t&&t.replace("fill:","color:")};return await vd(n)}else{const n=document.createElementNS("http://www.w3.org/2000/svg","text");n.setAttribute("style",t.replace("color:","fill:"));let o=[];typeof a=="string"?o=a.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(a)?o=a:o=[];for(const s of o){const c=document.createElementNS("http://www.w3.org/2000/svg","tspan");c.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),c.setAttribute("dy","1em"),c.setAttribute("x","0"),r?c.setAttribute("class","title-row"):c.setAttribute("class","row"),c.textContent=s.trim(),n.appendChild(c)}return n}},"createLabel"),tr=S2,Ue=f((e,t,r,i,a)=>["M",e+a,t,"H",e+r-a,"A",a,a,0,0,1,e+r,t+a,"V",t+i-a,"A",a,a,0,0,1,e+r-a,t+i,"H",e+a,"A",a,a,0,0,1,e,t+i-a,"V",t+a,"A",a,a,0,0,1,e+a,t,"Z"].join(" "),"createRoundedRectPathD"),Sd=f(async(e,t)=>{$.info("Creating subgraph rect for ",t.id,t);const r=ut(),{themeVariables:i,handDrawnSeed:a}=r,{clusterBkg:n,clusterBorder:o}=i,{labelStyles:s,nodeStyles:c,borderStyles:l,backgroundStyles:h}=Z(t),u=e.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),p=At(r.flowchart.htmlLabels),d=u.insert("g").attr("class","cluster-label "),g=await Ye(d,t.label,{style:t.labelStyle,useHtmlLabels:p,isNode:!0});let m=g.getBBox();if(At(r.flowchart.htmlLabels)){const S=g.children[0],O=ht(g);m=S.getBoundingClientRect(),O.attr("width",m.width),O.attr("height",m.height)}const y=t.width<=m.width+t.padding?m.width+t.padding:t.width;t.width<=m.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;const x=t.height,b=t.x-y/2,k=t.y-x/2;$.trace("Data ",t,JSON.stringify(t));let T;if(t.look==="handDrawn"){const S=j.svg(u),O=Y(t,{roughness:.7,fill:n,stroke:o,fillWeight:3,seed:a}),P=S.path(Ue(b,k,y,x,0),O);T=u.insert(()=>($.debug("Rough node insert CXC",P),P),":first-child"),T.select("path:nth-child(2)").attr("style",l.join(";")),T.select("path").attr("style",h.join(";").replace("fill","stroke"))}else T=u.insert("rect",":first-child"),T.attr("style",c).attr("rx",t.rx).attr("ry",t.ry).attr("x",b).attr("y",k).attr("width",y).attr("height",x);const{subGraphTitleTopMargin:v}=Ns(r);if(d.attr("transform",`translate(${t.x-m.width/2}, ${t.y-t.height/2+v})`),s){const S=d.select("span");S&&S.attr("style",s)}const C=T.node().getBBox();return t.offsetX=0,t.width=C.width,t.height=C.height,t.offsetY=m.height-t.padding/2,t.intersect=function(S){return Rr(t,S)},{cluster:u,labelBBox:m}},"rect"),T2=f((e,t)=>{const r=e.insert("g").attr("class","note-cluster").attr("id",t.id),i=r.insert("rect",":first-child"),a=0*t.padding,n=a/2;i.attr("rx",t.rx).attr("ry",t.ry).attr("x",t.x-t.width/2-n).attr("y",t.y-t.height/2-n).attr("width",t.width+a).attr("height",t.height+a).attr("fill","none");const o=i.node().getBBox();return t.width=o.width,t.height=o.height,t.intersect=function(s){return Rr(t,s)},{cluster:r,labelBBox:{width:0,height:0}}},"noteGroup"),_2=f(async(e,t)=>{const r=ut(),{themeVariables:i,handDrawnSeed:a}=r,{altBackground:n,compositeBackground:o,compositeTitleBackground:s,nodeBorder:c}=i,l=e.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-id",t.id).attr("data-look",t.look),h=l.insert("g",":first-child"),u=l.insert("g").attr("class","cluster-label");let p=l.append("rect");const d=u.node().appendChild(await tr(t.label,t.labelStyle,void 0,!0));let g=d.getBBox();if(At(r.flowchart.htmlLabels)){const P=d.children[0],R=ht(d);g=P.getBoundingClientRect(),R.attr("width",g.width),R.attr("height",g.height)}const m=0*t.padding,y=m/2,x=(t.width<=g.width+t.padding?g.width+t.padding:t.width)+m;t.width<=g.width+t.padding?t.diff=(x-t.width)/2-t.padding:t.diff=-t.padding;const b=t.height+m,k=t.height+m-g.height-6,T=t.x-x/2,v=t.y-b/2;t.width=x;const C=t.y-t.height/2-y+g.height+2;let S;if(t.look==="handDrawn"){const P=t.cssClasses.includes("statediagram-cluster-alt"),R=j.svg(l),E=t.rx||t.ry?R.path(Ue(T,v,x,b,10),{roughness:.7,fill:s,fillStyle:"solid",stroke:c,seed:a}):R.rectangle(T,v,x,b,{seed:a});S=l.insert(()=>E,":first-child");const N=R.rectangle(T,C,x,k,{fill:P?n:o,fillStyle:P?"hachure":"solid",stroke:c,seed:a});S=l.insert(()=>E,":first-child"),p=l.insert(()=>N)}else S=h.insert("rect",":first-child"),S.attr("class","outer").attr("x",T).attr("y",v).attr("width",x).attr("height",b).attr("data-look",t.look),p.attr("class","inner").attr("x",T).attr("y",C).attr("width",x).attr("height",k);u.attr("transform",`translate(${t.x-g.width/2}, ${v+1-(At(r.flowchart.htmlLabels)?0:3)})`);const O=S.node().getBBox();return t.height=O.height,t.offsetX=0,t.offsetY=g.height-t.padding/2,t.labelBBox=g,t.intersect=function(P){return Rr(t,P)},{cluster:l,labelBBox:g}},"roundedWithTitle"),B2=f(async(e,t)=>{$.info("Creating subgraph rect for ",t.id,t);const r=ut(),{themeVariables:i,handDrawnSeed:a}=r,{clusterBkg:n,clusterBorder:o}=i,{labelStyles:s,nodeStyles:c,borderStyles:l,backgroundStyles:h}=Z(t),u=e.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),p=At(r.flowchart.htmlLabels),d=u.insert("g").attr("class","cluster-label "),g=await Ye(d,t.label,{style:t.labelStyle,useHtmlLabels:p,isNode:!0,width:t.width});let m=g.getBBox();if(At(r.flowchart.htmlLabels)){const S=g.children[0],O=ht(g);m=S.getBoundingClientRect(),O.attr("width",m.width),O.attr("height",m.height)}const y=t.width<=m.width+t.padding?m.width+t.padding:t.width;t.width<=m.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;const x=t.height,b=t.x-y/2,k=t.y-x/2;$.trace("Data ",t,JSON.stringify(t));let T;if(t.look==="handDrawn"){const S=j.svg(u),O=Y(t,{roughness:.7,fill:n,stroke:o,fillWeight:4,seed:a}),P=S.path(Ue(b,k,y,x,t.rx),O);T=u.insert(()=>($.debug("Rough node insert CXC",P),P),":first-child"),T.select("path:nth-child(2)").attr("style",l.join(";")),T.select("path").attr("style",h.join(";").replace("fill","stroke"))}else T=u.insert("rect",":first-child"),T.attr("style",c).attr("rx",t.rx).attr("ry",t.ry).attr("x",b).attr("y",k).attr("width",y).attr("height",x);const{subGraphTitleTopMargin:v}=Ns(r);if(d.attr("transform",`translate(${t.x-m.width/2}, ${t.y-t.height/2+v})`),s){const S=d.select("span");S&&S.attr("style",s)}const C=T.node().getBBox();return t.offsetX=0,t.width=C.width,t.height=C.height,t.offsetY=m.height-t.padding/2,t.intersect=function(S){return Rr(t,S)},{cluster:u,labelBBox:m}},"kanbanSection"),L2=f((e,t)=>{const r=ut(),{themeVariables:i,handDrawnSeed:a}=r,{nodeBorder:n}=i,o=e.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-look",t.look),s=o.insert("g",":first-child"),c=0*t.padding,l=t.width+c;t.diff=-t.padding;const h=t.height+c,u=t.x-l/2,p=t.y-h/2;t.width=l;let d;if(t.look==="handDrawn"){const y=j.svg(o).rectangle(u,p,l,h,{fill:"lightgrey",roughness:.5,strokeLineDash:[5],stroke:n,seed:a});d=o.insert(()=>y,":first-child")}else d=s.insert("rect",":first-child"),d.attr("class","divider").attr("x",u).attr("y",p).attr("width",l).attr("height",h).attr("data-look",t.look);const g=d.node().getBBox();return t.height=g.height,t.offsetX=0,t.offsetY=0,t.intersect=function(m){return Rr(t,m)},{cluster:o,labelBBox:{}}},"divider"),A2=Sd,M2={rect:Sd,squareRect:A2,roundedWithTitle:_2,noteGroup:T2,divider:L2,kanbanSection:B2},Td=new Map,E2=f(async(e,t)=>{const r=t.shape||"rect",i=await M2[r](e,t);return Td.set(t.id,i),i},"insertCluster"),QS=f(()=>{Td=new Map},"clear");function _d(e,t){return e.intersect(t)}f(_d,"intersectNode");var F2=_d;function Bd(e,t,r,i){var a=e.x,n=e.y,o=a-i.x,s=n-i.y,c=Math.sqrt(t*t*s*s+r*r*o*o),l=Math.abs(t*r*o/c);i.x<a&&(l=-l);var h=Math.abs(t*r*s/c);return i.y<n&&(h=-h),{x:a+l,y:n+h}}f(Bd,"intersectEllipse");var Ld=Bd;function Ad(e,t,r){return Ld(e,t,t,r)}f(Ad,"intersectCircle");var $2=Ad;function Md(e,t,r,i){var a,n,o,s,c,l,h,u,p,d,g,m,y,x,b;if(a=t.y-e.y,o=e.x-t.x,c=t.x*e.y-e.x*t.y,p=a*r.x+o*r.y+c,d=a*i.x+o*i.y+c,!(p!==0&&d!==0&&fs(p,d))&&(n=i.y-r.y,s=r.x-i.x,l=i.x*r.y-r.x*i.y,h=n*e.x+s*e.y+l,u=n*t.x+s*t.y+l,!(h!==0&&u!==0&&fs(h,u))&&(g=a*s-n*o,g!==0)))return m=Math.abs(g/2),y=o*l-s*c,x=y<0?(y-m)/g:(y+m)/g,y=n*c-a*l,b=y<0?(y-m)/g:(y+m)/g,{x,y:b}}f(Md,"intersectLine");function fs(e,t){return e*t>0}f(fs,"sameSign");var O2=Md;function Ed(e,t,r){let i=e.x,a=e.y,n=[],o=Number.POSITIVE_INFINITY,s=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(h){o=Math.min(o,h.x),s=Math.min(s,h.y)}):(o=Math.min(o,t.x),s=Math.min(s,t.y));let c=i-e.width/2-o,l=a-e.height/2-s;for(let h=0;h<t.length;h++){let u=t[h],p=t[h<t.length-1?h+1:0],d=O2(e,r,{x:c+u.x,y:l+u.y},{x:c+p.x,y:l+p.y});d&&n.push(d)}return n.length?(n.length>1&&n.sort(function(h,u){let p=h.x-r.x,d=h.y-r.y,g=Math.sqrt(p*p+d*d),m=u.x-r.x,y=u.y-r.y,x=Math.sqrt(m*m+y*y);return g<x?-1:g===x?0:1}),n[0]):e}f(Ed,"intersectPolygon");var D2=Ed,H={node:F2,circle:$2,ellipse:Ld,polygon:D2,rect:Rr};function Fd(e,t){const{labelStyles:r}=Z(t);t.labelStyle=r;const i=tt(t);let a=i;i||(a="anchor");const n=e.insert("g").attr("class",a).attr("id",t.domId||t.id),o=1,{cssStyles:s}=t,c=j.svg(n),l=Y(t,{fill:"black",stroke:"none",fillStyle:"solid"});t.look!=="handDrawn"&&(l.roughness=0);const h=c.circle(0,0,o*2,l),u=n.insert(()=>h,":first-child");return u.attr("class","anchor").attr("style",Jt(s)),X(t,u),t.intersect=function(p){return $.info("Circle intersect",t,o,p),H.circle(t,o,p)},n}f(Fd,"anchor");function gs(e,t,r,i,a,n,o){const c=(e+r)/2,l=(t+i)/2,h=Math.atan2(i-t,r-e),u=(r-e)/2,p=(i-t)/2,d=u/a,g=p/n,m=Math.sqrt(d**2+g**2);if(m>1)throw new Error("The given radii are too small to create an arc between the points.");const y=Math.sqrt(1-m**2),x=c+y*n*Math.sin(h)*(o?-1:1),b=l-y*a*Math.cos(h)*(o?-1:1),k=Math.atan2((t-b)/n,(e-x)/a);let v=Math.atan2((i-b)/n,(r-x)/a)-k;o&&v<0&&(v+=2*Math.PI),!o&&v>0&&(v-=2*Math.PI);const C=[];for(let S=0;S<20;S++){const O=S/19,P=k+O*v,R=x+a*Math.cos(P),E=b+n*Math.sin(P);C.push({x:R,y:E})}return C}f(gs,"generateArcPoints");async function $d(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=n.width+t.padding+20,s=n.height+t.padding,c=s/2,l=c/(2.5+s/50),{cssStyles:h}=t,u=[{x:o/2,y:-s/2},{x:-o/2,y:-s/2},...gs(-o/2,-s/2,-o/2,s/2,l,c,!1),{x:o/2,y:s/2},...gs(o/2,s/2,o/2,-s/2,l,c,!0)],p=j.svg(a),d=Y(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=lt(u),m=p.path(g,d),y=a.insert(()=>m,":first-child");return y.attr("class","basic label-container"),h&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",i),y.attr("transform",`translate(${l/2}, 0)`),X(t,y),t.intersect=function(x){return H.polygon(t,u,x)},a}f($d,"bowTieRect");function Ge(e,t,r,i){return e.insert("polygon",":first-child").attr("points",i.map(function(a){return a.x+","+a.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+r/2+")")}f(Ge,"insertPolygonShape");async function Od(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=n.height+t.padding,s=12,c=n.width+t.padding+s,l=0,h=c,u=-o,p=0,d=[{x:l+s,y:u},{x:h,y:u},{x:h,y:p},{x:l,y:p},{x:l,y:u+s},{x:l+s,y:u}];let g;const{cssStyles:m}=t;if(t.look==="handDrawn"){const y=j.svg(a),x=Y(t,{}),b=lt(d),k=y.path(b,x);g=a.insert(()=>k,":first-child").attr("transform",`translate(${-c/2}, ${o/2})`),m&&g.attr("style",m)}else g=Ge(a,c,o,d);return i&&g.attr("style",i),X(t,g),t.intersect=function(y){return H.polygon(t,d,y)},a}f(Od,"card");function Dd(e,t){const{nodeStyles:r}=Z(t);t.label="";const i=e.insert("g").attr("class",tt(t)).attr("id",t.domId??t.id),{cssStyles:a}=t,n=Math.max(28,t.width??0),o=[{x:0,y:n/2},{x:n/2,y:0},{x:0,y:-n/2},{x:-n/2,y:0}],s=j.svg(i),c=Y(t,{});t.look!=="handDrawn"&&(c.roughness=0,c.fillStyle="solid");const l=lt(o),h=s.path(l,c),u=i.insert(()=>h,":first-child");return a&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",a),r&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",r),t.width=28,t.height=28,t.intersect=function(p){return H.polygon(t,o,p)},i}f(Dd,"choice");async function Rd(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,halfPadding:o}=await rt(e,t,tt(t)),s=n.width/2+o;let c;const{cssStyles:l}=t;if(t.look==="handDrawn"){const h=j.svg(a),u=Y(t,{}),p=h.circle(0,0,s*2,u);c=a.insert(()=>p,":first-child"),c.attr("class","basic label-container").attr("style",Jt(l))}else c=a.insert("circle",":first-child").attr("class","basic label-container").attr("style",i).attr("r",s).attr("cx",0).attr("cy",0);return X(t,c),t.intersect=function(h){return $.info("Circle intersect",t,s,h),H.circle(t,s,h)},a}f(Rd,"circle");function Id(e){const t=Math.cos(Math.PI/4),r=Math.sin(Math.PI/4),i=e*2,a={x:i/2*t,y:i/2*r},n={x:-(i/2)*t,y:i/2*r},o={x:-(i/2)*t,y:-(i/2)*r},s={x:i/2*t,y:-(i/2)*r};return`M ${n.x},${n.y} L ${s.x},${s.y}
                   M ${a.x},${a.y} L ${o.x},${o.y}`}f(Id,"createLine");function Pd(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r,t.label="";const a=e.insert("g").attr("class",tt(t)).attr("id",t.domId??t.id),n=Math.max(30,(t==null?void 0:t.width)??0),{cssStyles:o}=t,s=j.svg(a),c=Y(t,{});t.look!=="handDrawn"&&(c.roughness=0,c.fillStyle="solid");const l=s.circle(0,0,n*2,c),h=Id(n),u=s.path(h,c),p=a.insert(()=>l,":first-child");return p.insert(()=>u),o&&t.look!=="handDrawn"&&p.selectAll("path").attr("style",o),i&&t.look!=="handDrawn"&&p.selectAll("path").attr("style",i),X(t,p),t.intersect=function(d){return $.info("crossedCircle intersect",t,{radius:n,point:d}),H.circle(t,n,d)},a}f(Pd,"crossedCircle");function Ae(e,t,r,i=100,a=0,n=180){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const p=s+u*h,d=e+r*Math.cos(p),g=t+r*Math.sin(p);o.push({x:-d,y:-g})}return o}f(Ae,"generateCirclePoints");async function Nd(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=n.width+(t.padding??0),c=n.height+(t.padding??0),l=Math.max(5,c*.1),{cssStyles:h}=t,u=[...Ae(s/2,-c/2,l,30,-90,0),{x:-s/2-l,y:l},...Ae(s/2+l*2,-l,l,20,-180,-270),...Ae(s/2+l*2,l,l,20,-90,-180),{x:-s/2-l,y:-c/2},...Ae(s/2,c/2,l,20,0,90)],p=[{x:s/2,y:-c/2-l},{x:-s/2,y:-c/2-l},...Ae(s/2,-c/2,l,20,-90,0),{x:-s/2-l,y:-l},...Ae(s/2+s*.1,-l,l,20,-180,-270),...Ae(s/2+s*.1,l,l,20,-90,-180),{x:-s/2-l,y:c/2},...Ae(s/2,c/2,l,20,0,90),{x:-s/2,y:c/2+l},{x:s/2,y:c/2+l}],d=j.svg(a),g=Y(t,{fill:"none"});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const y=lt(u).replace("Z",""),x=d.path(y,g),b=lt(p),k=d.path(b,{...g}),T=a.insert("g",":first-child");return T.insert(()=>k,":first-child").attr("stroke-opacity",0),T.insert(()=>x,":first-child"),T.attr("class","text"),h&&t.look!=="handDrawn"&&T.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&T.selectAll("path").attr("style",i),T.attr("transform",`translate(${l}, 0)`),o.attr("transform",`translate(${-s/2+l-(n.x-(n.left??0))},${-c/2+(t.padding??0)/2-(n.y-(n.top??0))})`),X(t,T),t.intersect=function(v){return H.polygon(t,p,v)},a}f(Nd,"curlyBraceLeft");function Me(e,t,r,i=100,a=0,n=180){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const p=s+u*h,d=e+r*Math.cos(p),g=t+r*Math.sin(p);o.push({x:d,y:g})}return o}f(Me,"generateCirclePoints");async function zd(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=n.width+(t.padding??0),c=n.height+(t.padding??0),l=Math.max(5,c*.1),{cssStyles:h}=t,u=[...Me(s/2,-c/2,l,20,-90,0),{x:s/2+l,y:-l},...Me(s/2+l*2,-l,l,20,-180,-270),...Me(s/2+l*2,l,l,20,-90,-180),{x:s/2+l,y:c/2},...Me(s/2,c/2,l,20,0,90)],p=[{x:-s/2,y:-c/2-l},{x:s/2,y:-c/2-l},...Me(s/2,-c/2,l,20,-90,0),{x:s/2+l,y:-l},...Me(s/2+l*2,-l,l,20,-180,-270),...Me(s/2+l*2,l,l,20,-90,-180),{x:s/2+l,y:c/2},...Me(s/2,c/2,l,20,0,90),{x:s/2,y:c/2+l},{x:-s/2,y:c/2+l}],d=j.svg(a),g=Y(t,{fill:"none"});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const y=lt(u).replace("Z",""),x=d.path(y,g),b=lt(p),k=d.path(b,{...g}),T=a.insert("g",":first-child");return T.insert(()=>k,":first-child").attr("stroke-opacity",0),T.insert(()=>x,":first-child"),T.attr("class","text"),h&&t.look!=="handDrawn"&&T.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&T.selectAll("path").attr("style",i),T.attr("transform",`translate(${-l}, 0)`),o.attr("transform",`translate(${-s/2+(t.padding??0)/2-(n.x-(n.left??0))},${-c/2+(t.padding??0)/2-(n.y-(n.top??0))})`),X(t,T),t.intersect=function(v){return H.polygon(t,p,v)},a}f(zd,"curlyBraceRight");function Ft(e,t,r,i=100,a=0,n=180){const o=[],s=a*Math.PI/180,h=(n*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const p=s+u*h,d=e+r*Math.cos(p),g=t+r*Math.sin(p);o.push({x:-d,y:-g})}return o}f(Ft,"generateCirclePoints");async function Wd(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=n.width+(t.padding??0),c=n.height+(t.padding??0),l=Math.max(5,c*.1),{cssStyles:h}=t,u=[...Ft(s/2,-c/2,l,30,-90,0),{x:-s/2-l,y:l},...Ft(s/2+l*2,-l,l,20,-180,-270),...Ft(s/2+l*2,l,l,20,-90,-180),{x:-s/2-l,y:-c/2},...Ft(s/2,c/2,l,20,0,90)],p=[...Ft(-s/2+l+l/2,-c/2,l,20,-90,-180),{x:s/2-l/2,y:l},...Ft(-s/2-l/2,-l,l,20,0,90),...Ft(-s/2-l/2,l,l,20,-90,0),{x:s/2-l/2,y:-l},...Ft(-s/2+l+l/2,c/2,l,30,-180,-270)],d=[{x:s/2,y:-c/2-l},{x:-s/2,y:-c/2-l},...Ft(s/2,-c/2,l,20,-90,0),{x:-s/2-l,y:-l},...Ft(s/2+l*2,-l,l,20,-180,-270),...Ft(s/2+l*2,l,l,20,-90,-180),{x:-s/2-l,y:c/2},...Ft(s/2,c/2,l,20,0,90),{x:-s/2,y:c/2+l},{x:s/2-l-l/2,y:c/2+l},...Ft(-s/2+l+l/2,-c/2,l,20,-90,-180),{x:s/2-l/2,y:l},...Ft(-s/2-l/2,-l,l,20,0,90),...Ft(-s/2-l/2,l,l,20,-90,0),{x:s/2-l/2,y:-l},...Ft(-s/2+l+l/2,c/2,l,30,-180,-270)],g=j.svg(a),m=Y(t,{fill:"none"});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const x=lt(u).replace("Z",""),b=g.path(x,m),T=lt(p).replace("Z",""),v=g.path(T,m),C=lt(d),S=g.path(C,{...m}),O=a.insert("g",":first-child");return O.insert(()=>S,":first-child").attr("stroke-opacity",0),O.insert(()=>b,":first-child"),O.insert(()=>v,":first-child"),O.attr("class","text"),h&&t.look!=="handDrawn"&&O.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&O.selectAll("path").attr("style",i),O.attr("transform",`translate(${l-l/4}, 0)`),o.attr("transform",`translate(${-s/2+(t.padding??0)/2-(n.x-(n.left??0))},${-c/2+(t.padding??0)/2-(n.y-(n.top??0))})`),X(t,O),t.intersect=function(P){return H.polygon(t,d,P)},a}f(Wd,"curlyBraces");async function qd(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=80,s=20,c=Math.max(o,(n.width+(t.padding??0)*2)*1.25,(t==null?void 0:t.width)??0),l=Math.max(s,n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=l/2,{cssStyles:u}=t,p=j.svg(a),d=Y(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=c,m=l,y=g-h,x=m/4,b=[{x:y,y:0},{x,y:0},{x:0,y:m/2},{x,y:m},{x:y,y:m},...yi(-y,-m/2,h,50,270,90)],k=lt(b),T=p.path(k,d),v=a.insert(()=>T,":first-child");return v.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&v.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&v.selectChildren("path").attr("style",i),v.attr("transform",`translate(${-c/2}, ${-l/2})`),X(t,v),t.intersect=function(C){return H.polygon(t,b,C)},a}f(qd,"curvedTrapezoid");var R2=f((e,t,r,i,a,n)=>[`M${e},${t+n}`,`a${a},${n} 0,0,0 ${r},0`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`].join(" "),"createCylinderPathD"),I2=f((e,t,r,i,a,n)=>[`M${e},${t+n}`,`M${e+r},${t+n}`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`].join(" "),"createOuterCylinderPathD"),P2=f((e,t,r,i,a,n)=>[`M${e-r/2},${-i/2}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createInnerCylinderPathD");async function Hd(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=Math.max(n.width+t.padding,t.width??0),c=s/2,l=c/(2.5+s/50),h=Math.max(n.height+l+t.padding,t.height??0);let u;const{cssStyles:p}=t;if(t.look==="handDrawn"){const d=j.svg(a),g=I2(0,0,s,h,c,l),m=P2(0,l,s,h,c,l),y=d.path(g,Y(t,{})),x=d.path(m,Y(t,{fill:"none"}));u=a.insert(()=>x,":first-child"),u=a.insert(()=>y,":first-child"),u.attr("class","basic label-container"),p&&u.attr("style",p)}else{const d=R2(0,0,s,h,c,l);u=a.insert("path",":first-child").attr("d",d).attr("class","basic label-container").attr("style",Jt(p)).attr("style",i)}return u.attr("label-offset-y",l),u.attr("transform",`translate(${-s/2}, ${-(h/2+l)})`),X(t,u),o.attr("transform",`translate(${-(n.width/2)-(n.x-(n.left??0))}, ${-(n.height/2)+(t.padding??0)/1.5-(n.y-(n.top??0))})`),t.intersect=function(d){const g=H.rect(t,d),m=g.x-(t.x??0);if(c!=0&&(Math.abs(m)<(t.width??0)/2||Math.abs(m)==(t.width??0)/2&&Math.abs(g.y-(t.y??0))>(t.height??0)/2-l)){let y=l*l*(1-m*m/(c*c));y>0&&(y=Math.sqrt(y)),y=l-y,d.y-(t.y??0)>0&&(y=-y),g.y+=y}return g},a}f(Hd,"cylinder");async function jd(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=n.width+t.padding,c=n.height+t.padding,l=c*.2,h=-s/2,u=-c/2-l/2,{cssStyles:p}=t,d=j.svg(a),g=Y(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=[{x:h,y:u+l},{x:-h,y:u+l},{x:-h,y:-u},{x:h,y:-u},{x:h,y:u},{x:-h,y:u},{x:-h,y:u+l}],y=d.polygon(m.map(b=>[b.x,b.y]),g),x=a.insert(()=>y,":first-child");return x.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",p),i&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),o.attr("transform",`translate(${h+(t.padding??0)/2-(n.x-(n.left??0))}, ${u+l+(t.padding??0)/2-(n.y-(n.top??0))})`),X(t,x),t.intersect=function(b){return H.rect(t,b)},a}f(jd,"dividedRectangle");async function Yd(e,t){var p,d;const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,halfPadding:o}=await rt(e,t,tt(t)),c=n.width/2+o+5,l=n.width/2+o;let h;const{cssStyles:u}=t;if(t.look==="handDrawn"){const g=j.svg(a),m=Y(t,{roughness:.2,strokeWidth:2.5}),y=Y(t,{roughness:.2,strokeWidth:1.5}),x=g.circle(0,0,c*2,m),b=g.circle(0,0,l*2,y);h=a.insert("g",":first-child"),h.attr("class",Jt(t.cssClasses)).attr("style",Jt(u)),(p=h.node())==null||p.appendChild(x),(d=h.node())==null||d.appendChild(b)}else{h=a.insert("g",":first-child");const g=h.insert("circle",":first-child"),m=h.insert("circle");h.attr("class","basic label-container").attr("style",i),g.attr("class","outer-circle").attr("style",i).attr("r",c).attr("cx",0).attr("cy",0),m.attr("class","inner-circle").attr("style",i).attr("r",l).attr("cx",0).attr("cy",0)}return X(t,h),t.intersect=function(g){return $.info("DoubleCircle intersect",t,c,g),H.circle(t,c,g)},a}f(Yd,"doublecircle");function Ud(e,t,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:a}=Z(t);t.label="",t.labelStyle=i;const n=e.insert("g").attr("class",tt(t)).attr("id",t.domId??t.id),o=7,{cssStyles:s}=t,c=j.svg(n),{nodeBorder:l}=r,h=Y(t,{fillStyle:"solid"});t.look!=="handDrawn"&&(h.roughness=0);const u=c.circle(0,0,o*2,h),p=n.insert(()=>u,":first-child");return p.selectAll("path").attr("style",`fill: ${l} !important;`),s&&s.length>0&&t.look!=="handDrawn"&&p.selectAll("path").attr("style",s),a&&t.look!=="handDrawn"&&p.selectAll("path").attr("style",a),X(t,p),t.intersect=function(d){return $.info("filledCircle intersect",t,{radius:o,point:d}),H.circle(t,o,d)},n}f(Ud,"filledCircle");async function Gd(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=n.width+(t.padding??0),c=s+n.height,l=s+n.height,h=[{x:0,y:-c},{x:l,y:-c},{x:l/2,y:0}],{cssStyles:u}=t,p=j.svg(a),d=Y(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=lt(h),m=p.path(g,d),y=a.insert(()=>m,":first-child").attr("transform",`translate(${-c/2}, ${c/2})`);return u&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",i),t.width=s,t.height=c,X(t,y),o.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))}, ${-c/2+(t.padding??0)/2+(n.y-(n.top??0))})`),t.intersect=function(x){return $.info("Triangle intersect",t,h,x),H.polygon(t,h,x)},a}f(Gd,"flippedTriangle");function Vd(e,t,{dir:r,config:{state:i,themeVariables:a}}){const{nodeStyles:n}=Z(t);t.label="";const o=e.insert("g").attr("class",tt(t)).attr("id",t.domId??t.id),{cssStyles:s}=t;let c=Math.max(70,(t==null?void 0:t.width)??0),l=Math.max(10,(t==null?void 0:t.height)??0);r==="LR"&&(c=Math.max(10,(t==null?void 0:t.width)??0),l=Math.max(70,(t==null?void 0:t.height)??0));const h=-1*c/2,u=-1*l/2,p=j.svg(o),d=Y(t,{stroke:a.lineColor,fill:a.lineColor});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=p.rectangle(h,u,c,l,d),m=o.insert(()=>g,":first-child");s&&t.look!=="handDrawn"&&m.selectAll("path").attr("style",s),n&&t.look!=="handDrawn"&&m.selectAll("path").attr("style",n),X(t,m);const y=(i==null?void 0:i.padding)??0;return t.width&&t.height&&(t.width+=y/2||0,t.height+=y/2||0),t.intersect=function(x){return H.rect(t,x)},o}f(Vd,"forkJoin");async function Xd(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const a=80,n=50,{shapeSvg:o,bbox:s}=await rt(e,t,tt(t)),c=Math.max(a,s.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(n,s.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=l/2,{cssStyles:u}=t,p=j.svg(o),d=Y(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=[{x:-c/2,y:-l/2},{x:c/2-h,y:-l/2},...yi(-c/2+h,0,h,50,90,270),{x:c/2-h,y:l/2},{x:-c/2,y:l/2}],m=lt(g),y=p.path(m,d),x=o.insert(()=>y,":first-child");return x.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),X(t,x),t.intersect=function(b){return $.info("Pill intersect",t,{radius:h,point:b}),H.polygon(t,g,b)},o}f(Xd,"halfRoundedRectangle");async function Zd(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=n.height+(t.padding??0),s=n.width+(t.padding??0)*2.5,{cssStyles:c}=t,l=j.svg(a),h=Y(t,{});t.look!=="handDrawn"&&(h.roughness=0,h.fillStyle="solid");let u=s/2;const p=u/6;u=u+p;const d=o/2,g=d/2,m=u-g,y=[{x:-m,y:-d},{x:0,y:-d},{x:m,y:-d},{x:u,y:0},{x:m,y:d},{x:0,y:d},{x:-m,y:d},{x:-u,y:0}],x=lt(y),b=l.path(x,h),k=a.insert(()=>b,":first-child");return k.attr("class","basic label-container"),c&&t.look!=="handDrawn"&&k.selectChildren("path").attr("style",c),i&&t.look!=="handDrawn"&&k.selectChildren("path").attr("style",i),t.width=s,t.height=o,X(t,k),t.intersect=function(T){return H.polygon(t,y,T)},a}f(Zd,"hexagon");async function Kd(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.label="",t.labelStyle=r;const{shapeSvg:a}=await rt(e,t,tt(t)),n=Math.max(30,(t==null?void 0:t.width)??0),o=Math.max(30,(t==null?void 0:t.height)??0),{cssStyles:s}=t,c=j.svg(a),l=Y(t,{});t.look!=="handDrawn"&&(l.roughness=0,l.fillStyle="solid");const h=[{x:0,y:0},{x:n,y:0},{x:0,y:o},{x:n,y:o}],u=lt(h),p=c.path(u,l),d=a.insert(()=>p,":first-child");return d.attr("class","basic label-container"),s&&t.look!=="handDrawn"&&d.selectChildren("path").attr("style",s),i&&t.look!=="handDrawn"&&d.selectChildren("path").attr("style",i),d.attr("transform",`translate(${-n/2}, ${-o/2})`),X(t,d),t.intersect=function(g){return $.info("Pill intersect",t,{points:h}),H.polygon(t,h,g)},a}f(Kd,"hourglass");async function Qd(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=Z(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,label:u}=await rt(e,t,"icon-shape default"),p=t.pos==="t",d=s,g=s,{nodeBorder:m}=r,{stylesMap:y}=$r(t),x=-g/2,b=-d/2,k=t.label?8:0,T=j.svg(l),v=Y(t,{stroke:"none",fill:"none"});t.look!=="handDrawn"&&(v.roughness=0,v.fillStyle="solid");const C=T.rectangle(x,b,g,d,v),S=Math.max(g,h.width),O=d+h.height+k,P=T.rectangle(-S/2,-O/2,S,O,{...v,fill:"transparent",stroke:"none"}),R=l.insert(()=>C,":first-child"),E=l.insert(()=>P);if(t.icon){const N=l.append("g");N.html(`<g>${await Oi(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const D=N.node().getBBox(),L=D.width,A=D.height,B=D.x,F=D.y;N.attr("transform",`translate(${-L/2-B},${p?h.height/2+k/2-A/2-F:-h.height/2-k/2-A/2-F})`),N.attr("style",`color: ${y.get("stroke")??m};`)}return u.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${p?-O/2:O/2-h.height})`),R.attr("transform",`translate(0,${p?h.height/2+k/2:-h.height/2-k/2})`),X(t,E),t.intersect=function(N){if($.info("iconSquare intersect",t,N),!t.label)return H.rect(t,N);const D=t.x??0,L=t.y??0,A=t.height??0;let B=[];return p?B=[{x:D-h.width/2,y:L-A/2},{x:D+h.width/2,y:L-A/2},{x:D+h.width/2,y:L-A/2+h.height+k},{x:D+g/2,y:L-A/2+h.height+k},{x:D+g/2,y:L+A/2},{x:D-g/2,y:L+A/2},{x:D-g/2,y:L-A/2+h.height+k},{x:D-h.width/2,y:L-A/2+h.height+k}]:B=[{x:D-g/2,y:L-A/2},{x:D+g/2,y:L-A/2},{x:D+g/2,y:L-A/2+d},{x:D+h.width/2,y:L-A/2+d},{x:D+h.width/2/2,y:L+A/2},{x:D-h.width/2,y:L+A/2},{x:D-h.width/2,y:L-A/2+d},{x:D-g/2,y:L-A/2+d}],H.polygon(t,B,N)},l}f(Qd,"icon");async function Jd(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=Z(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,label:u}=await rt(e,t,"icon-shape default"),p=20,d=t.label?8:0,g=t.pos==="t",{nodeBorder:m,mainBkg:y}=r,{stylesMap:x}=$r(t),b=j.svg(l),k=Y(t,{});t.look!=="handDrawn"&&(k.roughness=0,k.fillStyle="solid");const T=x.get("fill");k.stroke=T??y;const v=l.append("g");t.icon&&v.html(`<g>${await Oi(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const C=v.node().getBBox(),S=C.width,O=C.height,P=C.x,R=C.y,E=Math.max(S,O)*Math.SQRT2+p*2,N=b.circle(0,0,E,k),D=Math.max(E,h.width),L=E+h.height+d,A=b.rectangle(-D/2,-L/2,D,L,{...k,fill:"transparent",stroke:"none"}),B=l.insert(()=>N,":first-child"),F=l.insert(()=>A);return v.attr("transform",`translate(${-S/2-P},${g?h.height/2+d/2-O/2-R:-h.height/2-d/2-O/2-R})`),v.attr("style",`color: ${x.get("stroke")??m};`),u.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${g?-L/2:L/2-h.height})`),B.attr("transform",`translate(0,${g?h.height/2+d/2:-h.height/2-d/2})`),X(t,F),t.intersect=function(M){return $.info("iconSquare intersect",t,M),H.rect(t,M)},l}f(Jd,"iconCircle");async function tp(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=Z(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,halfPadding:u,label:p}=await rt(e,t,"icon-shape default"),d=t.pos==="t",g=s+u*2,m=s+u*2,{nodeBorder:y,mainBkg:x}=r,{stylesMap:b}=$r(t),k=-m/2,T=-g/2,v=t.label?8:0,C=j.svg(l),S=Y(t,{});t.look!=="handDrawn"&&(S.roughness=0,S.fillStyle="solid");const O=b.get("fill");S.stroke=O??x;const P=C.path(Ue(k,T,m,g,5),S),R=Math.max(m,h.width),E=g+h.height+v,N=C.rectangle(-R/2,-E/2,R,E,{...S,fill:"transparent",stroke:"none"}),D=l.insert(()=>P,":first-child").attr("class","icon-shape2"),L=l.insert(()=>N);if(t.icon){const A=l.append("g");A.html(`<g>${await Oi(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const B=A.node().getBBox(),F=B.width,M=B.height,W=B.x,V=B.y;A.attr("transform",`translate(${-F/2-W},${d?h.height/2+v/2-M/2-V:-h.height/2-v/2-M/2-V})`),A.attr("style",`color: ${b.get("stroke")??y};`)}return p.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${d?-E/2:E/2-h.height})`),D.attr("transform",`translate(0,${d?h.height/2+v/2:-h.height/2-v/2})`),X(t,L),t.intersect=function(A){if($.info("iconSquare intersect",t,A),!t.label)return H.rect(t,A);const B=t.x??0,F=t.y??0,M=t.height??0;let W=[];return d?W=[{x:B-h.width/2,y:F-M/2},{x:B+h.width/2,y:F-M/2},{x:B+h.width/2,y:F-M/2+h.height+v},{x:B+m/2,y:F-M/2+h.height+v},{x:B+m/2,y:F+M/2},{x:B-m/2,y:F+M/2},{x:B-m/2,y:F-M/2+h.height+v},{x:B-h.width/2,y:F-M/2+h.height+v}]:W=[{x:B-m/2,y:F-M/2},{x:B+m/2,y:F-M/2},{x:B+m/2,y:F-M/2+g},{x:B+h.width/2,y:F-M/2+g},{x:B+h.width/2/2,y:F+M/2},{x:B-h.width/2,y:F+M/2},{x:B-h.width/2,y:F-M/2+g},{x:B-m/2,y:F-M/2+g}],H.polygon(t,W,A)},l}f(tp,"iconRounded");async function ep(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:a}=Z(t);t.labelStyle=a;const n=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(n,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,halfPadding:u,label:p}=await rt(e,t,"icon-shape default"),d=t.pos==="t",g=s+u*2,m=s+u*2,{nodeBorder:y,mainBkg:x}=r,{stylesMap:b}=$r(t),k=-m/2,T=-g/2,v=t.label?8:0,C=j.svg(l),S=Y(t,{});t.look!=="handDrawn"&&(S.roughness=0,S.fillStyle="solid");const O=b.get("fill");S.stroke=O??x;const P=C.path(Ue(k,T,m,g,.1),S),R=Math.max(m,h.width),E=g+h.height+v,N=C.rectangle(-R/2,-E/2,R,E,{...S,fill:"transparent",stroke:"none"}),D=l.insert(()=>P,":first-child"),L=l.insert(()=>N);if(t.icon){const A=l.append("g");A.html(`<g>${await Oi(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const B=A.node().getBBox(),F=B.width,M=B.height,W=B.x,V=B.y;A.attr("transform",`translate(${-F/2-W},${d?h.height/2+v/2-M/2-V:-h.height/2-v/2-M/2-V})`),A.attr("style",`color: ${b.get("stroke")??y};`)}return p.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${d?-E/2:E/2-h.height})`),D.attr("transform",`translate(0,${d?h.height/2+v/2:-h.height/2-v/2})`),X(t,L),t.intersect=function(A){if($.info("iconSquare intersect",t,A),!t.label)return H.rect(t,A);const B=t.x??0,F=t.y??0,M=t.height??0;let W=[];return d?W=[{x:B-h.width/2,y:F-M/2},{x:B+h.width/2,y:F-M/2},{x:B+h.width/2,y:F-M/2+h.height+v},{x:B+m/2,y:F-M/2+h.height+v},{x:B+m/2,y:F+M/2},{x:B-m/2,y:F+M/2},{x:B-m/2,y:F-M/2+h.height+v},{x:B-h.width/2,y:F-M/2+h.height+v}]:W=[{x:B-m/2,y:F-M/2},{x:B+m/2,y:F-M/2},{x:B+m/2,y:F-M/2+g},{x:B+h.width/2,y:F-M/2+g},{x:B+h.width/2/2,y:F+M/2},{x:B-h.width/2,y:F+M/2},{x:B-h.width/2,y:F-M/2+g},{x:B-m/2,y:F-M/2+g}],H.polygon(t,W,A)},l}f(ep,"iconSquare");async function rp(e,t,{config:{flowchart:r}}){const i=new Image;i.src=(t==null?void 0:t.img)??"",await i.decode();const a=Number(i.naturalWidth.toString().replace("px","")),n=Number(i.naturalHeight.toString().replace("px",""));t.imageAspectRatio=a/n;const{labelStyles:o}=Z(t);t.labelStyle=o;const s=r==null?void 0:r.wrappingWidth;t.defaultWidth=r==null?void 0:r.wrappingWidth;const c=Math.max(t.label?s??0:0,(t==null?void 0:t.assetWidth)??a),l=t.constraint==="on"&&t!=null&&t.assetHeight?t.assetHeight*t.imageAspectRatio:c,h=t.constraint==="on"?l/t.imageAspectRatio:(t==null?void 0:t.assetHeight)??n;t.width=Math.max(l,s??0);const{shapeSvg:u,bbox:p,label:d}=await rt(e,t,"image-shape default"),g=t.pos==="t",m=-l/2,y=-h/2,x=t.label?8:0,b=j.svg(u),k=Y(t,{});t.look!=="handDrawn"&&(k.roughness=0,k.fillStyle="solid");const T=b.rectangle(m,y,l,h,k),v=Math.max(l,p.width),C=h+p.height+x,S=b.rectangle(-v/2,-C/2,v,C,{...k,fill:"none",stroke:"none"}),O=u.insert(()=>T,":first-child"),P=u.insert(()=>S);if(t.img){const R=u.append("image");R.attr("href",t.img),R.attr("width",l),R.attr("height",h),R.attr("preserveAspectRatio","none"),R.attr("transform",`translate(${-l/2},${g?C/2-h:-C/2})`)}return d.attr("transform",`translate(${-p.width/2-(p.x-(p.left??0))},${g?-h/2-p.height/2-x/2:h/2-p.height/2+x/2})`),O.attr("transform",`translate(0,${g?p.height/2+x/2:-p.height/2-x/2})`),X(t,P),t.intersect=function(R){if($.info("iconSquare intersect",t,R),!t.label)return H.rect(t,R);const E=t.x??0,N=t.y??0,D=t.height??0;let L=[];return g?L=[{x:E-p.width/2,y:N-D/2},{x:E+p.width/2,y:N-D/2},{x:E+p.width/2,y:N-D/2+p.height+x},{x:E+l/2,y:N-D/2+p.height+x},{x:E+l/2,y:N+D/2},{x:E-l/2,y:N+D/2},{x:E-l/2,y:N-D/2+p.height+x},{x:E-p.width/2,y:N-D/2+p.height+x}]:L=[{x:E-l/2,y:N-D/2},{x:E+l/2,y:N-D/2},{x:E+l/2,y:N-D/2+h},{x:E+p.width/2,y:N-D/2+h},{x:E+p.width/2/2,y:N+D/2},{x:E-p.width/2,y:N+D/2},{x:E-p.width/2,y:N-D/2+h},{x:E-l/2,y:N-D/2+h}],H.polygon(t,L,R)},u}f(rp,"imageSquare");async function ip(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),s=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=[{x:0,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:-3*s/6,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=j.svg(a),p=Y(t,{}),d=lt(c),g=u.path(d,p);l=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=Ge(a,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,X(t,l),t.intersect=function(u){return H.polygon(t,c,u)},a}f(ip,"inv_trapezoid");async function tn(e,t,r){const{labelStyles:i,nodeStyles:a}=Z(t);t.labelStyle=i;const{shapeSvg:n,bbox:o}=await rt(e,t,tt(t)),s=Math.max(o.width+r.labelPaddingX*2,(t==null?void 0:t.width)||0),c=Math.max(o.height+r.labelPaddingY*2,(t==null?void 0:t.height)||0),l=-s/2,h=-c/2;let u,{rx:p,ry:d}=t;const{cssStyles:g}=t;if(r!=null&&r.rx&&r.ry&&(p=r.rx,d=r.ry),t.look==="handDrawn"){const m=j.svg(n),y=Y(t,{}),x=p||d?m.path(Ue(l,h,s,c,p||0),y):m.rectangle(l,h,s,c,y);u=n.insert(()=>x,":first-child"),u.attr("class","basic label-container").attr("style",Jt(g))}else u=n.insert("rect",":first-child"),u.attr("class","basic label-container").attr("style",a).attr("rx",Jt(p)).attr("ry",Jt(d)).attr("x",l).attr("y",h).attr("width",s).attr("height",c);return X(t,u),t.intersect=function(m){return H.rect(t,m)},n}f(tn,"drawRect");async function ap(e,t){const{shapeSvg:r,bbox:i,label:a}=await rt(e,t,"label"),n=r.insert("rect",":first-child");return n.attr("width",.1).attr("height",.1),r.attr("class","label edgeLabel"),a.attr("transform",`translate(${-(i.width/2)-(i.x-(i.left??0))}, ${-(i.height/2)-(i.y-(i.top??0))})`),X(t,n),t.intersect=function(c){return H.rect(t,c)},r}f(ap,"labelRect");async function np(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=Math.max(n.width+(t.padding??0),(t==null?void 0:t.width)??0),s=Math.max(n.height+(t.padding??0),(t==null?void 0:t.height)??0),c=[{x:0,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:-(3*s)/6,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=j.svg(a),p=Y(t,{}),d=lt(c),g=u.path(d,p);l=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=Ge(a,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,X(t,l),t.intersect=function(u){return H.polygon(t,c,u)},a}f(np,"lean_left");async function sp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=Math.max(n.width+(t.padding??0),(t==null?void 0:t.width)??0),s=Math.max(n.height+(t.padding??0),(t==null?void 0:t.height)??0),c=[{x:-3*s/6,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:0,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=j.svg(a),p=Y(t,{}),d=lt(c),g=u.path(d,p);l=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=Ge(a,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,X(t,l),t.intersect=function(u){return H.polygon(t,c,u)},a}f(sp,"lean_right");function op(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.label="",t.labelStyle=r;const a=e.insert("g").attr("class",tt(t)).attr("id",t.domId??t.id),{cssStyles:n}=t,o=Math.max(35,(t==null?void 0:t.width)??0),s=Math.max(35,(t==null?void 0:t.height)??0),c=7,l=[{x:o,y:0},{x:0,y:s+c/2},{x:o-2*c,y:s+c/2},{x:0,y:2*s},{x:o,y:s-c/2},{x:2*c,y:s-c/2}],h=j.svg(a),u=Y(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");const p=lt(l),d=h.path(p,u),g=a.insert(()=>d,":first-child");return n&&t.look!=="handDrawn"&&g.selectAll("path").attr("style",n),i&&t.look!=="handDrawn"&&g.selectAll("path").attr("style",i),g.attr("transform",`translate(-${o/2},${-s})`),X(t,g),t.intersect=function(m){return $.info("lightningBolt intersect",t,m),H.polygon(t,l,m)},a}f(op,"lightningBolt");var N2=f((e,t,r,i,a,n,o)=>[`M${e},${t+n}`,`a${a},${n} 0,0,0 ${r},0`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`,`M${e},${t+n+o}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createCylinderPathD"),z2=f((e,t,r,i,a,n,o)=>[`M${e},${t+n}`,`M${e+r},${t+n}`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`,`M${e},${t+n+o}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createOuterCylinderPathD"),W2=f((e,t,r,i,a,n)=>[`M${e-r/2},${-i/2}`,`a${a},${n} 0,0,0 ${r},0`].join(" "),"createInnerCylinderPathD");async function lp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=Math.max(n.width+(t.padding??0),t.width??0),c=s/2,l=c/(2.5+s/50),h=Math.max(n.height+l+(t.padding??0),t.height??0),u=h*.1;let p;const{cssStyles:d}=t;if(t.look==="handDrawn"){const g=j.svg(a),m=z2(0,0,s,h,c,l,u),y=W2(0,l,s,h,c,l),x=Y(t,{}),b=g.path(m,x),k=g.path(y,x);a.insert(()=>k,":first-child").attr("class","line"),p=a.insert(()=>b,":first-child"),p.attr("class","basic label-container"),d&&p.attr("style",d)}else{const g=N2(0,0,s,h,c,l,u);p=a.insert("path",":first-child").attr("d",g).attr("class","basic label-container").attr("style",Jt(d)).attr("style",i)}return p.attr("label-offset-y",l),p.attr("transform",`translate(${-s/2}, ${-(h/2+l)})`),X(t,p),o.attr("transform",`translate(${-(n.width/2)-(n.x-(n.left??0))}, ${-(n.height/2)+l-(n.y-(n.top??0))})`),t.intersect=function(g){const m=H.rect(t,g),y=m.x-(t.x??0);if(c!=0&&(Math.abs(y)<(t.width??0)/2||Math.abs(y)==(t.width??0)/2&&Math.abs(m.y-(t.y??0))>(t.height??0)/2-l)){let x=l*l*(1-y*y/(c*c));x>0&&(x=Math.sqrt(x)),x=l-x,g.y-(t.y??0)>0&&(x=-x),m.y+=x}return m},a}f(lp,"linedCylinder");async function cp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/4,h=c+l,{cssStyles:u}=t,p=j.svg(a),d=Y(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=[{x:-s/2-s/2*.1,y:-h/2},{x:-s/2-s/2*.1,y:h/2},...He(-s/2-s/2*.1,h/2,s/2+s/2*.1,h/2,l,.8),{x:s/2+s/2*.1,y:-h/2},{x:-s/2-s/2*.1,y:-h/2},{x:-s/2,y:-h/2},{x:-s/2,y:h/2*1.1},{x:-s/2,y:-h/2}],m=p.polygon(g.map(x=>[x.x,x.y]),d),y=a.insert(()=>m,":first-child");return y.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",u),i&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",i),y.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)+s/2*.1/2-(n.x-(n.left??0))},${-c/2+(t.padding??0)-l/2-(n.y-(n.top??0))})`),X(t,y),t.intersect=function(x){return H.polygon(t,g,x)},a}f(cp,"linedWaveEdgedRect");async function hp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=5,h=-s/2,u=-c/2,{cssStyles:p}=t,d=j.svg(a),g=Y(t,{}),m=[{x:h-l,y:u+l},{x:h-l,y:u+c+l},{x:h+s-l,y:u+c+l},{x:h+s-l,y:u+c},{x:h+s,y:u+c},{x:h+s,y:u+c-l},{x:h+s+l,y:u+c-l},{x:h+s+l,y:u-l},{x:h+l,y:u-l},{x:h+l,y:u},{x:h,y:u},{x:h,y:u+l}],y=[{x:h,y:u+l},{x:h+s-l,y:u+l},{x:h+s-l,y:u+c},{x:h+s,y:u+c},{x:h+s,y:u},{x:h,y:u}];t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const x=lt(m),b=d.path(x,g),k=lt(y),T=d.path(k,{...g,fill:"none"}),v=a.insert(()=>T,":first-child");return v.insert(()=>b,":first-child"),v.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",p),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),o.attr("transform",`translate(${-(n.width/2)-l-(n.x-(n.left??0))}, ${-(n.height/2)+l-(n.y-(n.top??0))})`),X(t,v),t.intersect=function(C){return H.polygon(t,m,C)},a}f(hp,"multiRect");async function up(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/4,h=c+l,u=-s/2,p=-h/2,d=5,{cssStyles:g}=t,m=He(u-d,p+h+d,u+s-d,p+h+d,l,.8),y=m==null?void 0:m[m.length-1],x=[{x:u-d,y:p+d},{x:u-d,y:p+h+d},...m,{x:u+s-d,y:y.y-d},{x:u+s,y:y.y-d},{x:u+s,y:y.y-2*d},{x:u+s+d,y:y.y-2*d},{x:u+s+d,y:p-d},{x:u+d,y:p-d},{x:u+d,y:p},{x:u,y:p},{x:u,y:p+d}],b=[{x:u,y:p+d},{x:u+s-d,y:p+d},{x:u+s-d,y:y.y-d},{x:u+s,y:y.y-d},{x:u+s,y:p},{x:u,y:p}],k=j.svg(a),T=Y(t,{});t.look!=="handDrawn"&&(T.roughness=0,T.fillStyle="solid");const v=lt(x),C=k.path(v,T),S=lt(b),O=k.path(S,T),P=a.insert(()=>C,":first-child");return P.insert(()=>O),P.attr("class","basic label-container"),g&&t.look!=="handDrawn"&&P.selectAll("path").attr("style",g),i&&t.look!=="handDrawn"&&P.selectAll("path").attr("style",i),P.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-(n.width/2)-d-(n.x-(n.left??0))}, ${-(n.height/2)+d-l/2-(n.y-(n.top??0))})`),X(t,P),t.intersect=function(R){return H.polygon(t,x,R)},a}f(up,"multiWaveEdgedRectangle");async function dp(e,t,{config:{themeVariables:r}}){var b;const{labelStyles:i,nodeStyles:a}=Z(t);t.labelStyle=i,t.useHtmlLabels||((b=Nt().flowchart)==null?void 0:b.htmlLabels)!==!1||(t.centerLabel=!0);const{shapeSvg:o,bbox:s,label:c}=await rt(e,t,tt(t)),l=Math.max(s.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),h=Math.max(s.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),u=-l/2,p=-h/2,{cssStyles:d}=t,g=j.svg(o),m=Y(t,{fill:r.noteBkgColor,stroke:r.noteBorderColor});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const y=g.rectangle(u,p,l,h,m),x=o.insert(()=>y,":first-child");return x.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",d),a&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",a),c.attr("transform",`translate(${-s.width/2-(s.x-(s.left??0))}, ${-(s.height/2)-(s.y-(s.top??0))})`),X(t,x),t.intersect=function(k){return H.rect(t,k)},o}f(dp,"note");var q2=f((e,t,r)=>[`M${e+r/2},${t}`,`L${e+r},${t-r/2}`,`L${e+r/2},${t-r}`,`L${e},${t-r/2}`,"Z"].join(" "),"createDecisionBoxPathD");async function pp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=n.width+t.padding,s=n.height+t.padding,c=o+s,l=.5,h=[{x:c/2,y:0},{x:c,y:-c/2},{x:c/2,y:-c},{x:0,y:-c/2}];let u;const{cssStyles:p}=t;if(t.look==="handDrawn"){const d=j.svg(a),g=Y(t,{}),m=q2(0,0,c),y=d.path(m,g);u=a.insert(()=>y,":first-child").attr("transform",`translate(${-c/2+l}, ${c/2})`),p&&u.attr("style",p)}else u=Ge(a,c,c,h),u.attr("transform",`translate(${-c/2+l}, ${c/2})`);return i&&u.attr("style",i),X(t,u),t.intersect=function(d){return $.debug(`APA12 Intersect called SPLIT
point:`,d,`
node:
`,t,`
res:`,H.polygon(t,h,d)),H.polygon(t,h,d)},a}f(pp,"question");async function fp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=Math.max(n.width+(t.padding??0),(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0),(t==null?void 0:t.height)??0),l=-s/2,h=-c/2,u=h/2,p=[{x:l+u,y:h},{x:l,y:0},{x:l+u,y:-h},{x:-l,y:-h},{x:-l,y:h}],{cssStyles:d}=t,g=j.svg(a),m=Y(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const y=lt(p),x=g.path(y,m),b=a.insert(()=>x,":first-child");return b.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",d),i&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",i),b.attr("transform",`translate(${-u/2},0)`),o.attr("transform",`translate(${-u/2-n.width/2-(n.x-(n.left??0))}, ${-(n.height/2)-(n.y-(n.top??0))})`),X(t,b),t.intersect=function(k){return H.polygon(t,p,k)},a}f(fp,"rect_left_inv_arrow");async function gp(e,t){var O,P;const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;let a;t.cssClasses?a="node "+t.cssClasses:a="node default";const n=e.insert("g").attr("class",a).attr("id",t.domId||t.id),o=n.insert("g"),s=n.insert("g").attr("class","label").attr("style",i),c=t.description,l=t.label,h=s.node().appendChild(await tr(l,t.labelStyle,!0,!0));let u={width:0,height:0};if(At((P=(O=ut())==null?void 0:O.flowchart)==null?void 0:P.htmlLabels)){const R=h.children[0],E=ht(h);u=R.getBoundingClientRect(),E.attr("width",u.width),E.attr("height",u.height)}$.info("Text 2",c);const p=c||[],d=h.getBBox(),g=s.node().appendChild(await tr(p.join?p.join("<br/>"):p,t.labelStyle,!0,!0)),m=g.children[0],y=ht(g);u=m.getBoundingClientRect(),y.attr("width",u.width),y.attr("height",u.height);const x=(t.padding||0)/2;ht(g).attr("transform","translate( "+(u.width>d.width?0:(d.width-u.width)/2)+", "+(d.height+x+5)+")"),ht(h).attr("transform","translate( "+(u.width<d.width?0:-(d.width-u.width)/2)+", 0)"),u=s.node().getBBox(),s.attr("transform","translate("+-u.width/2+", "+(-u.height/2-x+3)+")");const b=u.width+(t.padding||0),k=u.height+(t.padding||0),T=-u.width/2-x,v=-u.height/2-x;let C,S;if(t.look==="handDrawn"){const R=j.svg(n),E=Y(t,{}),N=R.path(Ue(T,v,b,k,t.rx||0),E),D=R.line(-u.width/2-x,-u.height/2-x+d.height+x,u.width/2+x,-u.height/2-x+d.height+x,E);S=n.insert(()=>($.debug("Rough node insert CXC",N),D),":first-child"),C=n.insert(()=>($.debug("Rough node insert CXC",N),N),":first-child")}else C=o.insert("rect",":first-child"),S=o.insert("line"),C.attr("class","outer title-state").attr("style",i).attr("x",-u.width/2-x).attr("y",-u.height/2-x).attr("width",u.width+(t.padding||0)).attr("height",u.height+(t.padding||0)),S.attr("class","divider").attr("x1",-u.width/2-x).attr("x2",u.width/2+x).attr("y1",-u.height/2-x+d.height+x).attr("y2",-u.height/2-x+d.height+x);return X(t,C),t.intersect=function(R){return H.rect(t,R)},n}f(gp,"rectWithTitle");function ei(e,t,r,i,a,n,o){const c=(e+r)/2,l=(t+i)/2,h=Math.atan2(i-t,r-e),u=(r-e)/2,p=(i-t)/2,d=u/a,g=p/n,m=Math.sqrt(d**2+g**2);if(m>1)throw new Error("The given radii are too small to create an arc between the points.");const y=Math.sqrt(1-m**2),x=c+y*n*Math.sin(h)*(o?-1:1),b=l-y*a*Math.cos(h)*(o?-1:1),k=Math.atan2((t-b)/n,(e-x)/a);let v=Math.atan2((i-b)/n,(r-x)/a)-k;o&&v<0&&(v+=2*Math.PI),!o&&v>0&&(v-=2*Math.PI);const C=[];for(let S=0;S<20;S++){const O=S/19,P=k+O*v,R=x+a*Math.cos(P),E=b+n*Math.sin(P);C.push({x:R,y:E})}return C}f(ei,"generateArcPoints");async function mp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=(t==null?void 0:t.padding)??0,s=(t==null?void 0:t.padding)??0,c=(t!=null&&t.width?t==null?void 0:t.width:n.width)+o*2,l=(t!=null&&t.height?t==null?void 0:t.height:n.height)+s*2,h=5,u=5,{cssStyles:p}=t,d=j.svg(a),g=Y(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=[{x:-c/2+u,y:-l/2},{x:c/2-u,y:-l/2},...ei(c/2-u,-l/2,c/2,-l/2+u,h,h,!0),{x:c/2,y:-l/2+u},{x:c/2,y:l/2-u},...ei(c/2,l/2-u,c/2-u,l/2,h,h,!0),{x:c/2-u,y:l/2},{x:-c/2+u,y:l/2},...ei(-c/2+u,l/2,-c/2,l/2-u,h,h,!0),{x:-c/2,y:l/2-u},{x:-c/2,y:-l/2+u},...ei(-c/2,-l/2+u,-c/2+u,-l/2,h,h,!0)],y=lt(m),x=d.path(y,g),b=a.insert(()=>x,":first-child");return b.attr("class","basic label-container outer-path"),p&&t.look!=="handDrawn"&&b.selectChildren("path").attr("style",p),i&&t.look!=="handDrawn"&&b.selectChildren("path").attr("style",i),X(t,b),t.intersect=function(k){return H.polygon(t,m,k)},a}f(mp,"roundedRect");async function yp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=(t==null?void 0:t.padding)??0,c=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=-n.width/2-s,u=-n.height/2-s,{cssStyles:p}=t,d=j.svg(a),g=Y(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=[{x:h,y:u},{x:h+c+8,y:u},{x:h+c+8,y:u+l},{x:h-8,y:u+l},{x:h-8,y:u},{x:h,y:u},{x:h,y:u+l}],y=d.polygon(m.map(b=>[b.x,b.y]),g),x=a.insert(()=>y,":first-child");return x.attr("class","basic label-container").attr("style",Jt(p)),i&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),p&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),o.attr("transform",`translate(${-c/2+4+(t.padding??0)-(n.x-(n.left??0))},${-l/2+(t.padding??0)-(n.y-(n.top??0))})`),X(t,x),t.intersect=function(b){return H.rect(t,b)},a}f(yp,"shadedProcess");async function xp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=-s/2,h=-c/2,{cssStyles:u}=t,p=j.svg(a),d=Y(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=[{x:l,y:h},{x:l,y:h+c},{x:l+s,y:h+c},{x:l+s,y:h-c/2}],m=lt(g),y=p.path(m,d),x=a.insert(()=>y,":first-child");return x.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),x.attr("transform",`translate(0, ${c/4})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(n.x-(n.left??0))}, ${-c/4+(t.padding??0)-(n.y-(n.top??0))})`),X(t,x),t.intersect=function(b){return H.polygon(t,g,b)},a}f(xp,"slopedRect");async function bp(e,t){const r={rx:0,ry:0,classes:"",labelPaddingX:((t==null?void 0:t.padding)||0)*2,labelPaddingY:((t==null?void 0:t.padding)||0)*1};return tn(e,t,r)}f(bp,"squareRect");async function Cp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=n.height+t.padding,s=n.width+o/4+t.padding,c=o/2,{cssStyles:l}=t,h=j.svg(a),u=Y(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");const p=[{x:-s/2+c,y:-o/2},{x:s/2-c,y:-o/2},...yi(-s/2+c,0,c,50,90,270),{x:s/2-c,y:o/2},...yi(s/2-c,0,c,50,270,450)],d=lt(p),g=h.path(d,u),m=a.insert(()=>g,":first-child");return m.attr("class","basic label-container outer-path"),l&&t.look!=="handDrawn"&&m.selectChildren("path").attr("style",l),i&&t.look!=="handDrawn"&&m.selectChildren("path").attr("style",i),X(t,m),t.intersect=function(y){return H.polygon(t,p,y)},a}f(Cp,"stadium");async function kp(e,t){return tn(e,t,{rx:5,ry:5,classes:"flowchart-node"})}f(kp,"state");function wp(e,t,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:a}=Z(t);t.labelStyle=i;const{cssStyles:n}=t,{lineColor:o,stateBorder:s,nodeBorder:c}=r,l=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),h=j.svg(l),u=Y(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");const p=h.circle(0,0,14,{...u,stroke:o,strokeWidth:2}),d=s??c,g=h.circle(0,0,5,{...u,fill:d,stroke:d,strokeWidth:2,fillStyle:"solid"}),m=l.insert(()=>p,":first-child");return m.insert(()=>g),n&&m.selectAll("path").attr("style",n),a&&m.selectAll("path").attr("style",a),X(t,m),t.intersect=function(y){return H.circle(t,7,y)},l}f(wp,"stateEnd");function vp(e,t,{config:{themeVariables:r}}){const{lineColor:i}=r,a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id);let n;if(t.look==="handDrawn"){const s=j.svg(a).circle(0,0,14,Vy(i));n=a.insert(()=>s),n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14)}else n=a.insert("circle",":first-child"),n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14);return X(t,n),t.intersect=function(o){return H.circle(t,7,o)},a}f(vp,"stateStart");async function Sp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=((t==null?void 0:t.padding)||0)/2,s=n.width+t.padding,c=n.height+t.padding,l=-n.width/2-o,h=-n.height/2-o,u=[{x:0,y:0},{x:s,y:0},{x:s,y:-c},{x:0,y:-c},{x:0,y:0},{x:-8,y:0},{x:s+8,y:0},{x:s+8,y:-c},{x:-8,y:-c},{x:-8,y:0}];if(t.look==="handDrawn"){const p=j.svg(a),d=Y(t,{}),g=p.rectangle(l-8,h,s+16,c,d),m=p.line(l,h,l,h+c,d),y=p.line(l+s,h,l+s,h+c,d);a.insert(()=>m,":first-child"),a.insert(()=>y,":first-child");const x=a.insert(()=>g,":first-child"),{cssStyles:b}=t;x.attr("class","basic label-container").attr("style",Jt(b)),X(t,x)}else{const p=Ge(a,s,c,u);i&&p.attr("style",i),X(t,p)}return t.intersect=function(p){return H.polygon(t,u,p)},a}f(Sp,"subroutine");async function Tp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),s=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=-o/2,l=-s/2,h=.2*s,u=.2*s,{cssStyles:p}=t,d=j.svg(a),g=Y(t,{}),m=[{x:c-h/2,y:l},{x:c+o+h/2,y:l},{x:c+o+h/2,y:l+s},{x:c-h/2,y:l+s}],y=[{x:c+o-h/2,y:l+s},{x:c+o+h/2,y:l+s},{x:c+o+h/2,y:l+s-u}];t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const x=lt(m),b=d.path(x,g),k=lt(y),T=d.path(k,{...g,fillStyle:"solid"}),v=a.insert(()=>T,":first-child");return v.insert(()=>b,":first-child"),v.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",p),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),X(t,v),t.intersect=function(C){return H.polygon(t,m,C)},a}f(Tp,"taggedRect");async function _p(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/4,h=.2*s,u=.2*c,p=c+l,{cssStyles:d}=t,g=j.svg(a),m=Y(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const y=[{x:-s/2-s/2*.1,y:p/2},...He(-s/2-s/2*.1,p/2,s/2+s/2*.1,p/2,l,.8),{x:s/2+s/2*.1,y:-p/2},{x:-s/2-s/2*.1,y:-p/2}],x=-s/2+s/2*.1,b=-p/2-u*.4,k=[{x:x+s-h,y:(b+c)*1.4},{x:x+s,y:b+c-u},{x:x+s,y:(b+c)*.9},...He(x+s,(b+c)*1.3,x+s-h,(b+c)*1.5,-c*.03,.5)],T=lt(y),v=g.path(T,m),C=lt(k),S=g.path(C,{...m,fillStyle:"solid"}),O=a.insert(()=>S,":first-child");return O.insert(()=>v,":first-child"),O.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&O.selectAll("path").attr("style",d),i&&t.look!=="handDrawn"&&O.selectAll("path").attr("style",i),O.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(n.x-(n.left??0))},${-c/2+(t.padding??0)-l/2-(n.y-(n.top??0))})`),X(t,O),t.intersect=function(P){return H.polygon(t,y,P)},a}f(_p,"taggedWaveEdgedRectangle");async function Bp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=Math.max(n.width+t.padding,(t==null?void 0:t.width)||0),s=Math.max(n.height+t.padding,(t==null?void 0:t.height)||0),c=-o/2,l=-s/2,h=a.insert("rect",":first-child");return h.attr("class","text").attr("style",i).attr("rx",0).attr("ry",0).attr("x",c).attr("y",l).attr("width",o).attr("height",s),X(t,h),t.intersect=function(u){return H.rect(t,u)},a}f(Bp,"text");var H2=f((e,t,r,i,a,n)=>`M${e},${t}
    a${a},${n} 0,0,1 0,${-i}
    l${r},0
    a${a},${n} 0,0,1 0,${i}
    M${r},${-i}
    a${a},${n} 0,0,0 0,${i}
    l${-r},0`,"createCylinderPathD"),j2=f((e,t,r,i,a,n)=>[`M${e},${t}`,`M${e+r},${t}`,`a${a},${n} 0,0,0 0,${-i}`,`l${-r},0`,`a${a},${n} 0,0,0 0,${i}`,`l${r},0`].join(" "),"createOuterCylinderPathD"),Y2=f((e,t,r,i,a,n)=>[`M${e+r/2},${-i/2}`,`a${a},${n} 0,0,0 0,${i}`].join(" "),"createInnerCylinderPathD");async function Lp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o,halfPadding:s}=await rt(e,t,tt(t)),c=t.look==="neo"?s*2:s,l=n.height+c,h=l/2,u=h/(2.5+l/50),p=n.width+u+c,{cssStyles:d}=t;let g;if(t.look==="handDrawn"){const m=j.svg(a),y=j2(0,0,p,l,u,h),x=Y2(0,0,p,l,u,h),b=m.path(y,Y(t,{})),k=m.path(x,Y(t,{fill:"none"}));g=a.insert(()=>k,":first-child"),g=a.insert(()=>b,":first-child"),g.attr("class","basic label-container"),d&&g.attr("style",d)}else{const m=H2(0,0,p,l,u,h);g=a.insert("path",":first-child").attr("d",m).attr("class","basic label-container").attr("style",Jt(d)).attr("style",i),g.attr("class","basic label-container"),d&&g.selectAll("path").attr("style",d),i&&g.selectAll("path").attr("style",i)}return g.attr("label-offset-x",u),g.attr("transform",`translate(${-p/2}, ${l/2} )`),o.attr("transform",`translate(${-(n.width/2)-u-(n.x-(n.left??0))}, ${-(n.height/2)-(n.y-(n.top??0))})`),X(t,g),t.intersect=function(m){const y=H.rect(t,m),x=y.y-(t.y??0);if(h!=0&&(Math.abs(x)<(t.height??0)/2||Math.abs(x)==(t.height??0)/2&&Math.abs(y.x-(t.x??0))>(t.width??0)/2-u)){let b=u*u*(1-x*x/(h*h));b!=0&&(b=Math.sqrt(Math.abs(b))),b=u-b,m.x-(t.x??0)>0&&(b=-b),y.x+=b}return y},a}f(Lp,"tiltedCylinder");async function Ap(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=n.width+t.padding,s=n.height+t.padding,c=[{x:-3*s/6,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:0,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=j.svg(a),p=Y(t,{}),d=lt(c),g=u.path(d,p);l=a.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=Ge(a,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,X(t,l),t.intersect=function(u){return H.polygon(t,c,u)},a}f(Ap,"trapezoid");async function Mp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=60,s=20,c=Math.max(o,n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(s,n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),{cssStyles:h}=t,u=j.svg(a),p=Y(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");const d=[{x:-c/2*.8,y:-l/2},{x:c/2*.8,y:-l/2},{x:c/2,y:-l/2*.6},{x:c/2,y:l/2},{x:-c/2,y:l/2},{x:-c/2,y:-l/2*.6}],g=lt(d),m=u.path(g,p),y=a.insert(()=>m,":first-child");return y.attr("class","basic label-container"),h&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",h),i&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",i),X(t,y),t.intersect=function(x){return H.polygon(t,d,x)},a}f(Mp,"trapezoidalPentagon");async function Ep(e,t){var b;const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=At((b=ut().flowchart)==null?void 0:b.htmlLabels),c=n.width+(t.padding??0),l=c+n.height,h=c+n.height,u=[{x:0,y:0},{x:h,y:0},{x:h/2,y:-l}],{cssStyles:p}=t,d=j.svg(a),g=Y(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=lt(u),y=d.path(m,g),x=a.insert(()=>y,":first-child").attr("transform",`translate(${-l/2}, ${l/2})`);return p&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",p),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),t.width=c,t.height=l,X(t,x),o.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))}, ${l/2-(n.height+(t.padding??0)/(s?2:1)-(n.y-(n.top??0)))})`),t.intersect=function(k){return $.info("Triangle intersect",t,u,k),H.polygon(t,u,k)},a}f(Ep,"triangle");async function Fp(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/8,h=c+l,{cssStyles:u}=t,d=70-s,g=d>0?d/2:0,m=j.svg(a),y=Y(t,{});t.look!=="handDrawn"&&(y.roughness=0,y.fillStyle="solid");const x=[{x:-s/2-g,y:h/2},...He(-s/2-g,h/2,s/2+g,h/2,l,.8),{x:s/2+g,y:-h/2},{x:-s/2-g,y:-h/2}],b=lt(x),k=m.path(b,y),T=a.insert(()=>k,":first-child");return T.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&T.selectAll("path").attr("style",u),i&&t.look!=="handDrawn"&&T.selectAll("path").attr("style",i),T.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(n.x-(n.left??0))},${-c/2+(t.padding??0)-l-(n.y-(n.top??0))})`),X(t,T),t.intersect=function(v){return H.polygon(t,x,v)},a}f(Fp,"waveEdgedRectangle");async function $p(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n}=await rt(e,t,tt(t)),o=100,s=50,c=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=c/l;let u=c,p=l;u>p*h?p=u/h:u=p*h,u=Math.max(u,o),p=Math.max(p,s);const d=Math.min(p*.2,p/4),g=p+d*2,{cssStyles:m}=t,y=j.svg(a),x=Y(t,{});t.look!=="handDrawn"&&(x.roughness=0,x.fillStyle="solid");const b=[{x:-u/2,y:g/2},...He(-u/2,g/2,u/2,g/2,d,1),{x:u/2,y:-g/2},...He(u/2,-g/2,-u/2,-g/2,d,-1)],k=lt(b),T=y.path(k,x),v=a.insert(()=>T,":first-child");return v.attr("class","basic label-container"),m&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",m),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),X(t,v),t.intersect=function(C){return H.polygon(t,b,C)},a}f($p,"waveRectangle");async function Op(e,t){const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await rt(e,t,tt(t)),s=Math.max(n.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(n.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=5,h=-s/2,u=-c/2,{cssStyles:p}=t,d=j.svg(a),g=Y(t,{}),m=[{x:h-l,y:u-l},{x:h-l,y:u+c},{x:h+s,y:u+c},{x:h+s,y:u-l}],y=`M${h-l},${u-l} L${h+s},${u-l} L${h+s},${u+c} L${h-l},${u+c} L${h-l},${u-l}
                M${h-l},${u} L${h+s},${u}
                M${h},${u-l} L${h},${u+c}`;t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const x=d.path(y,g),b=a.insert(()=>x,":first-child");return b.attr("transform",`translate(${l/2}, ${l/2})`),b.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",p),i&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",i),o.attr("transform",`translate(${-(n.width/2)+l/2-(n.x-(n.left??0))}, ${-(n.height/2)+l/2-(n.y-(n.top??0))})`),X(t,b),t.intersect=function(k){return H.polygon(t,m,k)},a}f(Op,"windowPane");async function uo(e,t){var ft,nt,vt,st;const r=t;if(r.alias&&(t.label=r.alias),t.look==="handDrawn"){const{themeVariables:at}=Nt(),{background:ct}=at,wt={...t,id:t.id+"-background",look:"default",cssStyles:["stroke: none",`fill: ${ct}`]};await uo(e,wt)}const i=Nt();t.useHtmlLabels=i.htmlLabels;let a=((ft=i.er)==null?void 0:ft.diagramPadding)??10,n=((nt=i.er)==null?void 0:nt.entityPadding)??6;const{cssStyles:o}=t,{labelStyles:s,nodeStyles:c}=Z(t);if(r.attributes.length===0&&t.label){const at={rx:0,ry:0,labelPaddingX:a,labelPaddingY:a*1.5,classes:""};Re(t.label,i)+at.labelPaddingX*2<i.er.minEntityWidth&&(t.width=i.er.minEntityWidth);const ct=await tn(e,t,at);if(!At(i.htmlLabels)){const wt=ct.select("text"),yt=(vt=wt.node())==null?void 0:vt.getBBox();wt.attr("transform",`translate(${-yt.width/2}, 0)`)}return ct}i.htmlLabels||(a*=1.25,n*=1.25);let l=tt(t);l||(l="node default");const h=e.insert("g").attr("class",l).attr("id",t.domId||t.id),u=await br(h,t.label??"",i,0,0,["name"],s);u.height+=n;let p=0;const d=[],g=[];let m=0,y=0,x=0,b=0,k=!0,T=!0;for(const at of r.attributes){const ct=await br(h,at.type,i,0,p,["attribute-type"],s);m=Math.max(m,ct.width+a);const wt=await br(h,at.name,i,0,p,["attribute-name"],s);y=Math.max(y,wt.width+a);const yt=await br(h,at.keys.join(),i,0,p,["attribute-keys"],s);x=Math.max(x,yt.width+a);const xt=await br(h,at.comment,i,0,p,["attribute-comment"],s);b=Math.max(b,xt.width+a);const Tt=Math.max(ct.height,wt.height,yt.height,xt.height)+n;g.push({yOffset:p,rowHeight:Tt}),p+=Tt}let v=4;x<=a&&(k=!1,x=0,v--),b<=a&&(T=!1,b=0,v--);const C=h.node().getBBox();if(u.width+a*2-(m+y+x+b)>0){const at=u.width+a*2-(m+y+x+b);m+=at/v,y+=at/v,x>0&&(x+=at/v),b>0&&(b+=at/v)}const S=m+y+x+b,O=j.svg(h),P=Y(t,{});t.look!=="handDrawn"&&(P.roughness=0,P.fillStyle="solid");let R=0;g.length>0&&(R=g.reduce((at,ct)=>at+((ct==null?void 0:ct.rowHeight)??0),0));const E=Math.max(C.width+a*2,(t==null?void 0:t.width)||0,S),N=Math.max((R??0)+u.height,(t==null?void 0:t.height)||0),D=-E/2,L=-N/2;h.selectAll("g:not(:first-child)").each((at,ct,wt)=>{const yt=ht(wt[ct]),xt=yt.attr("transform");let Tt=0,qt=0;if(xt){const oe=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(xt);oe&&(Tt=parseFloat(oe[1]),qt=parseFloat(oe[2]),yt.attr("class").includes("attribute-name")?Tt+=m:yt.attr("class").includes("attribute-keys")?Tt+=m+y:yt.attr("class").includes("attribute-comment")&&(Tt+=m+y+x))}yt.attr("transform",`translate(${D+a/2+Tt}, ${qt+L+u.height+n/2})`)}),h.select(".name").attr("transform","translate("+-u.width/2+", "+(L+n/2)+")");const A=O.rectangle(D,L,E,N,P),B=h.insert(()=>A,":first-child").attr("style",o.join("")),{themeVariables:F}=Nt(),{rowEven:M,rowOdd:W,nodeBorder:V}=F;d.push(0);for(const[at,ct]of g.entries()){const yt=(at+1)%2===0&&ct.yOffset!==0,xt=O.rectangle(D,u.height+L+(ct==null?void 0:ct.yOffset),E,ct==null?void 0:ct.rowHeight,{...P,fill:yt?M:W,stroke:V});h.insert(()=>xt,"g.label").attr("style",o.join("")).attr("class",`row-rect-${yt?"even":"odd"}`)}let U=O.line(D,u.height+L,E+D,u.height+L,P);h.insert(()=>U).attr("class","divider"),U=O.line(m+D,u.height+L,m+D,N+L,P),h.insert(()=>U).attr("class","divider"),k&&(U=O.line(m+y+D,u.height+L,m+y+D,N+L,P),h.insert(()=>U).attr("class","divider")),T&&(U=O.line(m+y+x+D,u.height+L,m+y+x+D,N+L,P),h.insert(()=>U).attr("class","divider"));for(const at of d)U=O.line(D,u.height+L+at,E+D,u.height+L+at,P),h.insert(()=>U).attr("class","divider");if(X(t,B),c&&t.look!=="handDrawn"){const at=c.split(";"),ct=(st=at==null?void 0:at.filter(wt=>wt.includes("stroke")))==null?void 0:st.map(wt=>`${wt}`).join("; ");h.selectAll("path").attr("style",ct??""),h.selectAll(".row-rect-even path").attr("style",c)}return t.intersect=function(at){return H.rect(t,at)},h}f(uo,"erBox");async function br(e,t,r,i=0,a=0,n=[],o=""){const s=e.insert("g").attr("class",`label ${n.join(" ")}`).attr("transform",`translate(${i}, ${a})`).attr("style",o);t!==Vo(t)&&(t=Vo(t),t=t.replaceAll("<","&lt;").replaceAll(">","&gt;"));const c=s.node().appendChild(await Ye(s,t,{width:Re(t,r)+100,style:o,useHtmlLabels:r.htmlLabels},r));if(t.includes("&lt;")||t.includes("&gt;")){let h=c.children[0];for(h.textContent=h.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">");h.childNodes[0];)h=h.childNodes[0],h.textContent=h.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">")}let l=c.getBBox();if(At(r.htmlLabels)){const h=c.children[0];h.style.textAlign="start";const u=ht(c);l=h.getBoundingClientRect(),u.attr("width",l.width),u.attr("height",l.height)}return l}f(br,"addText");async function Dp(e,t,r,i,a=r.class.padding??12){const n=i?0:3,o=e.insert("g").attr("class",tt(t)).attr("id",t.domId||t.id);let s=null,c=null,l=null,h=null,u=0,p=0,d=0;if(s=o.insert("g").attr("class","annotation-group text"),t.annotations.length>0){const b=t.annotations[0];await ri(s,{text:`«${b}»`},0),u=s.node().getBBox().height}c=o.insert("g").attr("class","label-group text"),await ri(c,t,0,["font-weight: bolder"]);const g=c.node().getBBox();p=g.height,l=o.insert("g").attr("class","members-group text");let m=0;for(const b of t.members){const k=await ri(l,b,m,[b.parseClassifier()]);m+=k+n}d=l.node().getBBox().height,d<=0&&(d=a/2),h=o.insert("g").attr("class","methods-group text");let y=0;for(const b of t.methods){const k=await ri(h,b,y,[b.parseClassifier()]);y+=k+n}let x=o.node().getBBox();if(s!==null){const b=s.node().getBBox();s.attr("transform",`translate(${-b.width/2})`)}return c.attr("transform",`translate(${-g.width/2}, ${u})`),x=o.node().getBBox(),l.attr("transform",`translate(0, ${u+p+a*2})`),x=o.node().getBBox(),h.attr("transform",`translate(0, ${u+p+(d?d+a*4:a*2)})`),x=o.node().getBBox(),{shapeSvg:o,bbox:x}}f(Dp,"textHelper");async function ri(e,t,r,i=[]){const a=e.insert("g").attr("class","label").attr("style",i.join("; ")),n=Nt();let o="useHtmlLabels"in t?t.useHtmlLabels:At(n.htmlLabels)??!0,s="";"text"in t?s=t.text:s=t.label,!o&&s.startsWith("\\")&&(s=s.substring(1)),_r(s)&&(o=!0);const c=await Ye(a,Es(cr(s)),{width:Re(s,n)+50,classes:"markdown-node-label",useHtmlLabels:o},n);let l,h=1;if(o){const u=c.children[0],p=ht(c);h=u.innerHTML.split("<br>").length,u.innerHTML.includes("</math>")&&(h+=u.innerHTML.split("<mrow>").length-1);const d=u.getElementsByTagName("img");if(d){const g=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...d].map(m=>new Promise(y=>{function x(){var b;if(m.style.display="flex",m.style.flexDirection="column",g){const k=((b=n.fontSize)==null?void 0:b.toString())??window.getComputedStyle(document.body).fontSize,v=parseInt(k,10)*5+"px";m.style.minWidth=v,m.style.maxWidth=v}else m.style.width="100%";y(m)}f(x,"setupImage"),setTimeout(()=>{m.complete&&x()}),m.addEventListener("error",x),m.addEventListener("load",x)})))}l=u.getBoundingClientRect(),p.attr("width",l.width),p.attr("height",l.height)}else{i.includes("font-weight: bolder")&&ht(c).selectAll("tspan").attr("font-weight",""),h=c.children.length;const u=c.children[0];(c.textContent===""||c.textContent.includes("&gt"))&&(u.textContent=s[0]+s.substring(1).replaceAll("&gt;",">").replaceAll("&lt;","<").trim(),s[1]===" "&&(u.textContent=u.textContent[0]+" "+u.textContent.substring(1))),u.textContent==="undefined"&&(u.textContent=""),l=c.getBBox()}return a.attr("transform","translate(0,"+(-l.height/(2*h)+r)+")"),l.height}f(ri,"addText");async function Rp(e,t){var P,R;const r=ut(),i=r.class.padding??12,a=i,n=t.useHtmlLabels??At(r.htmlLabels)??!0,o=t;o.annotations=o.annotations??[],o.members=o.members??[],o.methods=o.methods??[];const{shapeSvg:s,bbox:c}=await Dp(e,t,r,n,a),{labelStyles:l,nodeStyles:h}=Z(t);t.labelStyle=l,t.cssStyles=o.styles||"";const u=((P=o.styles)==null?void 0:P.join(";"))||h||"";t.cssStyles||(t.cssStyles=u.replaceAll("!important","").split(";"));const p=o.members.length===0&&o.methods.length===0&&!((R=r.class)!=null&&R.hideEmptyMembersBox),d=j.svg(s),g=Y(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=c.width;let y=c.height;o.members.length===0&&o.methods.length===0?y+=a:o.members.length>0&&o.methods.length===0&&(y+=a*2);const x=-m/2,b=-y/2,k=d.rectangle(x-i,b-i-(p?i:o.members.length===0&&o.methods.length===0?-i/2:0),m+2*i,y+2*i+(p?i*2:o.members.length===0&&o.methods.length===0?-i:0),g),T=s.insert(()=>k,":first-child");T.attr("class","basic label-container");const v=T.node().getBBox();s.selectAll(".text").each((E,N,D)=>{var W;const L=ht(D[N]),A=L.attr("transform");let B=0;if(A){const U=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(A);U&&(B=parseFloat(U[2]))}let F=B+b+i-(p?i:o.members.length===0&&o.methods.length===0?-i/2:0);n||(F-=4);let M=x;(L.attr("class").includes("label-group")||L.attr("class").includes("annotation-group"))&&(M=-((W=L.node())==null?void 0:W.getBBox().width)/2||0,s.selectAll("text").each(function(V,U,ft){window.getComputedStyle(ft[U]).textAnchor==="middle"&&(M=0)})),L.attr("transform",`translate(${M}, ${F})`)});const C=s.select(".annotation-group").node().getBBox().height-(p?i/2:0)||0,S=s.select(".label-group").node().getBBox().height-(p?i/2:0)||0,O=s.select(".members-group").node().getBBox().height-(p?i/2:0)||0;if(o.members.length>0||o.methods.length>0||p){const E=d.line(v.x,C+S+b+i,v.x+v.width,C+S+b+i,g);s.insert(()=>E).attr("class","divider").attr("style",u)}if(p||o.members.length>0||o.methods.length>0){const E=d.line(v.x,C+S+O+b+a*2+i,v.x+v.width,C+S+O+b+i+a*2,g);s.insert(()=>E).attr("class","divider").attr("style",u)}if(o.look!=="handDrawn"&&s.selectAll("path").attr("style",u),T.select(":nth-child(2)").attr("style",u),s.selectAll(".divider").select("path").attr("style",u),t.labelStyle?s.selectAll("span").attr("style",t.labelStyle):s.selectAll("span").attr("style",u),!n){const E=RegExp(/color\s*:\s*([^;]*)/),N=E.exec(u);if(N){const D=N[0].replace("color","fill");s.selectAll("tspan").attr("style",D)}else if(l){const D=E.exec(l);if(D){const L=D[0].replace("color","fill");s.selectAll("tspan").attr("style",L)}}}return X(t,T),t.intersect=function(E){return H.rect(t,E)},s}f(Rp,"classBox");async function Ip(e,t){var C,S;const{labelStyles:r,nodeStyles:i}=Z(t);t.labelStyle=r;const a=t,n=t,o=20,s=20,c="verifyMethod"in t,l=tt(t),h=e.insert("g").attr("class",l).attr("id",t.domId??t.id);let u;c?u=await ye(h,`&lt;&lt;${a.type}&gt;&gt;`,0,t.labelStyle):u=await ye(h,"&lt;&lt;Element&gt;&gt;",0,t.labelStyle);let p=u;const d=await ye(h,a.name,p,t.labelStyle+"; font-weight: bold;");if(p+=d+s,c){const O=await ye(h,`${a.requirementId?`ID: ${a.requirementId}`:""}`,p,t.labelStyle);p+=O;const P=await ye(h,`${a.text?`Text: ${a.text}`:""}`,p,t.labelStyle);p+=P;const R=await ye(h,`${a.risk?`Risk: ${a.risk}`:""}`,p,t.labelStyle);p+=R,await ye(h,`${a.verifyMethod?`Verification: ${a.verifyMethod}`:""}`,p,t.labelStyle)}else{const O=await ye(h,`${n.type?`Type: ${n.type}`:""}`,p,t.labelStyle);p+=O,await ye(h,`${n.docRef?`Doc Ref: ${n.docRef}`:""}`,p,t.labelStyle)}const g=(((C=h.node())==null?void 0:C.getBBox().width)??200)+o,m=(((S=h.node())==null?void 0:S.getBBox().height)??200)+o,y=-g/2,x=-m/2,b=j.svg(h),k=Y(t,{});t.look!=="handDrawn"&&(k.roughness=0,k.fillStyle="solid");const T=b.rectangle(y,x,g,m,k),v=h.insert(()=>T,":first-child");if(v.attr("class","basic label-container").attr("style",i),h.selectAll(".label").each((O,P,R)=>{const E=ht(R[P]),N=E.attr("transform");let D=0,L=0;if(N){const M=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(N);M&&(D=parseFloat(M[1]),L=parseFloat(M[2]))}const A=L-m/2;let B=y+o/2;(P===0||P===1)&&(B=D),E.attr("transform",`translate(${B}, ${A+o})`)}),p>u+d+s){const O=b.line(y,x+u+d+s,y+g,x+u+d+s,k);h.insert(()=>O).attr("style",i)}return X(t,v),t.intersect=function(O){return H.rect(t,O)},h}f(Ip,"requirementBox");async function ye(e,t,r,i=""){if(t==="")return 0;const a=e.insert("g").attr("class","label").attr("style",i),n=ut(),o=n.htmlLabels??!0,s=await Ye(a,Es(cr(t)),{width:Re(t,n)+50,classes:"markdown-node-label",useHtmlLabels:o,style:i},n);let c;if(o){const l=s.children[0],h=ht(s);c=l.getBoundingClientRect(),h.attr("width",c.width),h.attr("height",c.height)}else{const l=s.children[0];for(const h of l.children)h.textContent=h.textContent.replaceAll("&gt;",">").replaceAll("&lt;","<"),i&&h.setAttribute("style",i);c=s.getBBox(),c.height+=6}return a.attr("transform",`translate(${-c.width/2},${-c.height/2+r})`),c.height}f(ye,"addText");var U2=f(e=>{switch(e){case"Very High":return"red";case"High":return"orange";case"Medium":return null;case"Low":return"blue";case"Very Low":return"lightblue"}},"colorFromPriority");async function Pp(e,t,{config:r}){var N,D;const{labelStyles:i,nodeStyles:a}=Z(t);t.labelStyle=i||"";const n=10,o=t.width;t.width=(t.width??200)-10;const{shapeSvg:s,bbox:c,label:l}=await rt(e,t,tt(t)),h=t.padding||10;let u="",p;"ticket"in t&&t.ticket&&((N=r==null?void 0:r.kanban)!=null&&N.ticketBaseUrl)&&(u=(D=r==null?void 0:r.kanban)==null?void 0:D.ticketBaseUrl.replace("#TICKET#",t.ticket),p=s.insert("svg:a",":first-child").attr("class","kanban-ticket-link").attr("xlink:href",u).attr("target","_blank"));const d={useHtmlLabels:t.useHtmlLabels,labelStyle:t.labelStyle||"",width:t.width,img:t.img,padding:t.padding||8,centerLabel:!1};let g,m;p?{label:g,bbox:m}=await An(p,"ticket"in t&&t.ticket||"",d):{label:g,bbox:m}=await An(s,"ticket"in t&&t.ticket||"",d);const{label:y,bbox:x}=await An(s,"assigned"in t&&t.assigned||"",d);t.width=o;const b=10,k=(t==null?void 0:t.width)||0,T=Math.max(m.height,x.height)/2,v=Math.max(c.height+b*2,(t==null?void 0:t.height)||0)+T,C=-k/2,S=-v/2;l.attr("transform","translate("+(h-k/2)+", "+(-T-c.height/2)+")"),g.attr("transform","translate("+(h-k/2)+", "+(-T+c.height/2)+")"),y.attr("transform","translate("+(h+k/2-x.width-2*n)+", "+(-T+c.height/2)+")");let O;const{rx:P,ry:R}=t,{cssStyles:E}=t;if(t.look==="handDrawn"){const L=j.svg(s),A=Y(t,{}),B=P||R?L.path(Ue(C,S,k,v,P||0),A):L.rectangle(C,S,k,v,A);O=s.insert(()=>B,":first-child"),O.attr("class","basic label-container").attr("style",E||null)}else{O=s.insert("rect",":first-child"),O.attr("class","basic label-container __APA__").attr("style",a).attr("rx",P??5).attr("ry",R??5).attr("x",C).attr("y",S).attr("width",k).attr("height",v);const L="priority"in t&&t.priority;if(L){const A=s.append("line"),B=C+2,F=S+Math.floor((P??0)/2),M=S+v-Math.floor((P??0)/2);A.attr("x1",B).attr("y1",F).attr("x2",B).attr("y2",M).attr("stroke-width","4").attr("stroke",U2(L))}}return X(t,O),t.height=v,t.intersect=function(L){return H.rect(t,L)},s}f(Pp,"kanbanItem");var G2=[{semanticName:"Process",name:"Rectangle",shortName:"rect",description:"Standard process shape",aliases:["proc","process","rectangle"],internalAliases:["squareRect"],handler:bp},{semanticName:"Event",name:"Rounded Rectangle",shortName:"rounded",description:"Represents an event",aliases:["event"],internalAliases:["roundedRect"],handler:mp},{semanticName:"Terminal Point",name:"Stadium",shortName:"stadium",description:"Terminal point",aliases:["terminal","pill"],handler:Cp},{semanticName:"Subprocess",name:"Framed Rectangle",shortName:"fr-rect",description:"Subprocess",aliases:["subprocess","subproc","framed-rectangle","subroutine"],handler:Sp},{semanticName:"Database",name:"Cylinder",shortName:"cyl",description:"Database storage",aliases:["db","database","cylinder"],handler:Hd},{semanticName:"Start",name:"Circle",shortName:"circle",description:"Starting point",aliases:["circ"],handler:Rd},{semanticName:"Decision",name:"Diamond",shortName:"diam",description:"Decision-making step",aliases:["decision","diamond","question"],handler:pp},{semanticName:"Prepare Conditional",name:"Hexagon",shortName:"hex",description:"Preparation or condition step",aliases:["hexagon","prepare"],handler:Zd},{semanticName:"Data Input/Output",name:"Lean Right",shortName:"lean-r",description:"Represents input or output",aliases:["lean-right","in-out"],internalAliases:["lean_right"],handler:sp},{semanticName:"Data Input/Output",name:"Lean Left",shortName:"lean-l",description:"Represents output or input",aliases:["lean-left","out-in"],internalAliases:["lean_left"],handler:np},{semanticName:"Priority Action",name:"Trapezoid Base Bottom",shortName:"trap-b",description:"Priority action",aliases:["priority","trapezoid-bottom","trapezoid"],handler:Ap},{semanticName:"Manual Operation",name:"Trapezoid Base Top",shortName:"trap-t",description:"Represents a manual task",aliases:["manual","trapezoid-top","inv-trapezoid"],internalAliases:["inv_trapezoid"],handler:ip},{semanticName:"Stop",name:"Double Circle",shortName:"dbl-circ",description:"Represents a stop point",aliases:["double-circle"],internalAliases:["doublecircle"],handler:Yd},{semanticName:"Text Block",name:"Text Block",shortName:"text",description:"Text block",handler:Bp},{semanticName:"Card",name:"Notched Rectangle",shortName:"notch-rect",description:"Represents a card",aliases:["card","notched-rectangle"],handler:Od},{semanticName:"Lined/Shaded Process",name:"Lined Rectangle",shortName:"lin-rect",description:"Lined process shape",aliases:["lined-rectangle","lined-process","lin-proc","shaded-process"],handler:yp},{semanticName:"Start",name:"Small Circle",shortName:"sm-circ",description:"Small starting point",aliases:["start","small-circle"],internalAliases:["stateStart"],handler:vp},{semanticName:"Stop",name:"Framed Circle",shortName:"fr-circ",description:"Stop point",aliases:["stop","framed-circle"],internalAliases:["stateEnd"],handler:wp},{semanticName:"Fork/Join",name:"Filled Rectangle",shortName:"fork",description:"Fork or join in process flow",aliases:["join"],internalAliases:["forkJoin"],handler:Vd},{semanticName:"Collate",name:"Hourglass",shortName:"hourglass",description:"Represents a collate operation",aliases:["hourglass","collate"],handler:Kd},{semanticName:"Comment",name:"Curly Brace",shortName:"brace",description:"Adds a comment",aliases:["comment","brace-l"],handler:Nd},{semanticName:"Comment Right",name:"Curly Brace",shortName:"brace-r",description:"Adds a comment",handler:zd},{semanticName:"Comment with braces on both sides",name:"Curly Braces",shortName:"braces",description:"Adds a comment",handler:Wd},{semanticName:"Com Link",name:"Lightning Bolt",shortName:"bolt",description:"Communication link",aliases:["com-link","lightning-bolt"],handler:op},{semanticName:"Document",name:"Document",shortName:"doc",description:"Represents a document",aliases:["doc","document"],handler:Fp},{semanticName:"Delay",name:"Half-Rounded Rectangle",shortName:"delay",description:"Represents a delay",aliases:["half-rounded-rectangle"],handler:Xd},{semanticName:"Direct Access Storage",name:"Horizontal Cylinder",shortName:"h-cyl",description:"Direct access storage",aliases:["das","horizontal-cylinder"],handler:Lp},{semanticName:"Disk Storage",name:"Lined Cylinder",shortName:"lin-cyl",description:"Disk storage",aliases:["disk","lined-cylinder"],handler:lp},{semanticName:"Display",name:"Curved Trapezoid",shortName:"curv-trap",description:"Represents a display",aliases:["curved-trapezoid","display"],handler:qd},{semanticName:"Divided Process",name:"Divided Rectangle",shortName:"div-rect",description:"Divided process shape",aliases:["div-proc","divided-rectangle","divided-process"],handler:jd},{semanticName:"Extract",name:"Triangle",shortName:"tri",description:"Extraction process",aliases:["extract","triangle"],handler:Ep},{semanticName:"Internal Storage",name:"Window Pane",shortName:"win-pane",description:"Internal storage",aliases:["internal-storage","window-pane"],handler:Op},{semanticName:"Junction",name:"Filled Circle",shortName:"f-circ",description:"Junction point",aliases:["junction","filled-circle"],handler:Ud},{semanticName:"Loop Limit",name:"Trapezoidal Pentagon",shortName:"notch-pent",description:"Loop limit step",aliases:["loop-limit","notched-pentagon"],handler:Mp},{semanticName:"Manual File",name:"Flipped Triangle",shortName:"flip-tri",description:"Manual file operation",aliases:["manual-file","flipped-triangle"],handler:Gd},{semanticName:"Manual Input",name:"Sloped Rectangle",shortName:"sl-rect",description:"Manual input step",aliases:["manual-input","sloped-rectangle"],handler:xp},{semanticName:"Multi-Document",name:"Stacked Document",shortName:"docs",description:"Multiple documents",aliases:["documents","st-doc","stacked-document"],handler:up},{semanticName:"Multi-Process",name:"Stacked Rectangle",shortName:"st-rect",description:"Multiple processes",aliases:["procs","processes","stacked-rectangle"],handler:hp},{semanticName:"Stored Data",name:"Bow Tie Rectangle",shortName:"bow-rect",description:"Stored data",aliases:["stored-data","bow-tie-rectangle"],handler:$d},{semanticName:"Summary",name:"Crossed Circle",shortName:"cross-circ",description:"Summary",aliases:["summary","crossed-circle"],handler:Pd},{semanticName:"Tagged Document",name:"Tagged Document",shortName:"tag-doc",description:"Tagged document",aliases:["tag-doc","tagged-document"],handler:_p},{semanticName:"Tagged Process",name:"Tagged Rectangle",shortName:"tag-rect",description:"Tagged process",aliases:["tagged-rectangle","tag-proc","tagged-process"],handler:Tp},{semanticName:"Paper Tape",name:"Flag",shortName:"flag",description:"Paper tape",aliases:["paper-tape"],handler:$p},{semanticName:"Odd",name:"Odd",shortName:"odd",description:"Odd shape",internalAliases:["rect_left_inv_arrow"],handler:fp},{semanticName:"Lined Document",name:"Lined Document",shortName:"lin-doc",description:"Lined document",aliases:["lined-document"],handler:cp}],V2=f(()=>{const t=[...Object.entries({state:kp,choice:Dd,note:dp,rectWithTitle:gp,labelRect:ap,iconSquare:ep,iconCircle:Jd,icon:Qd,iconRounded:tp,imageSquare:rp,anchor:Fd,kanbanItem:Pp,classBox:Rp,erBox:uo,requirementBox:Ip}),...G2.flatMap(r=>[r.shortName,..."aliases"in r?r.aliases:[],..."internalAliases"in r?r.internalAliases:[]].map(a=>[a,r.handler]))];return Object.fromEntries(t)},"generateShapeMap"),Np=V2();function X2(e){return e in Np}f(X2,"isValidShape");var en=new Map;async function zp(e,t,r){let i,a;t.shape==="rect"&&(t.rx&&t.ry?t.shape="roundedRect":t.shape="squareRect");const n=t.shape?Np[t.shape]:void 0;if(!n)throw new Error(`No such shape: ${t.shape}. Please check your syntax.`);if(t.link){let o;r.config.securityLevel==="sandbox"?o="_top":t.linkTarget&&(o=t.linkTarget||"_blank"),i=e.insert("svg:a").attr("xlink:href",t.link).attr("target",o??null),a=await n(i,t,r)}else a=await n(e,t,r),i=a;return t.tooltip&&a.attr("title",t.tooltip),en.set(t.id,i),t.haveCallback&&i.attr("class",i.attr("class")+" clickable"),i}f(zp,"insertNode");var JS=f((e,t)=>{en.set(t.id,e)},"setNodeElem"),tT=f(()=>{en.clear()},"clear"),eT=f(e=>{const t=en.get(e.id);$.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");const r=8,i=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+i-e.width/2)+", "+(e.y-e.height/2-r)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),i},"positionNode"),Z2=f((e,t,r,i,a,n)=>{t.arrowTypeStart&&Ol(e,"start",t.arrowTypeStart,r,i,a,n),t.arrowTypeEnd&&Ol(e,"end",t.arrowTypeEnd,r,i,a,n)},"addEdgeMarkers"),K2={arrow_cross:{type:"cross",fill:!1},arrow_point:{type:"point",fill:!0},arrow_barb:{type:"barb",fill:!0},arrow_circle:{type:"circle",fill:!1},aggregation:{type:"aggregation",fill:!1},extension:{type:"extension",fill:!1},composition:{type:"composition",fill:!0},dependency:{type:"dependency",fill:!0},lollipop:{type:"lollipop",fill:!1},only_one:{type:"onlyOne",fill:!1},zero_or_one:{type:"zeroOrOne",fill:!1},one_or_more:{type:"oneOrMore",fill:!1},zero_or_more:{type:"zeroOrMore",fill:!1},requirement_arrow:{type:"requirement_arrow",fill:!1},requirement_contains:{type:"requirement_contains",fill:!1}},Ol=f((e,t,r,i,a,n,o)=>{var u;const s=K2[r];if(!s){$.warn(`Unknown arrow type: ${r}`);return}const c=s.type,h=`${a}_${n}-${c}${t==="start"?"Start":"End"}`;if(o&&o.trim()!==""){const p=o.replace(/[^\dA-Za-z]/g,"_"),d=`${h}_${p}`;if(!document.getElementById(d)){const g=document.getElementById(h);if(g){const m=g.cloneNode(!0);m.id=d,m.querySelectorAll("path, circle, line").forEach(x=>{x.setAttribute("stroke",o),s.fill&&x.setAttribute("fill",o)}),(u=g.parentNode)==null||u.appendChild(m)}}e.attr(`marker-${t}`,`url(${i}#${d})`)}else e.attr(`marker-${t}`,`url(${i}#${h})`)},"addEdgeMarker"),$a=new Map,$t=new Map,rT=f(()=>{$a.clear(),$t.clear()},"clear"),Kr=f(e=>e?e.reduce((r,i)=>r+";"+i,""):"","getLabelStyles"),Q2=f(async(e,t)=>{let r=At(ut().flowchart.htmlLabels);const i=await Ye(e,t.label,{style:Kr(t.labelStyle),useHtmlLabels:r,addSvgBackground:!0,isNode:!1});$.info("abc82",t,t.labelType);const a=e.insert("g").attr("class","edgeLabel"),n=a.insert("g").attr("class","label");n.node().appendChild(i);let o=i.getBBox();if(r){const c=i.children[0],l=ht(i);o=c.getBoundingClientRect(),l.attr("width",o.width),l.attr("height",o.height)}n.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"),$a.set(t.id,a),t.width=o.width,t.height=o.height;let s;if(t.startLabelLeft){const c=await tr(t.startLabelLeft,Kr(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),$t.get(t.id)||$t.set(t.id,{}),$t.get(t.id).startLeft=l,ii(s,t.startLabelLeft)}if(t.startLabelRight){const c=await tr(t.startLabelRight,Kr(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=l.node().appendChild(c),h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),$t.get(t.id)||$t.set(t.id,{}),$t.get(t.id).startRight=l,ii(s,t.startLabelRight)}if(t.endLabelLeft){const c=await tr(t.endLabelLeft,Kr(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),l.node().appendChild(c),$t.get(t.id)||$t.set(t.id,{}),$t.get(t.id).endLeft=l,ii(s,t.endLabelLeft)}if(t.endLabelRight){const c=await tr(t.endLabelRight,Kr(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),l.node().appendChild(c),$t.get(t.id)||$t.set(t.id,{}),$t.get(t.id).endRight=l,ii(s,t.endLabelRight)}return i},"insertEdgeLabel");function ii(e,t){ut().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}f(ii,"setTerminalWidth");var J2=f((e,t)=>{$.debug("Moving label abc88 ",e.id,e.label,$a.get(e.id),t);let r=t.updatedPath?t.updatedPath:t.originalPath;const i=ut(),{subGraphTitleTotalMargin:a}=Ns(i);if(e.label){const n=$a.get(e.id);let o=e.x,s=e.y;if(r){const c=ce.calcLabelPosition(r);$.debug("Moving label "+e.label+" from (",o,",",s,") to (",c.x,",",c.y,") abc88"),t.updatedPath&&(o=c.x,s=c.y)}n.attr("transform",`translate(${o}, ${s+a/2})`)}if(e.startLabelLeft){const n=$t.get(e.id).startLeft;let o=e.x,s=e.y;if(r){const c=ce.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",r);o=c.x,s=c.y}n.attr("transform",`translate(${o}, ${s})`)}if(e.startLabelRight){const n=$t.get(e.id).startRight;let o=e.x,s=e.y;if(r){const c=ce.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",r);o=c.x,s=c.y}n.attr("transform",`translate(${o}, ${s})`)}if(e.endLabelLeft){const n=$t.get(e.id).endLeft;let o=e.x,s=e.y;if(r){const c=ce.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",r);o=c.x,s=c.y}n.attr("transform",`translate(${o}, ${s})`)}if(e.endLabelRight){const n=$t.get(e.id).endRight;let o=e.x,s=e.y;if(r){const c=ce.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",r);o=c.x,s=c.y}n.attr("transform",`translate(${o}, ${s})`)}},"positionEdgeLabel"),tk=f((e,t)=>{const r=e.x,i=e.y,a=Math.abs(t.x-r),n=Math.abs(t.y-i),o=e.width/2,s=e.height/2;return a>=o||n>=s},"outsideNode"),ek=f((e,t,r)=>{$.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(r)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);const i=e.x,a=e.y,n=Math.abs(i-r.x),o=e.width/2;let s=r.x<t.x?o-n:o+n;const c=e.height/2,l=Math.abs(t.y-r.y),h=Math.abs(t.x-r.x);if(Math.abs(a-t.y)*o>Math.abs(i-t.x)*c){let u=r.y<t.y?t.y-c-a:a-c-t.y;s=h*u/l;const p={x:r.x<t.x?r.x+s:r.x-h+s,y:r.y<t.y?r.y+l-u:r.y-l+u};return s===0&&(p.x=t.x,p.y=t.y),h===0&&(p.x=t.x),l===0&&(p.y=t.y),$.debug(`abc89 top/bottom calc, Q ${l}, q ${u}, R ${h}, r ${s}`,p),p}else{r.x<t.x?s=t.x-o-i:s=i-o-t.x;let u=l*s/h,p=r.x<t.x?r.x+h-s:r.x-h+s,d=r.y<t.y?r.y+u:r.y-u;return $.debug(`sides calc abc89, Q ${l}, q ${u}, R ${h}, r ${s}`,{_x:p,_y:d}),s===0&&(p=t.x,d=t.y),h===0&&(p=t.x),l===0&&(d=t.y),{x:p,y:d}}},"intersection"),Dl=f((e,t)=>{$.warn("abc88 cutPathAtIntersect",e,t);let r=[],i=e[0],a=!1;return e.forEach(n=>{if($.info("abc88 checking point",n,t),!tk(t,n)&&!a){const o=ek(t,i,n);$.debug("abc88 inside",n,i,o),$.debug("abc88 intersection",o,t);let s=!1;r.forEach(c=>{s=s||c.x===o.x&&c.y===o.y}),r.some(c=>c.x===o.x&&c.y===o.y)?$.warn("abc88 no intersect",o,r):r.push(o),a=!0}else $.warn("abc88 outside",n,i),i=n,a||r.push(n)}),$.debug("returning points",r),r},"cutPathAtIntersect");function Wp(e){const t=[],r=[];for(let i=1;i<e.length-1;i++){const a=e[i-1],n=e[i],o=e[i+1];(a.x===n.x&&n.y===o.y&&Math.abs(n.x-o.x)>5&&Math.abs(n.y-a.y)>5||a.y===n.y&&n.x===o.x&&Math.abs(n.x-a.x)>5&&Math.abs(n.y-o.y)>5)&&(t.push(n),r.push(i))}return{cornerPoints:t,cornerPointPositions:r}}f(Wp,"extractCornerPoints");var Rl=f(function(e,t,r){const i=t.x-e.x,a=t.y-e.y,n=Math.sqrt(i*i+a*a),o=r/n;return{x:t.x-o*i,y:t.y-o*a}},"findAdjacentPoint"),rk=f(function(e){const{cornerPointPositions:t}=Wp(e),r=[];for(let i=0;i<e.length;i++)if(t.includes(i)){const a=e[i-1],n=e[i+1],o=e[i],s=Rl(a,o,5),c=Rl(n,o,5),l=c.x-s.x,h=c.y-s.y;r.push(s);const u=Math.sqrt(2)*2;let p={x:o.x,y:o.y};if(Math.abs(n.x-a.x)>10&&Math.abs(n.y-a.y)>=10){$.debug("Corner point fixing",Math.abs(n.x-a.x),Math.abs(n.y-a.y));const d=5;o.x===s.x?p={x:l<0?s.x-d+u:s.x+d-u,y:h<0?s.y-u:s.y+u}:p={x:l<0?s.x-u:s.x+u,y:h<0?s.y-d+u:s.y+d-u}}else $.debug("Corner point skipping fixing",Math.abs(n.x-a.x),Math.abs(n.y-a.y));r.push(p,c)}else r.push(e[i]);return r},"fixCorners"),ik=f(function(e,t,r,i,a,n,o){var E;const{handDrawnSeed:s}=ut();let c=t.points,l=!1;const h=a;var u=n;const p=[];for(const N in t.cssCompiledStyles)pu(N)||p.push(t.cssCompiledStyles[N]);u.intersect&&h.intersect&&(c=c.slice(1,t.points.length-1),c.unshift(h.intersect(c[0])),$.debug("Last point APA12",t.start,"-->",t.end,c[c.length-1],u,u.intersect(c[c.length-1])),c.push(u.intersect(c[c.length-1]))),t.toCluster&&($.info("to cluster abc88",r.get(t.toCluster)),c=Dl(t.points,r.get(t.toCluster).node),l=!0),t.fromCluster&&($.debug("from cluster abc88",r.get(t.fromCluster),JSON.stringify(c,null,2)),c=Dl(c.reverse(),r.get(t.fromCluster).node).reverse(),l=!0);let d=c.filter(N=>!Number.isNaN(N.y));d=rk(d);let g=Zi;switch(g=$n,t.curve){case"linear":g=$n;break;case"basis":g=Zi;break;case"cardinal":g=Zl;break;case"bumpX":g=Cu;break;case"bumpY":g=ku;break;case"catmullRom":g=Kl;break;case"monotoneX":g=Ql;break;case"monotoneY":g=Jl;break;case"natural":g=tc;break;case"step":g=ec;break;case"stepAfter":g=rc;break;case"stepBefore":g=ic;break;default:g=Zi}const{x:m,y}=Gy(t),x=ug().x(m).y(y).curve(g);let b;switch(t.thickness){case"normal":b="edge-thickness-normal";break;case"thick":b="edge-thickness-thick";break;case"invisible":b="edge-thickness-invisible";break;default:b="edge-thickness-normal"}switch(t.pattern){case"solid":b+=" edge-pattern-solid";break;case"dotted":b+=" edge-pattern-dotted";break;case"dashed":b+=" edge-pattern-dashed";break;default:b+=" edge-pattern-solid"}let k,T=x(d);const v=Array.isArray(t.style)?t.style:t.style?[t.style]:[];let C=v.find(N=>N==null?void 0:N.startsWith("stroke:"));if(t.look==="handDrawn"){const N=j.svg(e);Object.assign([],d);const D=N.path(T,{roughness:.3,seed:s});b+=" transition",k=ht(D).select("path").attr("id",t.id).attr("class"," "+b+(t.classes?" "+t.classes:"")).attr("style",v?v.reduce((A,B)=>A+";"+B,""):"");let L=k.attr("d");k.attr("d",L),e.node().appendChild(k.node())}else{const N=p.join(";"),D=v?v.reduce((B,F)=>B+F+";",""):"";let L="";t.animate&&(L=" edge-animation-fast"),t.animation&&(L=" edge-animation-"+t.animation);const A=N?N+";"+D+";":D;k=e.append("path").attr("d",T).attr("id",t.id).attr("class"," "+b+(t.classes?" "+t.classes:"")+(L??"")).attr("style",A),C=(E=A.match(/stroke:([^;]+)/))==null?void 0:E[1]}let S="";(ut().flowchart.arrowMarkerAbsolute||ut().state.arrowMarkerAbsolute)&&(S=Lc(!0)),$.info("arrowTypeStart",t.arrowTypeStart),$.info("arrowTypeEnd",t.arrowTypeEnd),Z2(k,t,S,o,i,C);const O=Math.floor(c.length/2),P=c[O];ce.isLabelCoordinateInPath(P,k.attr("d"))||(l=!0);let R={};return l&&(R.updatedPath=c),R.originalPath=t.points,R},"insertEdge"),ak=f((e,t,r,i)=>{t.forEach(a=>{Ck[a](e,r,i)})},"insertMarkers"),nk=f((e,t,r)=>{$.trace("Making markers for ",r),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),sk=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),ok=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),lk=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),ck=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),hk=f((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),uk=f((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),dk=f((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),pk=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","userSpaceOnUse").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),fk=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-onlyOneStart").attr("class","marker onlyOne "+t).attr("refX",0).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M9,0 L9,18 M15,0 L15,18"),e.append("defs").append("marker").attr("id",r+"_"+t+"-onlyOneEnd").attr("class","marker onlyOne "+t).attr("refX",18).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M3,0 L3,18 M9,0 L9,18")},"only_one"),gk=f((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrOneStart").attr("class","marker zeroOrOne "+t).attr("refX",0).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",21).attr("cy",9).attr("r",6),i.append("path").attr("d","M9,0 L9,18");const a=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrOneEnd").attr("class","marker zeroOrOne "+t).attr("refX",30).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");a.append("circle").attr("fill","white").attr("cx",9).attr("cy",9).attr("r",6),a.append("path").attr("d","M21,0 L21,18")},"zero_or_one"),mk=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-oneOrMoreStart").attr("class","marker oneOrMore "+t).attr("refX",18).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27"),e.append("defs").append("marker").attr("id",r+"_"+t+"-oneOrMoreEnd").attr("class","marker oneOrMore "+t).attr("refX",27).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18")},"one_or_more"),yk=f((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrMoreStart").attr("class","marker zeroOrMore "+t).attr("refX",18).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",48).attr("cy",18).attr("r",6),i.append("path").attr("d","M0,18 Q18,0 36,18 Q18,36 0,18");const a=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrMoreEnd").attr("class","marker zeroOrMore "+t).attr("refX",39).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");a.append("circle").attr("fill","white").attr("cx",9).attr("cy",18).attr("r",6),a.append("path").attr("d","M21,18 Q39,0 57,18 Q39,36 21,18")},"zero_or_more"),xk=f((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-requirement_arrowEnd").attr("refX",20).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("path").attr("d",`M0,0
      L20,10
      M20,10
      L0,20`)},"requirement_arrow"),bk=f((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-requirement_containsStart").attr("refX",0).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("g");i.append("circle").attr("cx",10).attr("cy",10).attr("r",9).attr("fill","none"),i.append("line").attr("x1",1).attr("x2",19).attr("y1",10).attr("y2",10),i.append("line").attr("y1",1).attr("y2",19).attr("x1",10).attr("x2",10)},"requirement_contains"),Ck={extension:nk,composition:sk,aggregation:ok,dependency:lk,lollipop:ck,point:hk,circle:uk,cross:dk,barb:pk,only_one:fk,zero_or_one:gk,one_or_more:mk,zero_or_more:yk,requirement_arrow:xk,requirement_contains:bk},kk=ak,wk={common:Fr,getConfig:Nt,insertCluster:E2,insertEdge:ik,insertEdgeLabel:Q2,insertMarkers:kk,insertNode:zp,interpolateToCurve:Gs,labelHelper:rt,log:$,positionEdgeLabel:J2},xi={},qp=f(e=>{for(const t of e)xi[t.name]=t},"registerLayoutLoaders"),vk=f(()=>{qp([{name:"dagre",loader:f(async()=>await gt(()=>import("./dagre-2BBEFEWP.3MZedGQZ.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url),"loader")}])},"registerDefaultLayoutLoaders");vk();var iT=f(async(e,t)=>{if(!(e.layoutAlgorithm in xi))throw new Error(`Unknown layout algorithm: ${e.layoutAlgorithm}`);const r=xi[e.layoutAlgorithm];return(await r.loader()).render(e,t,wk,{algorithm:r.algorithm})},"render"),aT=f((e="",{fallback:t="dagre"}={})=>{if(e in xi)return e;if(t in xi)return $.warn(`Layout algorithm ${e} is not registered. Using ${t} as fallback.`),t;throw new Error(`Both layout algorithms ${e} and ${t} are not registered.`)},"getRegisteredLayoutAlgorithm"),Il={name:"mermaid",version:"11.10.1",description:"Markdown-ish syntax for generating flowcharts, mindmaps, sequence diagrams, class diagrams, gantt charts, git graphs and more.",type:"module",module:"./dist/mermaid.core.mjs",types:"./dist/mermaid.d.ts",exports:{".":{types:"./dist/mermaid.d.ts",import:"./dist/mermaid.core.mjs",default:"./dist/mermaid.core.mjs"},"./*":"./*"},keywords:["diagram","markdown","flowchart","sequence diagram","gantt","class diagram","git graph","mindmap","packet diagram","c4 diagram","er diagram","pie chart","pie diagram","quadrant chart","requirement diagram","graph"],scripts:{clean:"rimraf dist",dev:"pnpm -w dev","docs:code":"typedoc src/defaultConfig.ts src/config.ts src/mermaid.ts && prettier --write ./src/docs/config/setup","docs:build":"rimraf ../../docs && pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts","docs:verify":"pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts --verify","docs:pre:vitepress":"pnpm --filter ./src/docs prefetch && rimraf src/vitepress && pnpm docs:code && tsx scripts/docs.cli.mts --vitepress && pnpm --filter ./src/vitepress install --no-frozen-lockfile --ignore-scripts","docs:build:vitepress":"pnpm docs:pre:vitepress && (cd src/vitepress && pnpm run build) && cpy --flat src/docs/landing/ ./src/vitepress/.vitepress/dist/landing","docs:dev":'pnpm docs:pre:vitepress && concurrently "pnpm --filter ./src/vitepress dev" "tsx scripts/docs.cli.mts --watch --vitepress"',"docs:dev:docker":'pnpm docs:pre:vitepress && concurrently "pnpm --filter ./src/vitepress dev:docker" "tsx scripts/docs.cli.mts --watch --vitepress"',"docs:serve":"pnpm docs:build:vitepress && vitepress serve src/vitepress","docs:spellcheck":'cspell "src/docs/**/*.md"',"docs:release-version":"tsx scripts/update-release-version.mts","docs:verify-version":"tsx scripts/update-release-version.mts --verify","types:build-config":"tsx scripts/create-types-from-json-schema.mts","types:verify-config":"tsx scripts/create-types-from-json-schema.mts --verify",checkCircle:"npx madge --circular ./src",prepublishOnly:"pnpm docs:verify-version"},repository:{type:"git",url:"https://github.com/mermaid-js/mermaid"},author:"Knut Sveidqvist",license:"MIT",standard:{ignore:["**/parser/*.js","dist/**/*.js","cypress/**/*.js"],globals:["page"]},dependencies:{"@braintree/sanitize-url":"^7.0.4","@iconify/utils":"^2.1.33","@mermaid-js/parser":"workspace:^","@types/d3":"^7.4.3",cytoscape:"^3.29.3","cytoscape-cose-bilkent":"^4.1.0","cytoscape-fcose":"^2.2.0",d3:"^7.9.0","d3-sankey":"^0.12.3","dagre-d3-es":"7.0.11",dayjs:"^1.11.13",dompurify:"^3.2.5",katex:"^0.16.22",khroma:"^2.1.0","lodash-es":"^4.17.21",marked:"^16.0.0",roughjs:"^4.6.6",stylis:"^4.3.6","ts-dedent":"^2.2.0",uuid:"^11.1.0"},devDependencies:{"@adobe/jsonschema2md":"^8.0.2","@iconify/types":"^2.0.0","@types/cytoscape":"^3.21.9","@types/cytoscape-fcose":"^2.2.4","@types/d3-sankey":"^0.12.4","@types/d3-scale":"^4.0.9","@types/d3-scale-chromatic":"^3.1.0","@types/d3-selection":"^3.0.11","@types/d3-shape":"^3.1.7","@types/jsdom":"^21.1.7","@types/katex":"^0.16.7","@types/lodash-es":"^4.17.12","@types/micromatch":"^4.0.9","@types/stylis":"^4.2.7","@types/uuid":"^10.0.0",ajv:"^8.17.1",canvas:"^3.1.0",chokidar:"3.6.0",concurrently:"^9.1.2","csstree-validator":"^4.0.1",globby:"^14.0.2",jison:"^0.4.18","js-base64":"^3.7.7",jsdom:"^26.1.0","json-schema-to-typescript":"^15.0.4",micromatch:"^4.0.8","path-browserify":"^1.0.1",prettier:"^3.5.2",remark:"^15.0.1","remark-frontmatter":"^5.0.0","remark-gfm":"^4.0.1",rimraf:"^6.0.1","start-server-and-test":"^2.0.10","type-fest":"^4.35.0",typedoc:"^0.27.8","typedoc-plugin-markdown":"^4.4.2",typescript:"~5.7.3","unist-util-flatmap":"^1.0.0","unist-util-visit":"^5.0.0",vitepress:"^1.0.2","vitepress-plugin-search":"1.0.4-alpha.22"},files:["dist/","README.md"],publishConfig:{access:"public"}},Sk=f(e=>{var a;const{securityLevel:t}=ut();let r=ht("body");if(t==="sandbox"){const o=((a=ht(`#i${e}`).node())==null?void 0:a.contentDocument)??document;r=ht(o.body)}return r.select(`#${e}`)},"selectSvgElement"),Hp="comm",jp="rule",Yp="decl",Tk="@import",_k="@namespace",Bk="@keyframes",Lk="@layer",Up=Math.abs,po=String.fromCharCode;function Gp(e){return e.trim()}function oa(e,t,r){return e.replace(t,r)}function Ak(e,t,r){return e.indexOf(t,r)}function wr(e,t){return e.charCodeAt(t)|0}function Ar(e,t,r){return e.slice(t,r)}function xe(e){return e.length}function Mk(e){return e.length}function Xi(e,t){return t.push(e),e}var rn=1,Mr=1,Vp=0,se=0,_t=0,Ir="";function fo(e,t,r,i,a,n,o,s){return{value:e,root:t,parent:r,type:i,props:a,children:n,line:rn,column:Mr,length:o,return:"",siblings:s}}function Ek(){return _t}function Fk(){return _t=se>0?wr(Ir,--se):0,Mr--,_t===10&&(Mr=1,rn--),_t}function ue(){return _t=se<Vp?wr(Ir,se++):0,Mr++,_t===10&&(Mr=1,rn++),_t}function Ne(){return wr(Ir,se)}function la(){return se}function an(e,t){return Ar(Ir,e,t)}function bi(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function $k(e){return rn=Mr=1,Vp=xe(Ir=e),se=0,[]}function Ok(e){return Ir="",e}function Mn(e){return Gp(an(se-1,ms(e===91?e+2:e===40?e+1:e)))}function Dk(e){for(;(_t=Ne())&&_t<33;)ue();return bi(e)>2||bi(_t)>3?"":" "}function Rk(e,t){for(;--t&&ue()&&!(_t<48||_t>102||_t>57&&_t<65||_t>70&&_t<97););return an(e,la()+(t<6&&Ne()==32&&ue()==32))}function ms(e){for(;ue();)switch(_t){case e:return se;case 34:case 39:e!==34&&e!==39&&ms(_t);break;case 40:e===41&&ms(e);break;case 92:ue();break}return se}function Ik(e,t){for(;ue()&&e+_t!==57;)if(e+_t===84&&Ne()===47)break;return"/*"+an(t,se-1)+"*"+po(e===47?e:ue())}function Pk(e){for(;!bi(Ne());)ue();return an(e,se)}function Nk(e){return Ok(ca("",null,null,null,[""],e=$k(e),0,[0],e))}function ca(e,t,r,i,a,n,o,s,c){for(var l=0,h=0,u=o,p=0,d=0,g=0,m=1,y=1,x=1,b=0,k="",T=a,v=n,C=i,S=k;y;)switch(g=b,b=ue()){case 40:if(g!=108&&wr(S,u-1)==58){Ak(S+=oa(Mn(b),"&","&\f"),"&\f",Up(l?s[l-1]:0))!=-1&&(x=-1);break}case 34:case 39:case 91:S+=Mn(b);break;case 9:case 10:case 13:case 32:S+=Dk(g);break;case 92:S+=Rk(la()-1,7);continue;case 47:switch(Ne()){case 42:case 47:Xi(zk(Ik(ue(),la()),t,r,c),c),(bi(g||1)==5||bi(Ne()||1)==5)&&xe(S)&&Ar(S,-1,void 0)!==" "&&(S+=" ");break;default:S+="/"}break;case 123*m:s[l++]=xe(S)*x;case 125*m:case 59:case 0:switch(b){case 0:case 125:y=0;case 59+h:x==-1&&(S=oa(S,/\f/g,"")),d>0&&(xe(S)-u||m===0&&g===47)&&Xi(d>32?Nl(S+";",i,r,u-1,c):Nl(oa(S," ","")+";",i,r,u-2,c),c);break;case 59:S+=";";default:if(Xi(C=Pl(S,t,r,l,h,a,s,k,T=[],v=[],u,n),n),b===123)if(h===0)ca(S,t,C,C,T,n,u,s,v);else{switch(p){case 99:if(wr(S,3)===110)break;case 108:if(wr(S,2)===97)break;default:h=0;case 100:case 109:case 115:}h?ca(e,C,C,i&&Xi(Pl(e,C,C,0,0,a,s,k,a,T=[],u,v),v),a,v,u,s,i?T:v):ca(S,C,C,C,[""],v,0,s,v)}}l=h=d=0,m=x=1,k=S="",u=o;break;case 58:u=1+xe(S),d=g;default:if(m<1){if(b==123)--m;else if(b==125&&m++==0&&Fk()==125)continue}switch(S+=po(b),b*m){case 38:x=h>0?1:(S+="\f",-1);break;case 44:s[l++]=(xe(S)-1)*x,x=1;break;case 64:Ne()===45&&(S+=Mn(ue())),p=Ne(),h=u=xe(k=S+=Pk(la())),b++;break;case 45:g===45&&xe(S)==2&&(m=0)}}return n}function Pl(e,t,r,i,a,n,o,s,c,l,h,u){for(var p=a-1,d=a===0?n:[""],g=Mk(d),m=0,y=0,x=0;m<i;++m)for(var b=0,k=Ar(e,p+1,p=Up(y=o[m])),T=e;b<g;++b)(T=Gp(y>0?d[b]+" "+k:oa(k,/&\f/g,d[b])))&&(c[x++]=T);return fo(e,t,r,a===0?jp:s,c,l,h,u)}function zk(e,t,r,i){return fo(e,t,r,Hp,po(Ek()),Ar(e,2,-2),0,i)}function Nl(e,t,r,i,a){return fo(e,t,r,Yp,Ar(e,0,i),Ar(e,i+1,-1),i,a)}function ys(e,t){for(var r="",i=0;i<e.length;i++)r+=t(e[i],i,e,t)||"";return r}function Wk(e,t,r,i){switch(e.type){case Lk:if(e.children.length)break;case Tk:case _k:case Yp:return e.return=e.return||e.value;case Hp:return"";case Bk:return e.return=e.value+"{"+ys(e.children,i)+"}";case jp:if(!xe(e.value=e.props.join(",")))return""}return xe(r=ys(e.children,i))?e.return=e.value+"{"+r+"}":""}var qk=Tu(Object.keys,Object),Hk=Object.prototype,jk=Hk.hasOwnProperty;function Yk(e){if(!Ua(e))return qk(e);var t=[];for(var r in Object(e))jk.call(e,r)&&r!="constructor"&&t.push(r);return t}var xs=lr(we,"DataView"),bs=lr(we,"Promise"),Cs=lr(we,"Set"),ks=lr(we,"WeakMap"),zl="[object Map]",Uk="[object Object]",Wl="[object Promise]",ql="[object Set]",Hl="[object WeakMap]",jl="[object DataView]",Gk=or(xs),Vk=or(mi),Xk=or(bs),Zk=or(Cs),Kk=or(ks),Ze=Or;(xs&&Ze(new xs(new ArrayBuffer(1)))!=jl||mi&&Ze(new mi)!=zl||bs&&Ze(bs.resolve())!=Wl||Cs&&Ze(new Cs)!=ql||ks&&Ze(new ks)!=Hl)&&(Ze=function(e){var t=Or(e),r=t==Uk?e.constructor:void 0,i=r?or(r):"";if(i)switch(i){case Gk:return jl;case Vk:return zl;case Xk:return Wl;case Zk:return ql;case Kk:return Hl}return t});var Qk="[object Map]",Jk="[object Set]",tw=Object.prototype,ew=tw.hasOwnProperty;function Yl(e){if(e==null)return!0;if(Ga(e)&&(va(e)||typeof e=="string"||typeof e.splice=="function"||Ys(e)||Us(e)||wa(e)))return!e.length;var t=Ze(e);if(t==Qk||t==Jk)return!e.size;if(Ua(e))return!Yk(e).length;for(var r in e)if(ew.call(e,r))return!1;return!0}var Xp="c4",rw=f(e=>/^\s*C4Context|C4Container|C4Component|C4Dynamic|C4Deployment/.test(e),"detector"),iw=f(async()=>{const{diagram:e}=await gt(()=>import("./c4Diagram-AAMF2YG6.DrmRm8LB.js"),__vite__mapDeps([6,7,8]),import.meta.url);return{id:Xp,diagram:e}},"loader"),aw={id:Xp,detector:rw,loader:iw},nw=aw,Zp="flowchart",sw=f((e,t)=>{var r,i;return((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="dagre-wrapper"||((i=t==null?void 0:t.flowchart)==null?void 0:i.defaultRenderer)==="elk"?!1:/^\s*graph/.test(e)},"detector"),ow=f(async()=>{const{diagram:e}=await gt(()=>import("./flowDiagram-THRYKUMA.D5X5he_2.js"),__vite__mapDeps([9,10,11,8,12,13]),import.meta.url);return{id:Zp,diagram:e}},"loader"),lw={id:Zp,detector:sw,loader:ow},cw=lw,Kp="flowchart-v2",hw=f((e,t)=>{var r,i,a;return((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="dagre-d3"?!1:(((i=t==null?void 0:t.flowchart)==null?void 0:i.defaultRenderer)==="elk"&&(t.layout="elk"),/^\s*graph/.test(e)&&((a=t==null?void 0:t.flowchart)==null?void 0:a.defaultRenderer)==="dagre-wrapper"?!0:/^\s*flowchart/.test(e))},"detector"),uw=f(async()=>{const{diagram:e}=await gt(()=>import("./flowDiagram-THRYKUMA.D5X5he_2.js"),__vite__mapDeps([9,10,11,8,12,13]),import.meta.url);return{id:Kp,diagram:e}},"loader"),dw={id:Kp,detector:hw,loader:uw},pw=dw,Qp="er",fw=f(e=>/^\s*erDiagram/.test(e),"detector"),gw=f(async()=>{const{diagram:e}=await gt(()=>import("./erDiagram-HZWUO2LU.BWBQDz7-.js"),__vite__mapDeps([14,11,8,12,13]),import.meta.url);return{id:Qp,diagram:e}},"loader"),mw={id:Qp,detector:fw,loader:gw},yw=mw,Jp="gitGraph",xw=f(e=>/^\s*gitGraph/.test(e),"detector"),bw=f(async()=>{const{diagram:e}=await gt(()=>import("./gitGraphDiagram-OJR772UL.BZy94xP4.js"),__vite__mapDeps([15,16,17,18,19,2,4,5,8]),import.meta.url);return{id:Jp,diagram:e}},"loader"),Cw={id:Jp,detector:xw,loader:bw},kw=Cw,tf="gantt",ww=f(e=>/^\s*gantt/.test(e),"detector"),vw=f(async()=>{const{diagram:e}=await gt(()=>import("./ganttDiagram-WV7ZQ7D5.C2yIMhNo.js"),__vite__mapDeps([20,21,19,22,23,24,25,26,27,28,29,8]),import.meta.url);return{id:tf,diagram:e}},"loader"),Sw={id:tf,detector:ww,loader:vw},Tw=Sw,ef="info",_w=f(e=>/^\s*info/.test(e),"detector"),Bw=f(async()=>{const{diagram:e}=await gt(()=>import("./infoDiagram-DDUCL6P7.DGHj4NNp.js"),__vite__mapDeps([30,18,19,2,4,5]),import.meta.url);return{id:ef,diagram:e}},"loader"),Lw={id:ef,detector:_w,loader:Bw},rf="pie",Aw=f(e=>/^\s*pie/.test(e),"detector"),Mw=f(async()=>{const{diagram:e}=await gt(()=>import("./pieDiagram-DBDJKBY4.-Uqa3ON4.js"),__vite__mapDeps([31,16,18,19,2,4,5,32,26,33,28]),import.meta.url);return{id:rf,diagram:e}},"loader"),Ew={id:rf,detector:Aw,loader:Mw},af="quadrantChart",Fw=f(e=>/^\s*quadrantChart/.test(e),"detector"),$w=f(async()=>{const{diagram:e}=await gt(()=>import("./quadrantDiagram-YPSRARAO.BLBqfq2h.js"),__vite__mapDeps([34,27,26,28,29,8]),import.meta.url);return{id:af,diagram:e}},"loader"),Ow={id:af,detector:Fw,loader:$w},Dw=Ow,nf="xychart",Rw=f(e=>/^\s*xychart(-beta)?/.test(e),"detector"),Iw=f(async()=>{const{diagram:e}=await gt(()=>import("./xychartDiagram-FDP5SA34.DvZIcumM.js"),__vite__mapDeps([35,28,33,36,27,26,29]),import.meta.url);return{id:nf,diagram:e}},"loader"),Pw={id:nf,detector:Rw,loader:Iw},Nw=Pw,sf="requirement",zw=f(e=>/^\s*requirement(Diagram)?/.test(e),"detector"),Ww=f(async()=>{const{diagram:e}=await gt(()=>import("./requirementDiagram-EGVEC5DT.Dq51eLEV.js"),__vite__mapDeps([37,11,8,12]),import.meta.url);return{id:sf,diagram:e}},"loader"),qw={id:sf,detector:zw,loader:Ww},Hw=qw,of="sequence",jw=f(e=>/^\s*sequenceDiagram/.test(e),"detector"),Yw=f(async()=>{const{diagram:e}=await gt(()=>import("./sequenceDiagram-4MX5Z3NR.CsIWAQna.js"),__vite__mapDeps([38,7,17,8]),import.meta.url);return{id:of,diagram:e}},"loader"),Uw={id:of,detector:jw,loader:Yw},Gw=Uw,lf="class",Vw=f((e,t)=>{var r;return((r=t==null?void 0:t.class)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!1:/^\s*classDiagram/.test(e)},"detector"),Xw=f(async()=>{const{diagram:e}=await gt(()=>import("./classDiagram-3BZAVTQC.f1uA9w0N.js"),__vite__mapDeps([39,40,10,11,8,12]),import.meta.url);return{id:lf,diagram:e}},"loader"),Zw={id:lf,detector:Vw,loader:Xw},Kw=Zw,cf="classDiagram",Qw=f((e,t)=>{var r;return/^\s*classDiagram/.test(e)&&((r=t==null?void 0:t.class)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!0:/^\s*classDiagram-v2/.test(e)},"detector"),Jw=f(async()=>{const{diagram:e}=await gt(()=>import("./classDiagram-v2-QTMF73CY.f1uA9w0N.js"),__vite__mapDeps([41,40,10,11,8,12]),import.meta.url);return{id:cf,diagram:e}},"loader"),tv={id:cf,detector:Qw,loader:Jw},ev=tv,hf="state",rv=f((e,t)=>{var r;return((r=t==null?void 0:t.state)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!1:/^\s*stateDiagram/.test(e)},"detector"),iv=f(async()=>{const{diagram:e}=await gt(()=>import("./stateDiagram-UUKSUZ4H.Ba--bB5h.js"),__vite__mapDeps([42,43,11,8,12,1,2,3,4,26]),import.meta.url);return{id:hf,diagram:e}},"loader"),av={id:hf,detector:rv,loader:iv},nv=av,uf="stateDiagram",sv=f((e,t)=>{var r;return!!(/^\s*stateDiagram-v2/.test(e)||/^\s*stateDiagram/.test(e)&&((r=t==null?void 0:t.state)==null?void 0:r.defaultRenderer)==="dagre-wrapper")},"detector"),ov=f(async()=>{const{diagram:e}=await gt(()=>import("./stateDiagram-v2-EYPG3UTE.CrCkSxPR.js"),__vite__mapDeps([44,43,11,8,12]),import.meta.url);return{id:uf,diagram:e}},"loader"),lv={id:uf,detector:sv,loader:ov},cv=lv,df="journey",hv=f(e=>/^\s*journey/.test(e),"detector"),uv=f(async()=>{const{diagram:e}=await gt(()=>import("./journeyDiagram-FFXJYRFH.B_vaNNIM.js"),__vite__mapDeps([45,7,10,8,32,26]),import.meta.url);return{id:df,diagram:e}},"loader"),dv={id:df,detector:hv,loader:uv},pv=dv,fv=f((e,t,r)=>{$.debug(`rendering svg for syntax error
`);const i=Sk(t),a=i.append("g");i.attr("viewBox","0 0 2412 512"),Ac(i,100,512,!0),a.append("path").attr("class","error-icon").attr("d","m411.313,123.313c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32-9.375,9.375-20.688-20.688c-12.484-12.5-32.766-12.5-45.25,0l-16,16c-1.261,1.261-2.304,2.648-3.31,4.051-21.739-8.561-45.324-13.426-70.065-13.426-105.867,0-192,86.133-192,192s86.133,192 192,192 192-86.133 192-192c0-24.741-4.864-48.327-13.426-70.065 1.402-1.007 2.79-2.049 4.051-3.31l16-16c12.5-12.492 12.5-32.758 0-45.25l-20.688-20.688 9.375-9.375 32.001-31.999zm-219.313,100.687c-52.938,0-96,43.063-96,96 0,8.836-7.164,16-16,16s-16-7.164-16-16c0-70.578 57.***********-128 8.836,0 16,7.164 16,16s-7.164,16-16,16z"),a.append("path").attr("class","error-icon").attr("d","m459.02,148.98c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l16,16c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16.001-16z"),a.append("path").attr("class","error-icon").attr("d","m340.395,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16-16c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l15.999,16z"),a.append("path").attr("class","error-icon").attr("d","m400,64c8.844,0 16-7.164 16-16v-32c0-8.836-7.156-16-16-16-8.844,0-16,7.164-16,16v32c0,8.836 7.156,16 16,16z"),a.append("path").attr("class","error-icon").attr("d","m496,96.586h-32c-8.844,0-16,7.164-16,16 0,8.836 7.156,16 16,16h32c8.844,0 16-7.164 16-16 0-8.836-7.156-16-16-16z"),a.append("path").attr("class","error-icon").attr("d","m436.98,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688l32-32c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32c-6.251,6.25-6.251,16.375-0.001,22.625z"),a.append("text").attr("class","error-text").attr("x",1440).attr("y",250).attr("font-size","150px").style("text-anchor","middle").text("Syntax error in text"),a.append("text").attr("class","error-text").attr("x",1250).attr("y",400).attr("font-size","100px").style("text-anchor","middle").text(`mermaid version ${r}`)},"draw"),pf={draw:fv},gv=pf,mv={db:{},renderer:pf,parser:{parse:f(()=>{},"parse")}},yv=mv,ff="flowchart-elk",xv=f((e,t={})=>{var r;return/^\s*flowchart-elk/.test(e)||/^\s*(flowchart|graph)/.test(e)&&((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="elk"?(t.layout="elk",!0):!1},"detector"),bv=f(async()=>{const{diagram:e}=await gt(()=>import("./flowDiagram-THRYKUMA.D5X5he_2.js"),__vite__mapDeps([9,10,11,8,12,13]),import.meta.url);return{id:ff,diagram:e}},"loader"),Cv={id:ff,detector:xv,loader:bv},kv=Cv,gf="timeline",wv=f(e=>/^\s*timeline/.test(e),"detector"),vv=f(async()=>{const{diagram:e}=await gt(()=>import("./timeline-definition-3HZDQTIS.CjCLpVxn.js"),__vite__mapDeps([46,8,32,26]),import.meta.url);return{id:gf,diagram:e}},"loader"),Sv={id:gf,detector:wv,loader:vv},Tv=Sv,mf="mindmap",_v=f(e=>/^\s*mindmap/.test(e),"detector"),Bv=f(async()=>{const{diagram:e}=await gt(()=>import("./mindmap-definition-LNHGMQRG.DKpqsxIq.js"),__vite__mapDeps([47,48,21,19,22,23,24,8]),import.meta.url);return{id:mf,diagram:e}},"loader"),Lv={id:mf,detector:_v,loader:Bv},Av=Lv,yf="kanban",Mv=f(e=>/^\s*kanban/.test(e),"detector"),Ev=f(async()=>{const{diagram:e}=await gt(()=>import("./kanban-definition-KOZQBZVT.DhBeYgt7.js"),__vite__mapDeps([49,10]),import.meta.url);return{id:yf,diagram:e}},"loader"),Fv={id:yf,detector:Mv,loader:Ev},$v=Fv,xf="sankey",Ov=f(e=>/^\s*sankey(-beta)?/.test(e),"detector"),Dv=f(async()=>{const{diagram:e}=await gt(()=>import("./sankeyDiagram-HRAUVNP4.CpXlZRsE.js"),__vite__mapDeps([50,33,28,8]),import.meta.url);return{id:xf,diagram:e}},"loader"),Rv={id:xf,detector:Ov,loader:Dv},Iv=Rv,bf="packet",Pv=f(e=>/^\s*packet(-beta)?/.test(e),"detector"),Nv=f(async()=>{const{diagram:e}=await gt(()=>import("./diagram-GUPCWM2R.BdHaD9a5.js"),__vite__mapDeps([51,16,18,19,2,4,5]),import.meta.url);return{id:bf,diagram:e}},"loader"),zv={id:bf,detector:Pv,loader:Nv},Cf="radar",Wv=f(e=>/^\s*radar-beta/.test(e),"detector"),qv=f(async()=>{const{diagram:e}=await gt(()=>import("./diagram-RP2FKANI.DGSAwjmZ.js"),__vite__mapDeps([52,16,18,19,2,4,5]),import.meta.url);return{id:Cf,diagram:e}},"loader"),Hv={id:Cf,detector:Wv,loader:qv},kf="block",jv=f(e=>/^\s*block(-beta)?/.test(e),"detector"),Yv=f(async()=>{const{diagram:e}=await gt(()=>import("./blockDiagram-ZYB65J3Q.CUyZilll.js"),__vite__mapDeps([53,10,5,2,1,13,8,26]),import.meta.url);return{id:kf,diagram:e}},"loader"),Uv={id:kf,detector:jv,loader:Yv},Gv=Uv,wf="architecture",Vv=f(e=>/^\s*architecture/.test(e),"detector"),Xv=f(async()=>{const{diagram:e}=await gt(()=>import("./architectureDiagram-KFL7JDKH.Cg89XiJ9.js"),__vite__mapDeps([54,16,18,19,2,4,5,48,21,22,23,24,8]),import.meta.url);return{id:wf,diagram:e}},"loader"),Zv={id:wf,detector:Vv,loader:Xv},Kv=Zv,vf="treemap",Qv=f(e=>/^\s*treemap/.test(e),"detector"),Jv=f(async()=>{const{diagram:e}=await gt(()=>import("./diagram-4IRLE6MV.BtZ0ObP7.js"),__vite__mapDeps([55,12,16,18,19,2,4,5,29,33,28,56,8]),import.meta.url);return{id:vf,diagram:e}},"loader"),tS={id:vf,detector:Qv,loader:Jv},Ul=!1,nn=f(()=>{Ul||(Ul=!0,pa("error",yv,e=>e.toLowerCase().trim()==="error"),pa("---",{db:{clear:f(()=>{},"clear")},styles:{},renderer:{draw:f(()=>{},"draw")},parser:{parse:f(()=>{throw new Error("Diagrams beginning with --- are not valid. If you were trying to use a YAML front-matter, please ensure that you've correctly opened and closed the YAML front-matter with un-indented `---` blocks")},"parse")},init:f(()=>null,"init")},e=>e.toLowerCase().trimStart().startsWith("---")),Rn(kv,Av,Kv),Rn(nw,$v,ev,Kw,yw,Tw,Lw,Ew,Hw,Gw,pw,cw,Tv,kw,cv,nv,pv,Dw,Iv,zv,Nw,Gv,Hv,tS))},"addDiagrams"),eS=f(async()=>{$.debug("Loading registered diagrams");const t=(await Promise.allSettled(Object.entries(er).map(async([r,{detector:i,loader:a}])=>{if(a)try{zn(r)}catch{try{const{diagram:n,id:o}=await a();pa(o,n,i)}catch(n){throw $.error(`Failed to load external diagram with key ${r}. Removing from detectors.`),delete er[r],n}}}))).filter(r=>r.status==="rejected");if(t.length>0){$.error(`Failed to load ${t.length} external diagrams`);for(const r of t)$.error(r);throw new Error(`Failed to load ${t.length} external diagrams`)}},"loadRegisteredDiagrams"),rS="graphics-document document";function Sf(e,t){e.attr("role",rS),t!==""&&e.attr("aria-roledescription",t)}f(Sf,"setA11yDiagramInfo");function Tf(e,t,r,i){if(e.insert!==void 0){if(r){const a=`chart-desc-${i}`;e.attr("aria-describedby",a),e.insert("desc",":first-child").attr("id",a).text(r)}if(t){const a=`chart-title-${i}`;e.attr("aria-labelledby",a),e.insert("title",":first-child").attr("id",a).text(t)}}}f(Tf,"addSVGa11yTitleDescription");var Er,ws=(Er=class{constructor(t,r,i,a,n){this.type=t,this.text=r,this.db=i,this.parser=a,this.renderer=n}static async fromText(t,r={}){var l,h;const i=Nt(),a=Ts(t,i);t=tC(t)+`
`;try{zn(a)}catch{const u=Gg(a);if(!u)throw new gc(`Diagram ${a} not found.`);const{id:p,diagram:d}=await u();pa(p,d)}const{db:n,parser:o,renderer:s,init:c}=zn(a);return o.parser&&(o.parser.yy=n),(l=n.clear)==null||l.call(n),c==null||c(i),r.title&&((h=n.setDiagramTitle)==null||h.call(n,r.title)),await o.parse(t),new Er(a,t,n,o,s)}async render(t,r){await this.renderer.draw(this.text,t,r,this)}getParser(){return this.parser}getType(){return this.type}},f(Er,"Diagram"),Er),Gl=[],iS=f(()=>{Gl.forEach(e=>{e()}),Gl=[]},"attachFunctions"),aS=f(e=>e.replace(/^\s*%%(?!{)[^\n]+\n?/gm,"").trimStart(),"cleanupComments");function _f(e){const t=e.match(fc);if(!t)return{text:e,metadata:{}};let r=Uy(t[1],{schema:Yy})??{};r=typeof r=="object"&&!Array.isArray(r)?r:{};const i={};return r.displayMode&&(i.displayMode=r.displayMode.toString()),r.title&&(i.title=r.title.toString()),r.config&&(i.config=r.config),{text:e.slice(t[0].length),metadata:i}}f(_f,"extractFrontMatter");var nS=f(e=>e.replace(/\r\n?/g,`
`).replace(/<(\w+)([^>]*)>/g,(t,r,i)=>"<"+r+i.replace(/="([^"]*)"/g,"='$1'")+">"),"cleanupText"),sS=f(e=>{const{text:t,metadata:r}=_f(e),{displayMode:i,title:a,config:n={}}=r;return i&&(n.gantt||(n.gantt={}),n.gantt.displayMode=i),{title:a,config:n,text:t}},"processFrontmatter"),oS=f(e=>{const t=ce.detectInit(e)??{},r=ce.detectDirective(e,"wrap");return Array.isArray(r)?t.wrap=r.some(({type:i})=>i==="wrap"):(r==null?void 0:r.type)==="wrap"&&(t.wrap=!0),{text:W1(e),directive:t}},"processDirectives");function go(e){const t=nS(e),r=sS(t),i=oS(r.text),a=Qs(r.config,i.directive);return e=aS(i.text),{code:e,title:r.title,config:a}}f(go,"preprocessDiagram");function Bf(e){const t=new TextEncoder().encode(e),r=Array.from(t,i=>String.fromCodePoint(i)).join("");return btoa(r)}f(Bf,"toBase64");var lS=5e4,cS="graph TB;a[Maximum text size in diagram exceeded];style a fill:#faa",hS="sandbox",uS="loose",dS="http://www.w3.org/2000/svg",pS="http://www.w3.org/1999/xlink",fS="http://www.w3.org/1999/xhtml",gS="100%",mS="100%",yS="border:0;margin:0;",xS="margin:0",bS="allow-top-navigation-by-user-activation allow-popups",CS='The "iframe" tag is not supported by your browser.',kS=["foreignobject"],wS=["dominant-baseline"];function mo(e){const t=go(e);return ua(),cm(t.config??{}),t}f(mo,"processAndSetConfigs");async function Lf(e,t){nn();try{const{code:r,config:i}=mo(e);return{diagramType:(await Mf(r)).type,config:i}}catch(r){if(t!=null&&t.suppressErrors)return!1;throw r}}f(Lf,"parse");var Vl=f((e,t,r=[])=>`
.${e} ${t} { ${r.join(" !important; ")} !important; }`,"cssImportantStyles"),vS=f((e,t=new Map)=>{var i;let r="";if(e.themeCSS!==void 0&&(r+=`
${e.themeCSS}`),e.fontFamily!==void 0&&(r+=`
:root { --mermaid-font-family: ${e.fontFamily}}`),e.altFontFamily!==void 0&&(r+=`
:root { --mermaid-alt-font-family: ${e.altFontFamily}}`),t instanceof Map){const s=e.htmlLabels??((i=e.flowchart)==null?void 0:i.htmlLabels)?["> *","span"]:["rect","polygon","ellipse","circle","path"];t.forEach(c=>{Yl(c.styles)||s.forEach(l=>{r+=Vl(c.id,l,c.styles)}),Yl(c.textStyles)||(r+=Vl(c.id,"tspan",((c==null?void 0:c.textStyles)||[]).map(l=>l.replace("color","fill"))))})}return r},"createCssStyles"),SS=f((e,t,r,i)=>{const a=vS(e,r),n=Lm(t,a,e.themeVariables);return ys(Nk(`${i}{${n}}`),Wk)},"createUserStyles"),TS=f((e="",t,r)=>{let i=e;return!r&&!t&&(i=i.replace(/marker-end="url\([\d+./:=?A-Za-z-]*?#/g,'marker-end="url(#')),i=cr(i),i=i.replace(/<br>/g,"<br/>"),i},"cleanUpSvgCode"),_S=f((e="",t)=>{var a,n;const r=(n=(a=t==null?void 0:t.viewBox)==null?void 0:a.baseVal)!=null&&n.height?t.viewBox.baseVal.height+"px":mS,i=Bf(`<body style="${xS}">${e}</body>`);return`<iframe style="width:${gS};height:${r};${yS}" src="data:text/html;charset=UTF-8;base64,${i}" sandbox="${bS}">
  ${CS}
</iframe>`},"putIntoIFrame"),Xl=f((e,t,r,i,a)=>{const n=e.append("div");n.attr("id",r),i&&n.attr("style",i);const o=n.append("svg").attr("id",t).attr("width","100%").attr("xmlns",dS);return a&&o.attr("xmlns:xlink",a),o.append("g"),e},"appendDivSvgG");function vs(e,t){return e.append("iframe").attr("id",t).attr("style","width: 100%; height: 100%;").attr("sandbox","")}f(vs,"sandboxedIframe");var BS=f((e,t,r,i)=>{var a,n,o;(a=e.getElementById(t))==null||a.remove(),(n=e.getElementById(r))==null||n.remove(),(o=e.getElementById(i))==null||o.remove()},"removeExistingElements"),LS=f(async function(e,t,r){var N,D,L,A,B,F;nn();const i=mo(t);t=i.code;const a=Nt();$.debug(a),t.length>((a==null?void 0:a.maxTextSize)??lS)&&(t=cS);const n="#"+e,o="i"+e,s="#"+o,c="d"+e,l="#"+c,h=f(()=>{const W=ht(p?s:l).node();W&&"remove"in W&&W.remove()},"removeTempElements");let u=ht("body");const p=a.securityLevel===hS,d=a.securityLevel===uS,g=a.fontFamily;if(r!==void 0){if(r&&(r.innerHTML=""),p){const M=vs(ht(r),o);u=ht(M.nodes()[0].contentDocument.body),u.node().style.margin=0}else u=ht(r);Xl(u,e,c,`font-family: ${g}`,pS)}else{if(BS(document,e,c,o),p){const M=vs(ht("body"),o);u=ht(M.nodes()[0].contentDocument.body),u.node().style.margin=0}else u=ht("body");Xl(u,e,c)}let m,y;try{m=await ws.fromText(t,{title:i.title})}catch(M){if(a.suppressErrorRendering)throw h(),M;m=await ws.fromText("error"),y=M}const x=u.select(l).node(),b=m.type,k=x.firstChild,T=k.firstChild,v=(D=(N=m.renderer).getClasses)==null?void 0:D.call(N,t,m),C=SS(a,b,v,n),S=document.createElement("style");S.innerHTML=C,k.insertBefore(S,T);try{await m.renderer.draw(t,e,Il.version,m)}catch(M){throw a.suppressErrorRendering?h():gv.draw(t,e,Il.version),M}const O=u.select(`${l} svg`),P=(A=(L=m.db).getAccTitle)==null?void 0:A.call(L),R=(F=(B=m.db).getAccDescription)==null?void 0:F.call(B);Ef(b,O,P,R),u.select(`[id="${e}"]`).selectAll("foreignobject > *").attr("xmlns",fS);let E=u.select(l).node().innerHTML;if($.debug("config.arrowMarkerAbsolute",a.arrowMarkerAbsolute),E=TS(E,p,At(a.arrowMarkerAbsolute)),p){const M=u.select(l+" svg").node();E=_S(E,M)}else d||(E=vr.sanitize(E,{ADD_TAGS:kS,ADD_ATTR:wS,HTML_INTEGRATION_POINTS:{foreignobject:!0}}));if(iS(),y)throw y;return h(),{diagramType:b,svg:E,bindFunctions:m.db.bindFunctions}},"render");function Af(e={}){var i;const t=Ot({},e);t!=null&&t.fontFamily&&!((i=t.themeVariables)!=null&&i.fontFamily)&&(t.themeVariables||(t.themeVariables={}),t.themeVariables.fontFamily=t.fontFamily),om(t),t!=null&&t.theme&&t.theme in $e?t.themeVariables=$e[t.theme].getThemeVariables(t.themeVariables):t&&(t.themeVariables=$e.default.getThemeVariables(t.themeVariables));const r=typeof t=="object"?sm(t):kc();Ss(r.logLevel),nn()}f(Af,"initialize");var Mf=f((e,t={})=>{const{code:r}=go(e);return ws.fromText(r,t)},"getDiagramFromText");function Ef(e,t,r,i){Sf(t,e),Tf(t,r,i,t.attr("id"))}f(Ef,"addA11yInfo");var nr=Object.freeze({render:LS,parse:Lf,getDiagramFromText:Mf,initialize:Af,getConfig:Nt,setConfig:wc,getSiteConfig:kc,updateSiteConfig:lm,reset:f(()=>{ua()},"reset"),globalReset:f(()=>{ua(Sr)},"globalReset"),defaultConfig:Sr});Ss(Nt().logLevel);ua(Nt());var AS=f((e,t,r)=>{$.warn(e),Ks(e)?(r&&r(e.str,e.hash),t.push({...e,message:e.str,error:e})):(r&&r(e),e instanceof Error&&t.push({str:e.message,message:e.message,hash:e.name,error:e}))},"handleError"),Ff=f(async function(e={querySelector:".mermaid"}){try{await MS(e)}catch(t){if(Ks(t)&&$.error(t.str),ee.parseError&&ee.parseError(t),!e.suppressErrors)throw $.error("Use the suppressErrors option to suppress these errors"),t}},"run"),MS=f(async function({postRenderCallback:e,querySelector:t,nodes:r}={querySelector:".mermaid"}){const i=nr.getConfig();$.debug(`${e?"":"No "}Callback function found`);let a;if(r)a=r;else if(t)a=document.querySelectorAll(t);else throw new Error("Nodes and querySelector are both undefined");$.debug(`Found ${a.length} diagrams`),(i==null?void 0:i.startOnLoad)!==void 0&&($.debug("Start On Load: "+(i==null?void 0:i.startOnLoad)),nr.updateSiteConfig({startOnLoad:i==null?void 0:i.startOnLoad}));const n=new ce.InitIDGenerator(i.deterministicIds,i.deterministicIDSeed);let o;const s=[];for(const c of Array.from(a)){if($.info("Rendering diagram: "+c.id),c.getAttribute("data-processed"))continue;c.setAttribute("data-processed","true");const l=`mermaid-${n.next()}`;o=c.innerHTML,o=id(ce.entityDecode(o)).trim().replace(/<br\s*\/?>/gi,"<br/>");const h=ce.detectInit(o);h&&$.debug("Detected early reinit: ",h);try{const{svg:u,bindFunctions:p}=await Rf(l,o,c);c.innerHTML=u,e&&await e(l),p&&p(c)}catch(u){AS(u,s,ee.parseError)}}if(s.length>0)throw s[0]},"runThrowsErrors"),$f=f(function(e){nr.initialize(e)},"initialize"),ES=f(async function(e,t,r){$.warn("mermaid.init is deprecated. Please use run instead."),e&&$f(e);const i={postRenderCallback:r,querySelector:".mermaid"};typeof t=="string"?i.querySelector=t:t&&(t instanceof HTMLElement?i.nodes=[t]:i.nodes=t),await Ff(i)},"init"),FS=f(async(e,{lazyLoad:t=!0}={})=>{nn(),Rn(...e),t===!1&&await eS()},"registerExternalDiagrams"),Of=f(function(){if(ee.startOnLoad){const{startOnLoad:e}=nr.getConfig();e&&ee.run().catch(t=>$.error("Mermaid failed to initialize",t))}},"contentLoaded");typeof document<"u"&&window.addEventListener("load",Of,!1);var $S=f(function(e){ee.parseError=e},"setParseErrorHandler"),Oa=[],En=!1,Df=f(async()=>{if(!En){for(En=!0;Oa.length>0;){const e=Oa.shift();if(e)try{await e()}catch(t){$.error("Error executing queue",t)}}En=!1}},"executeQueue"),OS=f(async(e,t)=>new Promise((r,i)=>{const a=f(()=>new Promise((n,o)=>{nr.parse(e,t).then(s=>{n(s),r(s)},s=>{var c;$.error("Error parsing",s),(c=ee.parseError)==null||c.call(ee,s),o(s),i(s)})}),"performCall");Oa.push(a),Df().catch(i)}),"parse"),Rf=f((e,t,r)=>new Promise((i,a)=>{const n=f(()=>new Promise((o,s)=>{nr.render(e,t,r).then(c=>{o(c),i(c)},c=>{var l;$.error("Error parsing",c),(l=ee.parseError)==null||l.call(ee,c),s(c),a(c)})}),"performCall");Oa.push(n),Df().catch(a)}),"render"),DS=f(()=>Object.keys(er).map(e=>({id:e})),"getRegisteredDiagramsMetadata"),ee={startOnLoad:!0,mermaidAPI:nr,parse:OS,render:Rf,init:ES,run:Ff,registerExternalDiagrams:FS,registerLayoutLoaders:qp,initialize:$f,parseError:void 0,contentLoaded:Of,setParseErrorHandler:$S,detectType:Ts,registerIconPacks:a2,getRegisteredDiagramsMetadata:DS},RS=ee;/*! Check if previously processed *//*!
 * Wait for document loaded before starting the execution
 */const nT=Object.freeze(Object.defineProperty({__proto__:null,default:RS},Symbol.toStringTag,{value:"Module"}));export{E2 as $,ni as A,Yg as B,be as C,bc as D,Qs as E,Nt as F,U1 as G,Im as H,Sk as I,Yy as J,Il as K,Jg as L,_r as M,HS as N,Lc as O,_s as P,Va as Q,Vo as R,Y1 as S,Mc as T,it as U,Tm as V,Li as W,q as X,J as Y,I1 as Z,f as _,Ot as a,je as a$,zp as a0,eT as a1,Wu as a2,At as a3,Ye as a4,Ns as a5,Gy as a6,gd as a7,cr as a8,pu as a9,Du as aA,Fu as aB,h1 as aC,F1 as aD,T1 as aE,db as aF,js as aG,s1 as aH,R1 as aI,Fi as aJ,Or as aK,Ca as aL,y1 as aM,Yk as aN,Ei as aO,wa as aP,u1 as aQ,_u as aR,gb as aS,mb as aT,Ze as aU,pl as aV,yb as aW,Ys as aX,fb as aY,Cb as aZ,Dr as a_,Z as aa,s2 as ab,$1 as ac,Tb as ad,_1 as ae,Hs as af,Yl as ag,kk as ah,tT as ai,rT as aj,QS as ak,X as al,JS as am,ik as an,J2 as ao,Q2 as ap,i2 as aq,a2 as ar,Oi as as,mg as at,sr as au,O1 as av,$u as aw,Ha as ax,Ga as ay,va as az,$m as b,ll as b0,Us as b1,Lu as b2,Cs as b3,D1 as b4,Ua as b5,nT as b6,Fm as c,ut as d,Fr as e,Hu as f,Em as g,Re as h,ne as i,Ac as j,fu as k,$ as l,Ai as m,Om as n,Dm as o,Uy as p,X2 as q,US as r,Mm as s,Am as t,ce as u,YS as v,X1 as w,jS as x,aT as y,iT as z};
//# sourceMappingURL=mermaid.core.B6NEFrXU.js.map
